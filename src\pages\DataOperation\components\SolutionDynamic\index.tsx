import React from 'react';
import './index.less';

const hotResult = [
  {
    id: 1,
    title: '汇智灵曦携新产品亮相WAIC2025 世界人工智能大会！',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '河南豫健数智科技有限公司',
    direction: 'AI医疗',
    username: '<PERSON>',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 1200,
    collectionCount: 300,
    img: '/platform/images/data-operation/solution-dynamic-1.png',
    link: 'http://www.aethermind.cn/frontend/',
    date: '2025-7-26',
  },
  {
    id: 2,
    title: '汇智灵曦重磅推出AI大模型平台！',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '河南豫健数智科技有限公司',
    direction: 'AI医疗',
    username: '<PERSON>',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 1200,
    collectionCount: 300,
    img: '/platform/images/data-operation/solution-dynamic-2.png',
    link: 'http://wenshu.aethermind.cn/welcome',
    date: '2025-7-26',
  },
  {
    id: 3,
    title: '深度问数：用AI解锁医疗数据价值，让决 策“一问即达”',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '河南豫健数智科技有限公司',
    direction: 'AI医疗',
    username: 'Arthur',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 1200,
    collectionCount: 300,
    img: '/platform/images/data-operation/solution-dynamic-3.png',
    link: 'http://kyan.aethermind.cn/#/login',
    date: '2025-7-26',
  },
];

const SolutionDynamic: React.FC = () => {
  const handleItemClick = (link: string) => {
    window.open(link, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className='solution-dynamic'>
      <div className='hot-result-container'>
        <div className='hot-result-title'>
          <h2>行业动态</h2>
        </div>
        <div className='hot-result-content'>
          {hotResult.map(item => (
            <div
              className={`hot-result-item ${item.id === 2 ? 'active' : ''}`}
              key={item.id}
              onClick={() => handleItemClick(item.link)}
            >
              <div className='hot-result-item-img'>
                <img src={item.img} alt='' />
              </div>
              <div className='hot-result-item-info'>
                <div className='hot-result-item-info-container'>
                  <h3>{item.title}</h3>
                  <p className='hot-result-item-info-company'>
                    <span>{item.company}</span>
                    <span>{item.direction}</span>
                  </p>
                  <p className='hot-result-item-info-content'>{item.content}</p>
                </div>
                <div className='hot-result-item-footer'>
                  <div className='hot-result-item-footer-left'>
                    <span>{item.date}</span>
                  </div>
                  <div className='hot-result-item-footer-right'>
                    <div>
                      <img
                        src='/platform/images/research-operations/hot-result-view.svg'
                        alt='view'
                      />
                      <span>{item.viewCount}</span>
                    </div>
                    <div>
                      <img
                        src='/platform/images/research-operations/hot-result-collection.svg'
                        alt='collection'
                      />
                      <span>{item.collectionCount}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SolutionDynamic;
