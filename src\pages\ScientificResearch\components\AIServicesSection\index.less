.aiServicesSection {
  --active-color: #3377ff;
  --inactive-color: rgba(0, 0, 0, 0.45);
  
  padding: 52px 0;
  background: #fff;
  // background: #eef5ff;

  .sectionContainer {
    max-width: 1240px;
    width: 1240px;
    margin: 0 auto;
  }

  .sectionTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    margin-bottom: 32px;
  }

  .servicesContent {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .servicesList {
    display: flex;
    justify-content: center;
    gap: 24px;
    cursor: pointer;
  }

  .serviceItem {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 20px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.25);
    border-radius: 40px;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    height: 48px;
    color: var(--inactive-color);
    transition: all 0.3s ease;

    &.active {
      border-color: #74aeff;
      .serviceTitle {
        color: var(--active-color);
      }
    }
  }

  .serviceIcon {
    width: 24px;
    height: 24px;
    transition: filter 0.3s ease;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .serviceTitle {
    color: var(--inactive-color);
    font-family: 'Alibaba PuHuiTi';
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 30px */
    transition: color 0.3s ease;
  }

  .assistantPreview {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    overflow: hidden;

    .medicalExperiment {
      width: 100%;
      height: 783px;
      .medicalExperimentImg {
        width: 100%;
        height: 100%;
      }
    }

    .intelligentHealthcare {
      width: 100%;
      height: 783px;
      padding: 52px 51px;
      background: url(/platform/images/scientific-research/ai-service-intelligent-healthcare.png)
        no-repeat center center;
      .intelligentHealthcareContainer {
        width: 100%;
        height: 100%;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.3);
        padding: 29px 25px 62px 34px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        gap: 26px;
        .intelligentHealthcareTitle {
          > h3 {
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: 'Alibaba PuHuiTi';
            font-size: 35px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 52.5px */
            margin: 0;
            margin-bottom: 4px;
            > span {
              color: var(---Brand1-5, #37f);
            }
          }
          > p {
            margin: 0;
            color: var(---45, rgba(0, 0, 0, 0.45));
            font-family: 'PingFang SC';
            font-size: 20px;
            font-style: normal;
            font-weight: 300;
            line-height: normal;
          }
        }
        .intelligentHealthcareContent {
          flex: 1;
          display: flex;
          justify-content: space-between;
          gap: 25px;
          .contentLeft {
            width: 260px;
            display: flex;
            flex-direction: column;
            gap: 40px;
            .contentLeftItem {
              width: 100%;
              height: 82px;
              line-height: 82px;
              border-radius: 12px;
              border: 1px solid #fff;
              box-shadow: 0 4px 4px 0 rgba(25, 62, 111, 0.16);
              color: var(---Brand1-5, #37f);
              text-align: center;
              font-family: 'Alibaba PuHuiTi';
              font-size: 28px;
              font-style: normal;
              font-weight: 600;
              background: url(/platform/images/scientific-research/ai-service-intelligent-healthcare-default.png)
                no-repeat center center;
              background-size: 100% 100%;
              cursor: pointer;
              position: relative;
              overflow: hidden;
              > img {
                position: absolute;
                width: 79px;
                height: 79px;
                bottom: -9px;
                right: -8px;
              }
            }
            .active {
              width: 100%;
              height: 82px;
              line-height: 82px;
              border-radius: 12px;
              border: 1px solid #c8dcff;
              background: var(---Brand1-5, #37f);
              box-shadow: 0 4px 4px 0 rgba(25, 62, 111, 0.16);
              color: var(---, #fff);
              text-align: center;
              font-family: 'Alibaba PuHuiTi';
              font-size: 28px;
              font-style: normal;
              font-weight: 600;
            }
          }
          .contentRight {
            flex: 1;
            height: 100%;
            border-radius: 12px;
            .ant-image {
              width: 100%;
              height: 100%;
            }
            .contentRightImg {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }

  .assistantHeader {
    position: relative;
    width: 100%;
    height: 200px;
    background: #e9edf6;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .assistantBg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .assistantInfo {
    width: 100%;
    position: relative;
    z-index: 2;
    margin-left: 240px;
  }

  .assistantName {
    font-family: 'Alibaba PuHuiTi';
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 60px */
    background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
  }

  .assistantDesc {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    margin: 0;
  }

  .assistantFeatures {
    background: #dfe5ef;
    display: flex;
    height: 583px;
  }

  .assistantFeaturesPages {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    justify-content: center;
    .assistantFeaturesPagesItem {
      width: 16px;
      height: 16px;
    }
  }

  .featuresLeft {
    position: relative;
    height: 100%;
    width: 281px;
    min-width: 281px;
    display: flex;
    justify-content: center;
    background: linear-gradient(180deg, #f0f3f9 0%, #d6deea 100%);
    overflow: hidden;
  }

  .featuresTitle {
    color: var(---85, rgba(0, 0, 0, 0.85));
    text-align: center;
    font-family: 'Alibaba PuHuiTi';
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 60px */
    margin: 0;
    margin-top: 122px;

    p {
      margin: 0;
    }
  }

  .highText {
    color: var(---Brand1-5, #37f);
    font-family: 'Alibaba PuHuiTi';
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
  }

  .featuresBg {
    position: absolute;
    bottom: 0;
    right: -90px;
    width: 487.912px;
    height: 273.445px;
    flex-shrink: 0;
    aspect-ratio: 91/51;
    background: url(/platform/images/scientific-research/ai-left-banner2.png) lightgray 50% / cover
      no-repeat;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .featuresGrid {
    height: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 24px;
    gap: 20px;
    display: grid;
    flex: 1;
    padding: 24px;
    row-gap: 20px;
    column-gap: 20px;
    flex-shrink: 0;
    align-self: stretch;
  }

  .featureCard {
    border-radius: 12px;
    background: #fff;
    display: flex;
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .featureImage {
    width: 100%;
    height: 143px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(---08, rgba(0, 0, 0, 0.08));

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .featureContent {
    padding: 6px 12px;

    h5 {
      color: var(---85, rgba(0, 0, 0, 0.85));
      font-family: 'Alibaba PuHuiTi';
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 27px */
      margin: 0;
    }

    p {
      color: var(---45, rgba(0, 0, 0, 0.45));
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      margin: 0px;
    }
  }

  .assistantActions {
    padding: 16px;
    display: flex;
    justify-content: center;
  }

  .actionBtn {
    display: flex;
    width: 182px;
    height: 48px;
    padding: 11px 56px 10px 55px;
    justify-content: center;
    align-items: center;
    border-radius: 41px;
    border: 1px solid var(---logo, #2560fd);
    background: linear-gradient(180deg, #fff 0%, #ebf5fb 100%);
    box-shadow: 0 4px 4px 0 rgba(17, 62, 91, 0.18);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0px 6px 8px 0px rgba(17, 62, 91, 0.25);
    }
  }
}
