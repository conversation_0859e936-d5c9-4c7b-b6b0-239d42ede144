import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Input,
  Select,
  Modal,
  Form,
  Statistic,
  Progress,
  Avatar,
  Rate,
  Badge,
  Steps,
  List,
  Divider,
} from 'antd';
import {
  ProjectOutlined,
  BookOutlined,
  TrophyOutlined,
  UserOutlined,
  PlusOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import './ProjectsCoursesPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Step } = Steps;

const ProjectsCoursesPage: React.FC = () => {
  const [isProjectModalVisible, setIsProjectModalVisible] = useState(false);
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 统计数据
  const stats = [
    { title: '在研项目', value: 456, prefix: <ProjectOutlined />, color: '#1890ff' },
    { title: '培训课程', value: 128, prefix: <BookOutlined />, color: '#52c41a' },
    { title: '获得认证', value: 2847, prefix: <TrophyOutlined />, color: '#fa8c16' },
    { title: '参与人员', value: 15623, prefix: <UserOutlined />, color: '#722ed1' },
  ];

  // 项目数据
  const projectData = [
    {
      key: '1',
      name: '精准医疗诊断系统研发',
      level: '国家重点研发计划',
      leader: '张院士',
      organization: '河南大学医学院',
      budget: '2800万元',
      duration: '36个月',
      startDate: '2023-01-15',
      endDate: '2025-12-31',
      progress: 75,
      status: 'ongoing',
      participants: 25,
      milestones: 8,
      domain: '精准医疗',
      description: '基于基因组学和蛋白质组学的精准医疗诊断技术，提高疾病诊断精度和个性化治疗效果',
    },
    {
      key: '2',
      name: '生物医学材料产业化',
      level: '省重点科技项目',
      leader: '李教授',
      organization: '郑州大学医工学院',
      budget: '1500万元',
      duration: '24个月',
      startDate: '2023-03-01',
      endDate: '2025-02-28',
      progress: 60,
      status: 'ongoing',
      participants: 18,
      milestones: 6,
      domain: '生物材料',
      description: '开发新型生物医学材料，包括人工骨、血管支架等医疗器械核心材料',
    },
    {
      key: '3',
      name: 'AI肿瘤影像诊断系统',
      level: '企业委托项目',
      leader: '王主任',
      organization: '河南省人民医院',
      budget: '800万元',
      duration: '18个月',
      startDate: '2023-06-01',
      endDate: '2024-11-30',
      progress: 85,
      status: 'ongoing',
      participants: 12,
      milestones: 5,
      domain: '医学影像',
      description: '基于深度学习的肿瘤影像智能诊断系统，提高早期癌症检出率和诊断准确性',
    },
    {
      key: '4',
      name: '远程医疗平台建设',
      level: '市科技计划',
      leader: '赵博士',
      organization: '河南中医药大学',
      budget: '500万元',
      duration: '24个月',
      startDate: '2022-09-01',
      endDate: '2024-08-31',
      progress: 95,
      status: 'completed',
      participants: 15,
      milestones: 4,
      domain: '远程医疗',
      description: '构建覆盖城乡的远程医疗服务平台，提高医疗资源配置效率和基层医疗服务能力',
    },
  ];

  // 课程数据
  const courseData = [
    {
      key: '1',
      title: '医学影像AI诊断技术',
      instructor: '张院士',
      organization: '河南大学医学院',
      level: '高级',
      duration: '40学时',
      format: '线上+线下',
      participants: 156,
      rating: 4.9,
      price: '3200元',
      startDate: '2024-02-15',
      category: '医学影像',
      skills: ['深度学习', '医学图像处理', 'CNN架构', '病灶检测'],
      description: '深入学习医学影像AI诊断原理与实践，掌握主流医学AI框架的使用',
      status: 'upcoming',
    },
    {
      key: '2',
      title: '生物医学材料与器械',
      instructor: '李教授',
      organization: '郑州大学医工学院',
      level: '中级',
      duration: '32学时',
      format: '线下实验',
      participants: 89,
      rating: 4.8,
      price: '2800元',
      startDate: '2024-01-20',
      category: '生物材料',
      skills: ['生物相容性', '材料表征', '植入物设计', '临床应用'],
      description: '全面了解生物医学材料研发流程，掌握医疗器械关键技术',
      status: 'ongoing',
    },
    {
      key: '3',
      title: '精准医疗与基因诊断',
      instructor: '王主任',
      organization: '河南省人民医院',
      level: '高级',
      duration: '48学时',
      format: '混合式',
      participants: 67,
      rating: 4.7,
      price: '3800元',
      startDate: '2024-03-01',
      category: '精准医疗',
      skills: ['基因测序', '生物信息学', '药物基因组学', '个性化治疗'],
      description: '掌握现代精准医疗核心技术，了解基因诊断发展趋势',
      status: 'upcoming',
    },
  ];

  // 认证数据
  const certificationData = [
    {
      name: '医学影像AI工程师认证',
      level: '高级',
      issuer: '中国医学影像AI协会',
      validity: '3年',
      requirements: ['完成医学影像AI课程', '通过理论考试', '完成临床诊断项目'],
      benefits: ['医疗行业认可', '职业提升', '薪资优势'],
      holders: 234,
    },
    {
      name: '生物医学工程师认证',
      level: '专家级',
      issuer: '中国生物医学工程学会',
      validity: '5年',
      requirements: ['相关学位', '5年临床经验', '发表研究论文'],
      benefits: ['专家地位', '项目主导', '技术咨询'],
      holders: 156,
    },
    {
      name: '精准医疗数据分析师认证',
      level: '中级',
      issuer: '精准医疗协会',
      validity: '3年',
      requirements: ['相关工作经验', '培训课程', '案例分析'],
      benefits: ['数据分析能力', '团队协作', '项目成功率'],
      holders: 345,
    },
  ];

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <Tag color='blue'>{record.level}</Tag>
        </div>
      ),
    },
    {
      title: '项目负责人',
      dataIndex: 'leader',
      key: 'leader',
      width: 150,
      render: (text: string, record: any) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.organization}</div>
        </div>
      ),
    },
    {
      title: '领域',
      dataIndex: 'domain',
      key: 'domain',
      width: 100,
      render: (domain: string) => <Tag color='green'>{domain}</Tag>,
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      width: 100,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 200,
      render: (progress: number, record: any) => (
        <div>
          <Progress
            percent={progress}
            size='small'
            status={record.status === 'completed' ? 'success' : 'active'}
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {record.participants} 人参与 | {record.milestones} 个里程碑
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          ongoing: { color: 'processing', text: '进行中' },
          completed: { color: 'success', text: '已完成' },
          pending: { color: 'warning', text: '待启动' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Badge status={statusInfo.color as any} text={statusInfo.text} />;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: () => (
        <div>
          <Button type='link' icon={<EyeOutlined />} size='small'>
            详情
          </Button>
          <Button type='link' size='small'>
            申请加入
          </Button>
        </div>
      ),
    },
  ];

  const courseColumns = [
    {
      title: '课程信息',
      dataIndex: 'title',
      key: 'title',
      width: 240,
      render: (text: string, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.instructor} • {record.organization}
          </div>
          <div style={{ marginTop: 4 }}>
            <Tag color='orange'>{record.level}</Tag>
            <Tag color='blue'>{record.category}</Tag>
          </div>
        </div>
      ),
    },
    {
      title: '课程详情',
      key: 'details',
      width: 180,
      render: (_: any, record: any) => (
        <div>
          <div>
            <ClockCircleOutlined /> {record.duration}
          </div>
          <div style={{ marginTop: 4 }}>
            <TeamOutlined /> {record.participants} 人报名
          </div>
          <div style={{ marginTop: 4 }}>
            <Rate disabled defaultValue={record.rating} style={{ fontSize: '12px' }} />
            <span style={{ marginLeft: 8, fontSize: '12px' }}>({record.rating})</span>
          </div>
        </div>
      ),
    },
    {
      title: '开课时间',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 140,
      render: (date: string, record: any) => (
        <div>
          <div>{date}</div>
          <Badge
            status={
              record.status === 'ongoing'
                ? 'processing'
                : record.status === 'upcoming'
                  ? 'warning'
                  : 'success'
            }
            text={
              record.status === 'ongoing'
                ? '进行中'
                : record.status === 'upcoming'
                  ? '即将开始'
                  : '已结束'
            }
          />
        </div>
      ),
    },
    {
      title: '费用',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price: string) => (
        <span style={{ color: '#f5222d', fontWeight: 'bold' }}>{price}</span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_: any, record: any) => (
        <div>
          <Button type='primary' size='small' disabled={record.status === 'completed'}>
            {record.status === 'ongoing' ? '立即学习' : '立即报名'}
          </Button>
          <Button type='link' icon={<EyeOutlined />} size='small'>
            详情
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className='projects-courses-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>项目与课程管理</h1>
            <p>整合科研项目资源，提供专业培训课程，构建完整的人才培养体系</p>
            <div className='header-actions'>
              <Button
                type='primary'
                size='large'
                icon={<PlusOutlined />}
                onClick={() => setIsProjectModalVisible(true)}
              >
                申请项目
              </Button>
              <Button
                size='large'
                icon={<BookOutlined />}
                onClick={() => setIsCourseModalVisible(true)}
              >
                申请开课
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            {stats.map((stat, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className='stat-card'>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    prefix={stat.prefix}
                    valueStyle={{ color: stat.color }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 主要内容标签页 */}
      <section className='content-section'>
        <div className='container'>
          <Card>
            <Tabs defaultActiveKey='projects' size='large'>
              {/* 科研项目 */}
              <TabPane
                tab={
                  <span>
                    <ProjectOutlined />
                    科研项目
                  </span>
                }
                key='projects'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索项目名称或负责人'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索项目:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 150 }}>
                      <Option value='all'>全部项目类型</Option>
                      <Option value='national'>国家重点项目</Option>
                      <Option value='provincial'>省市项目</Option>
                      <Option value='enterprise'>企业委托</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部状态</Option>
                      <Option value='ongoing'>进行中</Option>
                      <Option value='completed'>已完成</Option>
                    </Select>
                  </div>
                </div>
                <Table
                  dataSource={projectData}
                  columns={projectColumns}
                  pagination={{ pageSize: 8 }}
                  scroll={{ x: 1200 }}
                />
              </TabPane>

              {/* 培训课程 */}
              <TabPane
                tab={
                  <span>
                    <BookOutlined />
                    培训课程
                  </span>
                }
                key='courses'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索课程名称或讲师'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索课程:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类别</Option>
                      <Option value='medical-imaging'>医学影像</Option>
                      <Option value='biomaterials'>生物材料</Option>
                      <Option value='precision-medicine'>精准医疗</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部级别</Option>
                      <Option value='basic'>初级</Option>
                      <Option value='intermediate'>中级</Option>
                      <Option value='advanced'>高级</Option>
                    </Select>
                  </div>
                </div>
                <Table
                  dataSource={courseData}
                  columns={courseColumns}
                  pagination={{ pageSize: 6 }}
                  scroll={{ x: 1000 }}
                />
              </TabPane>

              {/* 技能认证 */}
              <TabPane
                tab={
                  <span>
                    <TrophyOutlined />
                    技能认证
                  </span>
                }
                key='certification'
              >
                <Row gutter={[16, 16]}>
                  {certificationData.map((cert, index) => (
                    <Col xs={24} lg={8} key={index}>
                      <Card
                        title={
                          <div>
                            <TrophyOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                            {cert.name}
                          </div>
                        }
                        extra={<Tag color='gold'>{cert.level}</Tag>}
                      >
                        <div className='cert-info'>
                          <p>
                            <strong>认证机构:</strong> {cert.issuer}
                          </p>
                          <p>
                            <strong>有效期:</strong> {cert.validity}
                          </p>
                          <p>
                            <strong>持证人数:</strong> {cert.holders} 人
                          </p>

                          <Divider orientation='left' style={{ fontSize: '14px' }}>
                            认证要求
                          </Divider>
                          <ul style={{ paddingLeft: 20, margin: 0 }}>
                            {cert.requirements.map((req, i) => (
                              <li key={i} style={{ fontSize: '12px', color: '#666' }}>
                                {req}
                              </li>
                            ))}
                          </ul>

                          <Divider orientation='left' style={{ fontSize: '14px' }}>
                            认证价值
                          </Divider>
                          <div>
                            {cert.benefits.map((benefit, i) => (
                              <Tag key={i} color='blue' style={{ marginBottom: 4 }}>
                                {benefit}
                              </Tag>
                            ))}
                          </div>

                          <div style={{ marginTop: 16 }}>
                            <Button type='primary' size='small' block>
                              申请认证
                            </Button>
                          </div>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>

              {/* 学习路径 */}
              <TabPane
                tab={
                  <span>
                    <CalendarOutlined />
                    学习路径
                  </span>
                }
                key='learning-path'
              >
                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={12}>
                    <Card title='医学影像AI工程师成长路径' className='learning-path-card'>
                      <Steps direction='vertical' size='small'>
                        <Step
                          status='finish'
                          title='医学基础知识学习'
                          description='解剖学、病理学、影像学基础'
                          icon={<CheckCircleOutlined />}
                        />
                        <Step
                          status='process'
                          title='AI技术实践'
                          description='深度学习、医学图像处理、CNN架构'
                          icon={<PlayCircleOutlined />}
                        />
                        <Step
                          status='wait'
                          title='临床项目实战'
                          description='完成真实诊断项目、团队协作、解决临床问题'
                        />
                        <Step
                          status='wait'
                          title='认证考试'
                          description='通过医学影像AI工程师认证考试，获得专业资质'
                        />
                      </Steps>
                      <div style={{ marginTop: 16 }}>
                        <Button type='primary' size='small'>
                          开始学习
                        </Button>
                        <Button type='link' size='small'>
                          查看详情
                        </Button>
                      </div>
                    </Card>
                  </Col>

                  <Col xs={24} lg={12}>
                    <Card title='精准医疗专家培养计划' className='learning-path-card'>
                      <Steps direction='vertical' size='small'>
                        <Step
                          status='finish'
                          title='生物医学基础'
                          description='基因组学、蛋白质组学、分子生物学'
                          icon={<CheckCircleOutlined />}
                        />
                        <Step
                          status='finish'
                          title='数据分析技能'
                          description='生物信息学、统计分析、数据挖掘'
                          icon={<CheckCircleOutlined />}
                        />
                        <Step
                          status='process'
                          title='精准诊断技术'
                          description='基因测序、药物基因组学、个性化治疗'
                          icon={<PlayCircleOutlined />}
                        />
                        <Step
                          status='wait'
                          title='临床转化应用'
                          description='临床研究设计、转化医学、产业应用'
                        />
                      </Steps>
                      <div style={{ marginTop: 16 }}>
                        <Button type='primary' size='small'>
                          继续学习
                        </Button>
                        <Button type='link' size='small'>
                          查看详情
                        </Button>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {/* 推荐课程 */}
                <Card title='推荐课程' style={{ marginTop: 24 }}>
                  <List
                    itemLayout='horizontal'
                    dataSource={courseData.slice(0, 3)}
                    renderItem={item => (
                      <List.Item
                        actions={[
                          <Button key='enroll' type='primary' size='small'>
                            立即报名
                          </Button>,
                          <Button key='detail' type='link' size='small'>
                            查看详情
                          </Button>,
                        ]}
                      >
                        <List.Item.Meta
                          avatar={<Avatar icon={<BookOutlined />} />}
                          title={
                            <div>
                              {item.title}
                              <Tag color='orange' style={{ marginLeft: 8 }}>
                                {item.level}
                              </Tag>
                            </div>
                          }
                          description={
                            <div>
                              <div>{item.description}</div>
                              <div style={{ marginTop: 4 }}>
                                <span style={{ color: '#666' }}>
                                  {item.instructor} • {item.duration} •{' '}
                                </span>
                                <span style={{ color: '#f5222d', fontWeight: 'bold' }}>
                                  {item.price}
                                </span>
                              </div>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </TabPane>
            </Tabs>
          </Card>
        </div>
      </section>

      {/* 项目申请模态框 */}
      <Modal
        title='申请科研项目'
        visible={isProjectModalVisible}
        onOk={() => setIsProjectModalVisible(false)}
        onCancel={() => setIsProjectModalVisible(false)}
        width={700}
      >
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='项目名称' name='projectName' rules={[{ required: true }]}>
                <Input placeholder='请输入项目名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='项目级别' name='level' rules={[{ required: true }]}>
                <Select placeholder='请选择项目级别'>
                  <Option value='national'>国家重点研发计划</Option>
                  <Option value='provincial'>省市科技计划</Option>
                  <Option value='enterprise'>企业委托项目</Option>
                  <Option value='internal'>内部研发项目</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='项目负责人' name='leader' rules={[{ required: true }]}>
                <Input placeholder='请输入项目负责人' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='技术领域' name='domain' rules={[{ required: true }]}>
                <Select placeholder='请选择技术领域'>
                  <Option value='precision-medicine'>精准医疗</Option>
                  <Option value='medical-imaging'>医学影像</Option>
                  <Option value='biomaterials'>生物医学材料</Option>
                  <Option value='telemedicine'>远程医疗</Option>
                  <Option value='medical-devices'>医疗器械</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='项目简介' name='description' rules={[{ required: true }]}>
            <Input.TextArea rows={4} placeholder='请简要描述项目背景、目标和预期成果' />
          </Form.Item>
        </Form>
      </Modal>

      {/* 课程申请模态框 */}
      <Modal
        title='申请开设课程'
        visible={isCourseModalVisible}
        onOk={() => setIsCourseModalVisible(false)}
        onCancel={() => setIsCourseModalVisible(false)}
        width={700}
      >
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='课程名称' name='courseName' rules={[{ required: true }]}>
                <Input placeholder='请输入课程名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='课程类别' name='category' rules={[{ required: true }]}>
                <Select placeholder='请选择课程类别'>
                  <Option value='medical-imaging'>医学影像</Option>
                  <Option value='biomaterials'>生物医学材料</Option>
                  <Option value='precision-medicine'>精准医疗</Option>
                  <Option value='medical-management'>医疗管理</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='授课讲师' name='instructor' rules={[{ required: true }]}>
                <Input placeholder='请输入授课讲师' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='课程级别' name='level' rules={[{ required: true }]}>
                <Select placeholder='请选择课程级别'>
                  <Option value='basic'>初级</Option>
                  <Option value='intermediate'>中级</Option>
                  <Option value='advanced'>高级</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='课程大纲' name='outline' rules={[{ required: true }]}>
            <Input.TextArea rows={4} placeholder='请详细描述课程内容、学习目标和教学安排' />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectsCoursesPage;
