.dashboard-right {
    display: flex;
    flex-direction: column;

    .panel-card {
        padding: 20px;
        margin: 10px;
        // background: #011B31;
        // border-radius: 12px;
        // border: 1px solid rgba(0, 212, 255, 0.2);

        .card-header {
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: -20px -20px 16px;
            padding: 12px 16px;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 70, 120, 0.65) 50%, transparent 100%);
            //   border-radius: 12px;

            .card-icon {
                font-size: 16px;
                color: #00d4ff;

                >img {
                    width: 44px;
                }
            }

            .card-title-background {
                display: flex;
                height: 48px;
                padding: 8px 0;
                flex-direction: column;
                align-items: center;
                gap: 10px;
                flex: 1 0 0;
                background: linear-gradient(90deg, rgba(0, 114, 244, 0.00) 0%, rgba(0, 114, 244, 0.30) 51.54%, rgba(0, 114, 244, 0.00) 100%);
            }

            .card-title {
                color: #FFF;
                text-align: center;
                font-feature-settings: 'liga' off, 'clig' off;
                text-shadow: 0 -3px 6px rgba(0, 102, 255, 0.60);
                font-family: PangMenZhengDao-3;
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                letter-spacing: 2.4px;
            }
        }

        .card-content {
            padding: 8px 4px 0 4px;
        }
    }

    /* 安全运营总览 */
    .security-overview {
        .overview-grid {
            display: grid;
            grid-template-columns: 1fr 1.2fr 1fr;
            gap: 12px;
            align-items: center;
            // left: 10px;
        }

        .overview-col {
            display: flex;
            flex-direction: column;
            gap: 16px;
            position: relative;
            width: 118px;
            height: 144px;
            margin-bottom: 26px;
            right: 35px;
        }

        .section-container {
            width: 100%;
            height: 50%;
        }

        .security-item {
            position: relative;
            width: 100%;
            height: 100%;
            // left: 10px;
            text-align: center;
            margin-bottom: 16px;
            color: white;

        }

        .security-item-bg {
            width: 136px;
            height: 65.6px;
            display: block;
            position: absolute;
            // top: 0;
            // left: 0;
            // z-index: 1;
        }

        .state-item {
            position: absolute;
            margin: 5px;
            z-index: 999;
            right: 5px;

            .label {
                color: rgba(229, 240, 255, 0.75);
                font-size: 14px;
            }

            .value-row {
                display: flex;
                align-items: baseline;

                .value {
                    font-size: 28px;
                    font-weight: 700;
                    color: #fff;
                }

                .unit {
                    margin-left: 6px;
                    color: rgba(229, 240, 255, 0.9);
                    font-size: 14px;
                }
            }
        }

        .overview-center {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: -50px;
            margin-top: -4px;
        }

        .security-item-center-bg {
            width: 200px;
        }

        .inner {
            position: absolute;
            width: 13%;
            height: 12%;
            z-index: 999;

            .inner-percent {
                display: flex;
                align-items: baseline;

                .percent-number {
                    // font-size: 48px;
                    // font-weight: 800;
                    // color: #fff;
                    // text-shadow: 0 2px 10px rgba(10, 53, 61, 0.5);

                    color: #EBFDFF;
                    font-family: Poppins;
                    font-size: 36px;
                    font-style: normal;
                    font-weight: 700;
                    // line-height: 80%;
                    /* 28.8px */
                    letter-spacing: 2.16px;
                }

                .percent-unit {
                    font-size: 24px;
                    color: #E5F0FF;
                    margin-left: 6px;
                }
            }

            .inner-label {
                margin-top: 6px;
                font-size: 14px;
                color: #E5F0FF;
            }

        }
    }

    /* 应用订阅趋势 */
    .subscription-trend {
        .trend-content {
            position: relative;

            .trend-left-label {
                position: absolute;
                top: 4px;
                left: 0;
                color: #E5F0FF;
                font-size: 12px;
                opacity: 0.8;
            }
        }
    }

    /* 热门应用TOP */
    .hot-apps {
        .apps-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .app-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .app-left {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 180px;

            .rank {
                color: #8AD3FF;
                font-size: 14px;
                letter-spacing: 0.84px;
            }

            .name {
                color: #E5F0FF;
                font-size: 14px;
                letter-spacing: 0.84px;
                display: inline-flex;
                align-items: center;
                gap: 6px;

                .trend {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;

                    &.up {
                        background: #20D57A;
                    }

                    &.down {
                        background: #FF6B6B;
                    }
                }
            }
        }

        .app-right {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;

            .progress {
                position: relative;
                height: 6px;
                background: rgba(255, 255, 255, 0.12);
                border-radius: 6px;
                overflow: hidden;
                flex: 1;

                .bar {
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, #3AA0FF 0%, #5DE2FF 100%);
                }
            }

            .usage {
                display: flex;
                align-items: baseline;

                .table-num {
                    color: #FFF;
                    font-size: 18px;
                    font-weight: 700;
                    letter-spacing: 1.08px;
                }

                .table-unit {
                    color: #E5F0FF;
                    font-size: 18px;
                    font-weight: 400;
                    letter-spacing: 1.08px;
                    margin-left: 4px;
                }
            }
        }
    }
}