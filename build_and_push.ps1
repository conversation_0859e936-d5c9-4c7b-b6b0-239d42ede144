﻿# 设置输出编码为 UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$env:PYTHONIOENCODING = "utf-8"
$env:LANG = "en_US.UTF-8"
$ErrorActionPreference = "Stop"

# 提示用户选择环境
Write-Host "`n🌍 请选择部署环境：" -ForegroundColor Yellow
Write-Host "1️⃣ 246 环境"
Write-Host "2️⃣ 92 环境"
$envChoice = Read-Host "请输入环境编号 (1 或 2)"

if ($envChoice -eq "1") {
    $adminServer = "https://medpal.sodabot.cn"
} elseif ($envChoice -eq "2") {
    $adminServer = "https://medpal.sodabot.cn"  # 如果92环境有不同的服务器地址，请在这里修改
} else {
    Write-Host "❌ 无效的环境选项！脚本终止。" -ForegroundColor Red
    Read-Host "👀 请重新运行脚本并选择正确的环境，按回车关闭窗口"
    exit 1
}

# 检查 dist 目录是否存在
$distPath = ".\dist"
Write-Host "`n🔍 检查 dist 目录..." -ForegroundColor Cyan
if (-Not (Test-Path $distPath)) {
    Write-Host "📦 dist 目录不存在，正在构建项目..." -ForegroundColor Cyan
    npm run build

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 构建失败！请检查错误信息。" -ForegroundColor Red
        Read-Host "👀 按回车关闭窗口"
        exit 1
    }

    # 再次检查 dist 目录是否创建成功
    if (-Not (Test-Path $distPath)) {
        Write-Host "❌ 构建完成但 dist 目录仍不存在！" -ForegroundColor Red
        Read-Host "👀 按回车关闭窗口"
        exit 1
    }
} else {
    Write-Host "✅ dist 目录已存在，跳过构建步骤" -ForegroundColor Green
}

# 显示 dist 目录内容用于调试
Write-Host "`n📁 dist 目录内容：" -ForegroundColor Cyan
Get-ChildItem $distPath | Format-Table Name, Length -AutoSize

# 构建镜像
$tag = Get-Date -Format "yyyyMMdd_HHmm"
$imagename = "10to3/ai-platform-frontend:$tag"
$aliyunImageName = "crpi-dzk2aw0old0xwqw9.cn-shanghai.personal.cr.aliyuncs.com/10to3/ai-platform-frontend:$tag"

Write-Host "`n🌟 正在构建镜像: $imagename" -ForegroundColor Cyan
docker build -t $imagename .

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ 构建失败！镜像未生成，终止上传流程。" -ForegroundColor Red
    Read-Host "👀 请检查 Dockerfile 或网络问题后重试，按回车关闭窗口"
    exit 1
}

Write-Host "`n🏷️  正在标记镜像为阿里云镜像..." -ForegroundColor Yellow
docker tag $imagename $aliyunImageName

Write-Host "`n⬆️  正在上传镜像到阿里云容器镜像服务..." -ForegroundColor Yellow
docker push $aliyunImageName

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ 上传失败！可能是网络、权限或登录问题。" -ForegroundColor Red
    Read-Host "👀 请检查阿里云容器镜像服务登录状态后重试，按回车关闭窗口"
    exit 1
}

Write-Host "`n✅ 上传完成！镜像标签为: '$aliyunImageName'" -ForegroundColor Green

$cmd = @"
docker rm -f `$(docker ps -aq --filter name=ai-platform-frontend) 2>/dev/null
docker images --format "{{.ID}}" crpi-dzk2aw0old0xwqw9.cn-shanghai.personal.cr.aliyuncs.com/10to3/ai-platform-frontend | xargs docker rmi -f
docker pull $aliyunImageName
docker run -d --name ai-platform-frontend -p 8989:8000 -e ADMIN_SERVER=$adminServer $aliyunImageName
"@

Write-Host "`n请复制以下命令到 Xshell 执行：" -ForegroundColor Cyan
Write-Host "`n$cmd" -ForegroundColor Green

Read-Host "`n✨ 一切正常，按回车关闭窗口"


