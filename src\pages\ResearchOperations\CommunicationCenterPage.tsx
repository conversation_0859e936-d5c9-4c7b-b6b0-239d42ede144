import React, { useState } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Timeline,
  Rate,
  Avatar,
  List,
  Space,
  Badge,
} from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  CalendarOutlined,
  VideoCameraOutlined,
  ShareAltOutlined,
  LikeOutlined,
  UploadOutlined,
  EyeOutlined,
  CommentOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import './CommunicationCenterPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const CommunicationCenterPage: React.FC = () => {
  const [isEventModalVisible, setIsEventModalVisible] = useState(false);
  const [isTopicModalVisible, setIsTopicModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('events');

  // 学术活动数据
  const academicEvents = [
    {
      key: '1',
      title: '2024年人工智能前沿技术研讨会',
      type: '学术会议',
      date: '2024-02-15',
      location: '北京国际会议中心',
      participants: 350,
      status: '即将开始',
      organizer: '中科院计算所',
      rating: 4.8,
    },
    {
      key: '2',
      title: '新材料产业化技术交流论坛',
      type: '产业论坛',
      date: '2024-02-20',
      location: '深圳科技园',
      participants: 280,
      status: '报名中',
      organizer: '深圳大学',
      rating: 4.6,
    },
    {
      key: '3',
      title: '生物医学工程创新研讨会',
      type: '技术研讨',
      date: '2024-02-25',
      location: '上海交通大学',
      participants: 200,
      status: '筹备中',
      organizer: '上海交大医学院',
      rating: 4.7,
    },
  ];

  // 在线讨论数据
  const discussionTopics = [
    {
      id: '1',
      title: 'ChatGPT在科研中的应用前景探讨',
      author: '张教授',
      avatar: 'https://via.placeholder.com/40',
      category: 'AI技术',
      replies: 45,
      views: 1283,
      lastReply: '2小时前',
      isHot: true,
    },
    {
      id: '2',
      title: '新能源汽车电池技术发展趋势',
      author: '李研究员',
      avatar: 'https://via.placeholder.com/40',
      category: '新能源',
      replies: 32,
      views: 956,
      lastReply: '4小时前',
      isHot: true,
    },
    {
      id: '3',
      title: '量子计算在密码学中的应用',
      author: '王博士',
      avatar: 'https://via.placeholder.com/40',
      category: '量子技术',
      replies: 28,
      views: 734,
      lastReply: '6小时前',
      isHot: false,
    },
  ];

  // 专家直播数据
  const expertWebinars = [
    {
      id: '1',
      title: '人工智能在医疗诊断中的突破',
      expert: '陈院士',
      expertTitle: '中国科学院院士',
      avatar: 'https://via.placeholder.com/60',
      time: '2024-02-18 14:00',
      duration: '90分钟',
      viewers: 2845,
      status: 'upcoming',
    },
    {
      id: '2',
      title: '绿色能源技术的未来发展',
      expert: '刘教授',
      expertTitle: '清华大学教授',
      avatar: 'https://via.placeholder.com/60',
      time: '2024-02-20 15:30',
      duration: '120分钟',
      viewers: 1967,
      status: 'upcoming',
    },
    {
      id: '3',
      title: '区块链技术在供应链中的应用',
      expert: '黄博士',
      expertTitle: '北京大学副教授',
      avatar: 'https://via.placeholder.com/60',
      time: '2024-02-16 16:00',
      duration: '75分钟',
      viewers: 3521,
      status: 'completed',
    },
  ];

  // 近期动态
  const recentActivities = [
    {
      time: '刚刚',
      content: '张教授发布了新的研究成果：《深度学习在图像识别中的应用》',
      type: 'publish',
    },
    {
      time: '10分钟前',
      content: '李研究员参与了话题讨论：新能源汽车电池技术发展趋势',
      type: 'discuss',
    },
    {
      time: '30分钟前',
      content: '王博士的直播《量子计算前沿技术》获得了500+点赞',
      type: 'like',
    },
    {
      time: '1小时前',
      content: '陈院士即将开始直播：人工智能在医疗诊断中的突破',
      type: 'webinar',
    },
  ];

  const eventColumns = [
    {
      title: '活动名称',
      dataIndex: 'title',
      key: 'title',
      width: 220,
      ellipsis: true,
      render: (text: any) => <strong>{text}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (text: any) => <Tag color='blue'>{text}</Tag>,
    },
    {
      title: '时间',
      dataIndex: 'date',
      key: 'date',
      width: 110,
    },
    {
      title: '地点',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      ellipsis: true,
    },
    {
      title: '参与人数',
      dataIndex: 'participants',
      key: 'participants',
      width: 100,
      render: (count: any) => <Badge count={count} color='#1890ff' />,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: any) => {
        const colors = { 即将开始: 'green', 报名中: 'blue', 筹备中: 'orange' };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: any) => <Rate disabled defaultValue={rating} style={{ fontSize: 12 }} />,
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: (_: any) => (
        <Space size='small'>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            查看
          </Button>
          <Button type='primary' size='small'>
            报名
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className='communication-center-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>交流中心</h1>
            <p>汇聚学术智慧，促进产业对接，构建开放共享的科研交流平台</p>
            <div className='header-actions'>
              <Button
                type='primary'
                size='large'
                icon={<CalendarOutlined />}
                onClick={() => setIsEventModalVisible(true)}
              >
                发起活动
              </Button>
              <Button
                size='large'
                icon={<MessageOutlined />}
                onClick={() => setIsTopicModalVisible(true)}
              >
                发起讨论
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='学术活动'
                  value={156}
                  suffix='场'
                  prefix={<CalendarOutlined style={{ color: '#1890ff' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='活跃用户'
                  value={8942}
                  suffix='人'
                  prefix={<UserOutlined style={{ color: '#52c41a' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='讨论话题'
                  value={1567}
                  suffix='个'
                  prefix={<MessageOutlined style={{ color: '#fa8c16' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='专家直播'
                  value={89}
                  suffix='场'
                  prefix={<VideoCameraOutlined style={{ color: '#722ed1' }} />}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* 主要内容 */}
      <section className='content-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={16}>
              <Card>
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                  <TabPane
                    tab={
                      <span>
                        <CalendarOutlined />
                        学术活动
                      </span>
                    }
                    key='events'
                  >
                    <div className='tab-header'>
                      <div className='search-filters'>
                        <Search placeholder='搜索活动名称' style={{ width: 250 }} />
                        <Select placeholder='活动类型' style={{ width: 130 }}>
                          <Option value='conference'>学术会议</Option>
                          <Option value='forum'>产业论坛</Option>
                          <Option value='workshop'>技术研讨</Option>
                          <Option value='seminar'>学术讲座</Option>
                        </Select>
                        <Select placeholder='状态' style={{ width: 120 }}>
                          <Option value='upcoming'>即将开始</Option>
                          <Option value='registration'>报名中</Option>
                          <Option value='preparation'>筹备中</Option>
                        </Select>
                      </div>
                      <Button
                        type='primary'
                        icon={<CalendarOutlined />}
                        onClick={() => setIsEventModalVisible(true)}
                      >
                        发起活动
                      </Button>
                    </div>
                    <Table
                      columns={eventColumns}
                      dataSource={academicEvents}
                      pagination={{ pageSize: 8 }}
                      scroll={{ x: 'max-content' }}
                    />
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <MessageOutlined />
                        在线讨论
                      </span>
                    }
                    key='discussions'
                  >
                    <div className='tab-header'>
                      <div className='search-filters'>
                        <Search placeholder='搜索话题' style={{ width: 250 }} />
                        <Select placeholder='分类' style={{ width: 130 }}>
                          <Option value='ai'>AI技术</Option>
                          <Option value='energy'>新能源</Option>
                          <Option value='quantum'>量子技术</Option>
                          <Option value='bio'>生物医学</Option>
                        </Select>
                      </div>
                      <Button
                        type='primary'
                        icon={<MessageOutlined />}
                        onClick={() => setIsTopicModalVisible(true)}
                      >
                        发起讨论
                      </Button>
                    </div>
                    <List
                      itemLayout='vertical'
                      dataSource={discussionTopics}
                      renderItem={item => (
                        <List.Item
                          key={item.id}
                          actions={[
                            <span key='views'>
                              <EyeOutlined /> {item.views}
                            </span>,
                            <span key='replies'>
                              <CommentOutlined /> {item.replies}
                            </span>,
                            <span key='time'>{item.lastReply}</span>,
                          ]}
                        >
                          <List.Item.Meta
                            avatar={<Avatar src={item.avatar} />}
                            title={
                              <div className='topic-title'>
                                {item.isHot && <Tag color='red'>热门</Tag>}
                                <span>{item.title}</span>
                              </div>
                            }
                            description={
                              <div>
                                <Tag color='blue'>{item.category}</Tag>
                                <span>发起人：{item.author}</span>
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <VideoCameraOutlined />
                        专家直播
                      </span>
                    }
                    key='webinars'
                  >
                    <Row gutter={[16, 16]}>
                      {expertWebinars.map(webinar => (
                        <Col xs={24} md={12} key={webinar.id}>
                          <Card className='webinar-card'>
                            <div className='webinar-header'>
                              <Avatar size={60} src={webinar.avatar} />
                              <div className='expert-info'>
                                <h4>{webinar.expert}</h4>
                                <p>{webinar.expertTitle}</p>
                              </div>
                              <div className='webinar-status'>
                                {webinar.status === 'upcoming' ? (
                                  <Tag color='blue' icon={<ClockCircleOutlined />}>
                                    即将开始
                                  </Tag>
                                ) : (
                                  <Tag color='green' icon={<CheckCircleOutlined />}>
                                    已完成
                                  </Tag>
                                )}
                              </div>
                            </div>
                            <h3>{webinar.title}</h3>
                            <div className='webinar-meta'>
                              <div>
                                <CalendarOutlined /> {webinar.time}
                              </div>
                              <div>
                                <ClockCircleOutlined /> {webinar.duration}
                              </div>
                              <div>
                                <EyeOutlined /> {webinar.viewers} 人观看
                              </div>
                            </div>
                            <div className='webinar-actions'>
                              {webinar.status === 'upcoming' ? (
                                <Button type='primary' icon={<PlayCircleOutlined />}>
                                  预约观看
                                </Button>
                              ) : (
                                <Button icon={<PlayCircleOutlined />}>观看回放</Button>
                              )}
                              <Button icon={<ShareAltOutlined />}>分享</Button>
                            </div>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </TabPane>
                </Tabs>
              </Card>
            </Col>

            <Col xs={24} lg={8}>
              {/* 近期动态 */}
              <Card title='近期动态' className='activity-card'>
                <Timeline>
                  {recentActivities.map((activity, index) => (
                    <Timeline.Item
                      key={index}
                      dot={
                        activity.type === 'publish' ? (
                          <FileTextOutlined style={{ color: '#1890ff' }} />
                        ) : activity.type === 'discuss' ? (
                          <MessageOutlined style={{ color: '#52c41a' }} />
                        ) : activity.type === 'like' ? (
                          <LikeOutlined style={{ color: '#fa8c16' }} />
                        ) : (
                          <VideoCameraOutlined style={{ color: '#722ed1' }} />
                        )
                      }
                    >
                      <div className='activity-content'>
                        <p>{activity.content}</p>
                        <span className='activity-time'>{activity.time}</span>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>

              {/* 热门标签 */}
              <Card title='热门标签' className='tags-card'>
                <div className='tag-cloud'>
                  <Tag color='red'>人工智能</Tag>
                  <Tag color='blue'>新能源</Tag>
                  <Tag color='green'>生物医学</Tag>
                  <Tag color='orange'>量子计算</Tag>
                  <Tag color='purple'>区块链</Tag>
                  <Tag color='gold'>新材料</Tag>
                  <Tag color='cyan'>智能制造</Tag>
                  <Tag color='magenta'>大数据</Tag>
                </div>
              </Card>

              {/* 推荐专家 */}
              <Card title='推荐专家' className='experts-card'>
                <List
                  itemLayout='horizontal'
                  dataSource={[
                    { name: '陈院士', title: '人工智能专家', followers: 12543 },
                    { name: '刘教授', title: '新能源技术专家', followers: 8967 },
                    { name: '黄博士', title: '区块链技术专家', followers: 6543 },
                  ]}
                  renderItem={expert => (
                    <List.Item
                      actions={[
                        <Button size='small' type='primary'>
                          关注
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={expert.name}
                        description={
                          <div>
                            <div>{expert.title}</div>
                            <div style={{ color: '#999', fontSize: 12 }}>
                              {expert.followers} 关注者
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* 发起活动模态框 */}
      <Modal
        title='发起学术活动'
        visible={isEventModalVisible}
        onCancel={() => setIsEventModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='活动名称' name='title' rules={[{ required: true }]}>
            <Input placeholder='请输入活动名称' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='活动类型' name='type' rules={[{ required: true }]}>
                <Select placeholder='选择活动类型'>
                  <Option value='conference'>学术会议</Option>
                  <Option value='forum'>产业论坛</Option>
                  <Option value='workshop'>技术研讨</Option>
                  <Option value='seminar'>学术讲座</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='活动时间' name='date' rules={[{ required: true }]}>
                <Input type='datetime-local' />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='活动地点' name='location' rules={[{ required: true }]}>
            <Input placeholder='请输入活动地点' />
          </Form.Item>
          <Form.Item label='活动简介' name='description' rules={[{ required: true }]}>
            <TextArea rows={4} placeholder='请详细描述活动内容和议程' />
          </Form.Item>
          <Form.Item label='相关资料' name='materials'>
            <Upload>
              <Button icon={<UploadOutlined />}>上传资料</Button>
            </Upload>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                发布活动
              </Button>
              <Button onClick={() => setIsEventModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 发起讨论模态框 */}
      <Modal
        title='发起讨论话题'
        visible={isTopicModalVisible}
        onCancel={() => setIsTopicModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='话题标题' name='title' rules={[{ required: true }]}>
            <Input placeholder='请输入话题标题' />
          </Form.Item>
          <Form.Item label='话题分类' name='category' rules={[{ required: true }]}>
            <Select placeholder='选择话题分类'>
              <Option value='ai'>AI技术</Option>
              <Option value='energy'>新能源</Option>
              <Option value='quantum'>量子技术</Option>
              <Option value='bio'>生物医学</Option>
              <Option value='material'>新材料</Option>
              <Option value='blockchain'>区块链</Option>
            </Select>
          </Form.Item>
          <Form.Item label='话题内容' name='content' rules={[{ required: true }]}>
            <TextArea rows={6} placeholder='请详细描述你想讨论的问题' />
          </Form.Item>
          <Form.Item label='相关标签' name='tags'>
            <Select mode='tags' placeholder='添加相关标签'>
              <Option value='人工智能'>人工智能</Option>
              <Option value='机器学习'>机器学习</Option>
              <Option value='深度学习'>深度学习</Option>
              <Option value='新能源'>新能源</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                发布话题
              </Button>
              <Button onClick={() => setIsTopicModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CommunicationCenterPage;
