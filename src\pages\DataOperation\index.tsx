import React from 'react';
import './index.less';
import HeroSection from './components/HeroSection';
import ServiceContent from './components/ServiceContent';
import FeaturesSection from './components/FeaturesSection';
import Solution from './components/Solution';
import StatsSection from './components/StatsSection';
import SolutionDynamic from './components/SolutionDynamic';
import BecomePartner from './components/BecomePartner';

const DataOperation: React.FC = () => {
  return (
    <div className='data-operation'>
      <HeroSection />
      <ServiceContent />
      <FeaturesSection />
      <Solution />
      <StatsSection />
      <SolutionDynamic />
      <BecomePartner />
    </div>
  );
};

export default DataOperation;
