// 菜单配置类型定义
export interface MenuItem {
  key: string;
  label: string;
  path: string;
  icon?: string;
  target?: '_blank' | '_self';
  children?: MenuItem[];
}

export interface MenuConfig {
  id: string;
  name: string;
  logo?: {
    src: string;
    alt: string;
    text?: string;
  };
  items: MenuItem[];
  style?: {
    background?: string;
    textColor?: string;
    activeColor?: string;
    titleColor?: string;
  };
}

// 主平台菜单配置
export const mainPlatformMenu: MenuConfig = {
  id: 'main-platform',
  name: '主平台导航',
  logo: {
    src: '/platform/images/home/<USER>',
    alt: '汇智灵曦',
    text: '河南省人工智能行业赋能中心（医疗）',
  },
  items: [
    { key: 'home', label: '首页', path: '/platform/' },
    {
      key: 'scientific-research',
      label: '科研赋能',
      path: '/platform/scientific-research',
    },
    { key: 'security-ops', label: '安全赋能', path: '/platform/security-ops' },
    { key: 'computing-mall', label: '算力赋能', path: '/platform/computing-mall' },
    { key: 'app-mall', label: '应用中心', path: '/platform/app-mall' },
    { key: 'training-center', label: '培训中心', path: '/platform/training-center' },
    {
      key: 'news-center',
      label: '中心动态',
      path: '#',
      children: [
        {
          key: 'notice-announcement',
          label: '通知公告',
          path: '/platform/news-center/notice-announcement',
        },
        {
          key: 'policy-file',
          label: '政策文件',
          path: '/platform/news-center/policy-file',
        },
        {
          key: 'industry-information',
          label: '行业资讯',
          path: '/platform/news-center/industry-information',
        },
      ],
    },
    // { key: 'model-mall', label: '模型商城', path: '/platform/model-mall' },
    // {
    //   key: 'research-dropdown',
    //   label: '产学研平台',
    //   path: '#',
    //   children: [
    //     {
    //       key: 'scientific-research',
    //       label: '科研赋能平台',
    //       path: '/platform/scientific-research',
    //       target: '_blank',
    //     },
    //     {
    //       key: 'research-operations',
    //       label: '产学研运营',
    //       path: '/platform/research-operations',
    //       target: '_blank',
    //     },
    //   ],
    // },
    // { key: 'data-management', label: '数据空间', path: '/platform/data-management' },
    // { key: 'security-ops', label: '安全运营', path: '/platform/security-ops' },
    { key: 'about', label: '关于我们', path: '/platform/about' },
  ],
  style: {
    background: 'rgba(255, 255, 255, 0.95)',
    textColor: 'rgba(0, 0, 0, 0.65)',
    activeColor: 'rgba(0, 0, 0, 0.85)',
    titleColor: 'rgba(0, 0, 0, 0.85)',
  },
};

// 产学研专用菜单配置
export const scientificResearchMenu: MenuConfig = {
  id: 'scientific-research',
  name: '产学研平台导航',
  logo: {
    src: '/platform/images/home/<USER>',
    alt: '汇智灵曦',
    text: '科研赋能平台',
  },
  items: [
    { key: 'home', label: '首页', path: '/platform/scientific-research' },
    { key: 'products', label: '产品', path: '/platform/scientific-research/product' },
    { key: 'pricing', label: '价格', path: '/platform/scientific-research/pricing' },
    { key: 'about', label: '关于我们', path: '/platform/scientific-research/about' },
  ],
  style: {
    background: 'rgba(255, 255, 255, 0.95)',
    textColor: 'rgba(0, 0, 0, 0.65)',
    activeColor: 'rgba(0, 0, 0, 0.85)',
    titleColor: 'rgba(0, 0, 0, 0.85)',
  },
};

// 管理后台菜单配置
export const managementMenu: MenuConfig = {
  id: 'management',
  name: '管理中心导航',
  logo: {
    src: '/platform/images/home/<USER>',
    alt: '汇智灵曦',
    text: '管理中心',
  },
  items: [
    { key: 'dashboard', label: '', path: '/platform/management/dashboard' },
    // { key: 'user-management', label: '用户管理', path: '/platform/management/users' },
    // { key: 'resource-management', label: '资源管理', path: '/platform/management/resources' },
    // { key: 'data-analysis', label: '数据分析', path: '/platform/management/analytics' },
    // { key: 'system-settings', label: '系统设置', path: '/platform/management/settings' },
  ],
  style: {
    background: '#001529',
    textColor: 'rgba(255, 255, 255, 0.65)',
    activeColor: '#1890ff',
    titleColor: 'rgba(255, 255, 255, 0.85)',
  },
};

// 用户中心菜单配置
export const userCenterMenu: MenuConfig = {
  id: 'user-center',
  name: '用户中心导航',
  logo: {
    src: '/platform/images/home/<USER>',
    alt: '汇智灵曦',
    text: '个人中心',
  },
  items: [
    { key: 'profile', label: '个人信息', path: '/platform/user/profile' },
    { key: 'orders', label: '我的订单', path: '/platform/user/orders' },
    { key: 'resources', label: '我的资源', path: '/platform/user/resources' },
    { key: 'billing', label: '账单管理', path: '/platform/user/billing' },
    { key: 'settings', label: '账户设置', path: '/platform/user/settings' },
  ],
};

// 产学研运营菜单配置
export const researchOperationsMenu: MenuConfig = {
  id: 'research-operations',
  name: '产学研运营平台导航',
  logo: {
    src: '/platform/images/home/<USER>',
    alt: '汇智灵曦',
    text: '产学研运营',
  },
  items: [
    { key: 'home', label: '首页', path: '/platform/research-operations' },
    {
      key: 'collaboration',
      label: '多组织协作',
      path: '/platform/research-operations/collaboration',
    },
    {
      key: 'resources',
      label: '资源目录与共享',
      path: '/platform/research-operations/resources-sharing',
    },
    {
      key: 'projects',
      label: '项目与课程',
      path: '/platform/research-operations/projects-courses',
    },
    { key: 'talent', label: '人才培养', path: '/platform/research-operations/talent-development' },
    {
      key: 'achievement',
      label: '成果转化与技术转移',
      path: '/platform/research-operations/achievement-transfer',
    },
    {
      key: 'communication',
      label: '交流中心',
      path: '/platform/research-operations/communication-center',
    },
    { key: 'security', label: '安全中心', path: '/platform/research-operations/security-center' },
  ],
  style: {
    background: 'rgba(255, 255, 255, 0.95)',
    textColor: 'rgba(0, 0, 0, 0.65)',
    activeColor: 'rgba(0, 0, 0, 0.85)',
    titleColor: 'rgba(0, 0, 0, 0.85)',
  },
};

// 菜单配置映射
export const menuConfigs = {
  'main-platform': mainPlatformMenu,
  'scientific-research': scientificResearchMenu,
  'research-operations': researchOperationsMenu,
  management: managementMenu,
  'user-center': userCenterMenu,
};

// 根据路由匹配菜单配置
export const getMenuConfigByPath = (pathname: string): MenuConfig => {
  // if (pathname.startsWith('/platform/scientific-research')) {
  //   return scientificResearchMenu;
  // }
  if (pathname.startsWith('/platform/research-operations')) {
    return researchOperationsMenu;
  }
  if (pathname.startsWith('/platform/management')) {
    return managementMenu;
  }
  if (pathname.startsWith('/platform/user')) {
    return userCenterMenu;
  }
  // 默认返回主平台菜单
  return mainPlatformMenu;
};

// 菜单工具函数
export const menuUtils = {
  // 获取指定菜单配置
  getMenuConfig: (menuId: string): MenuConfig | undefined => {
    return menuConfigs[menuId as keyof typeof menuConfigs];
  },

  // 根据路径自动匹配菜单
  getMenuByPath: getMenuConfigByPath,

  // 查找菜单项
  findMenuItem: (menu: MenuConfig, key: string): MenuItem | undefined => {
    const findInItems = (items: MenuItem[]): MenuItem | undefined => {
      for (const item of items) {
        if (item.key === key) {
          return item;
        }
        if (item.children) {
          const found = findInItems(item.children);
          if (found) {
            return found;
          }
        }
      }
      return undefined;
    };
    return findInItems(menu.items);
  },
};
