import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import type { UserActions, UserState } from './types';
import type { User } from '../api/types';
import { authApi } from '../api/auth';
import { utils } from '../utils';
import {
  setToken,
  removeToken,
  removeRefreshToken,
  setUserInfo,
  removeUserInfo,
} from '../utils/token';

// 用户状态管理
export const useUserStore = create<UserState & UserActions>()(
  persist(
    set => ({
      // 状态
      user: null,
      isLoggedIn: false,
      isLoading: false,
      error: null,

      // 常用操作
      setUser: (user: User | null) => {
        set({
          user,
          isLoggedIn: !!user,
          error: null,
        });

        if (user) {
          setUserInfo(user);
        } else {
          removeUserInfo();
        }
      },

      // 初始化用户信息(从本地存储恢复)
      initUserFromStorage: () => {
        const userInfo = localStorage.getItem('user_info');
        const token = localStorage.getItem('access_token');

        if (userInfo && token) {
          try {
            const user = JSON.parse(userInfo);
            set({
              user,
              isLoggedIn: true,
              error: null,
            });
          } catch (error) {
            console.error('解析用户信息失败:', error);
            // 清除损坏的数据
            localStorage.removeItem('user_info');
            localStorage.removeItem('access_token');
          }
        }
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          // 生成请求ID用于追踪
          const requestId = utils.generateRequestId();
          console.log(`[Login Request] RequestId: ${requestId}`);

          const response = await authApi.login(username, password, requestId);

          // 存储 token
          setToken(response.token);
          if (response.expiresAt) {
            // 如果有refreshToken也存储
            // setRefreshToken(response.refreshToken);
          }

          // 获取用户详细信息
          const userInfo = await authApi.getUserInfo();

          // 整理用户信息
          const user: User = {
            ...userInfo.user,
            roles: userInfo.roleKeyList,
            permissions: userInfo.permissionList,
            menuList: userInfo.menuList,
          };

          // 存储用户信息
          setUserInfo(user);

          set({
            user,
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          return response;
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录失败',
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          // 调用后端登出接口
          await authApi.logout();
        } catch (error) {
          console.warn('后端登出接口调用失败:', error);
        } finally {
          // 无论后端是否成功，都清除本地存储
          removeToken();
          removeRefreshToken();
          removeUserInfo();

          set({
            user: null,
            isLoggedIn: false,
            isLoading: false,
            error: null,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // 更新用户信息
      updateUserInfo: async () => {
        set({ isLoading: true });

        try {
          const userInfo = await authApi.getUserInfo();

          const user: User = {
            ...userInfo.user,
            roles: userInfo.roleKeyList,
            permissions: userInfo.permissionList,
            menuList: userInfo.menuList,
          };

          setUserInfo(user);

          set({
            user,
            isLoading: false,
            error: null,
          });

          return user;
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '获取用户信息失败',
          });
          throw error;
        }
      },
    }),
    {
      name: 'user-store',
      storage: createJSONStorage(() => localStorage),
      // 只持久化关键状态
      partialize: state => ({
        user: state.user,
        isLoggedIn: state.isLoggedIn,
      }),
    }
  )
);
