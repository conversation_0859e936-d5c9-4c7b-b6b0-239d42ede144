.computing-mall-core-advantage {
  width: 100%;
  padding: 52px 0;
  background: url(/platform/images/computing-mall/core-advantage-bg.png) lightgray 50% / cover
    no-repeat;
  .computing-mall-core-advantage-container {
    width: 1240px;
    margin: 0 auto;
    .computing-mall-core-advantage-title {
      > h2 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
    }
    .computing-mall-core-advantage-content {
      margin-top: 32px;
      height: 280px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      padding: 0 32px;
      gap: 20px;
      border-radius: 20px;
      border: 2px solid #fff;
      background: var(---50, rgba(255, 255, 255, 0.5));
      backdrop-filter: blur(12.5px);
      .advantage-content-item {
        padding: 48px 32px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        > div {
          height: 67px;
          > img {
            height: 67px;
          }
        }
        > h4 {
          color: #000;

          /* h3 */
          font-family: 'Aliba<PERSON> PuHuiTi';
          font-size: 18px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 27px */
        }
        > p {
          color: #000;

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
          opacity: 0.5;
        }
      }
    }
  }
}
