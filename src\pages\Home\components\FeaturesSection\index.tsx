import React from 'react';
import './index.less';
import CustomSwiper from '@/components/CustomSwiper';
import type {
  CustomSwiperProps,
  MaskConfig,
  SlideData,
  SlideSizeConfig,
} from '@/components/CustomSwiper';

const features = [
  {
    id: 1,
    title: '智能辅助诊断',
    description:
      '基于领先的多模态AI技术，系统深度解析CT、MRI影像，秒级完成病灶智能识别与高精度标注。AI如同医生的"火眼金睛"，精准定位结节、肿块、异常信号等病变并清晰标记，显著提升诊断效率与准确性，降低漏诊风险，让医生聚焦临床决策，赋能精准高效诊疗。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 2,
    title: '临床科研数据中台',
    description:
      '平台支持异构数据的标准化采集、清洗与建模，助力多中心临床研究快速开展。研究者可在保证数据安全合规的前提下，灵活调用算力与模型工具进行深度分析，实现从科研数据管理到成果转化的全流程支撑。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 3,
    title: '智能病历结构化',
    description:
      '平台集成先进的自然语言处理大模型，具备强大的电子病历智能理解能力。可对电子病历进行实体识别、关系抽取与标准化处理，自动生成结构化数据。临床信息可高效用于科研分析、质控评估与智能决策支持，提升医院信息化水平，实现了医疗数据的价值最大化。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 4,
    title: '高效模型训练与迭代',
    description:
      '依托强大算力平台和分布式大模型训练系统，实现海量医疗数据的高速处理与模型迭代。支持多任务并行训练，快速优化算法性能，缩短模型研发周期，保障医疗AI系统始终保持最新、最精准的诊断和预测能力。有效提升研发效率和模型质量，助力医疗AI系统持续进化。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 5,
    title: '智能算力调度与资源管理',
    description:
      '平台利用先进的监控与预测技术，实时感知算力需求波动，智能调度算法动态分配算力资源，确保医疗AI应用在高峰期稳定运行。实现计算资源的弹性扩展与自动优化，降低硬件闲置率，提高系统整体效率，保障多种医疗模型的稳定部署和服务连续性。',
    image: '/platform/images/home/<USER>',
  },
];

const FeaturesSection: React.FC = () => {
  // 将features数据转换为SlideData格式
  const slides: SlideData[] = features.map(feature => ({
    id: feature.id,
    content: (
      <div className='feature-card'>
        <div className='feature-image'>
          <img src={feature.image} alt={feature.title} className='background-image' />
        </div>
        <h3 className='feature-title'>{feature.title}</h3>
        <div className='card-content'>
          <p className='feature-description'>{feature.description}</p>
        </div>
      </div>
    ),
  }));

  // 尺寸配置
  const slideSizeConfig: SlideSizeConfig = {
    normalWidth: '480px',
    activeWidth: '620px',
    normalHeight: '336px',
    activeHeight: '100%',
  };

  // 遮罩配置
  const maskConfig: MaskConfig = {
    enabled: true,
  };

  // Swiper配置 - 使用内置箭头和遮罩
  const swiperConfig: CustomSwiperProps = {
    slides,
    showArrows: true, // 恢复内置箭头
    arrowConfig: {
      leftIcon: '/platform/images/scientific-research/arrow-left.svg',
      rightIcon: '/platform/images/scientific-research/arrow-right.svg',
      leftPosition: '192px',
      rightPosition: '192px',
    },
    slideSizeConfig,
    maskConfig, // 添加遮罩配置
    swiperOptions: {
      spaceBetween: 32,
      slidesPerView: 3,
      centeredSlides: true,
      initialSlide: 1,
      loop: true,
    },
    onSlideChange: activeIndex => {
      console.log('Active slide index:', activeIndex);
    },
    className: 'feature-card-container',
  };

  return (
    <section className='features-section'>
      <div className='features-container'>
        <h2 className='section-title'>智能能力，落地场景</h2>
        <div className='features-content'>
          <div className='features-grid'>
            <CustomSwiper {...swiperConfig} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
