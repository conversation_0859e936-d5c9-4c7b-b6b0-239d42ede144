@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.empowerment-section {
  position: relative;
  width: 100%;
  padding: 100px 340px;
  overflow: hidden;

  .empowerment-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    .empowerment-bg-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .empowerment-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
    }
  }

  .empowerment-container {
    position: relative;
    z-index: 2;
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    gap: 200px;
  }

  .empowerment-header {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 20px;

    .empowerment-title {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.5;
      color: #ffffff;
      margin: 0;
      text-align: center;
    }

    .empowerment-subtitle {
      font-family: 'Aliba<PERSON> PuHuiTi', @font-family;
      font-weight: 500;
      font-size: 32px;
      line-height: 1.5;
      color: #ffffff;
      margin: 0;
      text-align: left;
    }
  }

  .empowerment-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .empowerment-tabs {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .empowerment-tab {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 200px;
        height: 60px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        cursor: pointer;
        transition: all 0.3s ease;

        .tab-number,
        .tab-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 400;
          font-size: 20px;
          line-height: 1.5;
          color: #ffffff;
          text-align: center;
        }

        &:hover,
        &.active {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .empowerment-cards {
      display: flex;
      gap: 20px;
      margin-top: 40px;

      .empowerment-card {
        flex: 1;
        width: 470px;
        height: 640px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        transition: all 0.3s ease;

        .card-image {
          width: 100%;
          height: 100%;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(22, 33, 56, 0) 53%, rgba(22, 33, 56, 0.55) 100%);
          }
        }

        .card-content {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 32px;
          z-index: 2;

          .card-title {
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 500;
            font-size: 24px;
            line-height: 1.5;
            color: #ffffff;
            margin: 0 0 12px 0;
          }

          .card-description {
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 400;
            font-size: 16px;
            line-height: 1.8;
            color: #ffffff;
            margin: 0;
            display: none;
          }
        }

        &.active {
          .card-content {
            .card-description {
              display: block;
            }
          }
        }

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .empowerment-section {
    padding: 60px 20px;

    .empowerment-container {
      flex-direction: column;
      gap: 40px;

      .empowerment-content {
        .empowerment-cards {
          flex-direction: column;
          align-items: center;

          .empowerment-card {
            width: 100%;
            max-width: 470px;
            height: auto;
            aspect-ratio: 470/640;
          }
        }
      }
    }
  }
}

@media (max-width: @screen-md) {
  .empowerment-section {
    padding: 40px 16px;

    .empowerment-container {
      .empowerment-header {
        .empowerment-title {
          font-size: 18px;
        }

        .empowerment-subtitle {
          font-size: 24px;
        }
      }

      .empowerment-content {
        .empowerment-tabs {
          .empowerment-tab {
            gap: 20px;
            height: 50px;

            .tab-number,
            .tab-title {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
