import React from 'react';
import './index.less';

const ServiceContent: React.FC = () => {
  return (
    <div className='service-content'>
      <div className='service-content-container'>
        <div className='service-content-title'>
          <h2>服务内容</h2>
        </div>
        <div className='service-content-content'>
          <div className='service-content-item'>
            <h4>数据质检</h4>
            <p>从源头保障数据质量，为您的每一次洞察与行动保驾护航</p>
            <img src="/platform/images/data-operation/service-content-item-1.png" alt="" />
          </div>
          <div className='service-content-item'>
            <h4>数据清洗</h4>
            <p>提升数据的准确性、一致性与可用性，让原始数据焕发真正的洞察与决策价值</p>
            <img src="/platform/images/data-operation/service-content-item-2.png" alt="" />
          </div>
          <div className='service-content-item'>
            <h4>数据管理</h4>
            <p>确保数据的可信、可用、可懂与安全，让分散的数据汇聚成可复用的战略资产</p>
            <img src="/platform/images/data-operation/service-content-item-3.png" alt="" />
          </div>
          <div className='service-content-item'>
            <h4>数据加工</h4>
            <p>通过清洗、转换、整合、计算与特征工程，构建分析所需的数据集市</p>
            <img src="/platform/images/data-operation/service-content-item-4.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceContent;
