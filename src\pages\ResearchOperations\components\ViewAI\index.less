.view-ai-page {
  width: 100%;
  height: 232px;
  background: url(/platform/images/research-operations/view-ai-banner.png) lightgray 50% / cover
    no-repeat;
  .view-ai-container {
    width: 1240px;
    margin: 0 auto;
    padding-top: 42px;
    > h2 {
      color: var(---, #fff);
      font-family: 'Alibaba PuHuiTi';
      font-size: 38px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 57px */
    }
    .view-ai-main {
      margin-top: 40px;
      display: flex;
      height: 48px;
      align-items: flex-end;
      gap: 26px;
      .view-ai-btn {
        display: flex;
        width: 96px;
        height: 48px;
        padding: 8px 20px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 4px;
        background: var(--Brand1-5, #006bff);
        color: #fff;
        border: none;
      }
      .view-ai-info {
        display: flex;
        > p {
          color: #fff;
          font-family: '<PERSON><PERSON><PERSON> PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
        }
        .view-ai-info-line {
          margin: 0 6px;
        }
      }
    }
  }
}
