import React, { useState } from 'react';
import './index.less';
import { Button } from 'antd';

const plans = [
  {
    id: 6,
    title: '科研分析工具',
    img: '/platform/images/app-mall/app-plan-6.svg',
  },
  {
    id: 2,
    title: '智能影像',
    img: '/platform/images/app-mall/app-plan-2.svg',
  },
  {
    id: 3,
    title: '临床辅助决策',
    img: '/platform/images/app-mall/app-plan-3.svg',
  },
  {
    id: 4,
    title: '电子病历处理',
    img: '/platform/images/app-mall/app-plan-4.svg',
  },
  {
    id: 5,
    title: '检验与指标预测',
    img: '/platform/images/app-mall/app-plan-5.svg',
  },
];

const AppPlan: React.FC = () => {
  const [activePlan, setActivePlan] = useState(6);
  return (
    <div className='app-plan-section'>
      <div className='app-plan-container'>
        <div className='app-plan-title'>
          <h2>深耕垂直行业，智启专业方案</h2>
        </div>
        <div className='app-plans'>
          {plans.map(item => (
            <div
              className={`app-plan-item ${activePlan === item.id ? 'active' : ''}`}
              key={item.id}
              onClick={() => setActivePlan(item.id)}
            >
              <img src={item.img} alt={item.title} />
              <p className='app-plan-item-title'>{item.title}</p>
            </div>
          ))}
        </div>
        <div className='app-plan-desc'>
          <div className='app-plan-desc-left'>
            <div className='app-plan-desc-left-title'>
              <h2>灵曦助手</h2>
              <p>科研工作者的智能第二大脑，全流程支持，让医学研究更高效。</p>
            </div>
            <div className='stroke-line'>
              <img src='/platform/images/app-mall/app-plan-stroke.svg' alt='' />
            </div>
            <div className='app-plan-desc-left-content'>
              <div>
                <img src='/platform/images/app-mall/app-plan-true.svg' alt='' />
                <p>AI科研助手，辅助项目构思、撰写</p>
              </div>
              <div>
                <img src='/platform/images/app-mall/app-plan-true.svg' alt='' />
                <p>项目协作管理，任务分工、多人共享</p>
              </div>
              <div>
                <img src='/platform/images/app-mall/app-plan-true.svg' alt='' />
                <p>数据分析工具，自动化图表生成</p>
              </div>
              <div>
                <img src='/platform/images/app-mall/app-plan-true.svg' alt='' />
                <p>成果产出支持：自动生成论文框架</p>
              </div>
            </div>
            <Button
              className='app-plan-desc-left-btn'
              onClick={() => {
                window.open('http://kyan.aethermind.cn/#/login', '_blank');
              }}
            >
              立即体验
            </Button>
          </div>
          <div className='app-plan-desc-right'>
            <img src='/platform/images/app-mall/app-plan-info-1.png' alt='' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppPlan;
