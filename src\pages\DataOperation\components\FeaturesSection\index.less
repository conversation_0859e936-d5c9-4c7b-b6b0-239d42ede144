@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.features-section {
  width: 100%;
  padding: 64px 0;
  background: #f5f7fa;

  .features-container {
    max-width: 1624px;
    width: 1624px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 48px;
  }
  .features-content {
    position: relative;
    width: 100%;
  }

  .section-title {
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 600;
    font-size: 32px;
    line-height: 1.5;
    color: #000000;
    text-align: center;
    margin: 0;
  }

  .features-grid {
    display: flex;
    align-items: stretch;
    gap: 24px;
    justify-content: center;
    width: 100%;
    height: 420px;
  }

  .features-card {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(134, 156, 199, 0.15);

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 40px rgba(134, 156, 199, 0.25);
    }

    .features-image {
      height: 50%;
      .background-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .card-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      .gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(51, 119, 255, 0.9) 0%,
          rgba(51, 119, 255, 0.7) 50%,
          rgba(51, 119, 255, 0.85) 100%
        );
        z-index: 2;
      }
    }
    .feature-title {
      color: var(---45, rgba(0, 0, 0, 0.45));
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 32px;
      display: flex;
      padding: 16px 20px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      /* t2 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      margin: 0;
      border-radius: 4px;
      background: #fff;

      /* 卡片投影 */
      box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
    }

    .card-content {
      position: relative;
      z-index: 3;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 32px 24px;
      color: white;
      > h3 {
        color: var(---85, rgba(0, 0, 0, 0.85));

        /* h2 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 30px */
        margin-bottom: 10px;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .expand-icon {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        margin-left: 16px;
        filter: brightness(0) invert(1); // 将图标变为白色
        opacity: 0.8;
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 1;
        }
      }
    }

    .feature-description {
      color: var(---45, rgba(0, 0, 0, 0.45));

      /* t2 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      margin: 0;
    }
  }

  .arrow-right {
    right: 192px;
  }
  .arrow-left {
    left: 192px;
  }
  .action-btn {
    width: 48px;
    height: 48px;
    background: #ffffff;
    z-index: 10;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);
    border: none;
    border-radius: 122px;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    img {
      width: 24px;
      height: 24px;
    }

    &:hover {
      box-shadow: 0px 12px 24px 0px rgba(134, 156, 199, 0.2);
    }
  }
  .swiper-wrapper {
    align-items: center;
    .swiper-slide-active {
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .features-section {
    .features-container {
      max-width: 100%;
      padding: 0 32px;
    }

    .features-grid {
      gap: 20px;
    }

    .feature-card {
      max-width: 320px;
      min-height: 280px;

      .card-content {
        padding: 28px 20px;
      }

      .feature-title {
        font-size: 20px;
      }

      .feature-description {
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 48px 0;

    .features-container {
      padding: 0 20px;
      gap: 32px;
    }

    .section-title {
      font-size: 28px;
    }

    .features-grid {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    .feature-card {
      max-width: 100%;
      width: 100%;
      min-height: 260px;

      .card-content {
        padding: 24px 20px;
      }

      .feature-title {
        font-size: 18px;
      }

      .feature-description {
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

@media (max-width: 480px) {
  .features-section {
    padding: 40px 0;

    .features-container {
      padding: 0 16px;
      gap: 24px;
    }

    .section-title {
      font-size: 24px;
    }

    .feature-card {
      min-height: 240px;

      .card-content {
        padding: 20px 16px;
      }

      .card-header {
        margin-bottom: 12px;

        .feature-title {
          font-size: 16px;
        }

        .expand-icon {
          width: 20px;
          height: 20px;
          margin-left: 12px;
        }
      }

      .feature-description {
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }
}
