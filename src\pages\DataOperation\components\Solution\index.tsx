import React from 'react';
import './index.less';

const solutionItems = [
  {
    id: 1,
    title: '数据集成',
    img: '/platform/images/data-operation/solution-item-icon-1.svg',
  },
  {
    id: 2,
    title: '数据储存',
    img: '/platform/images/data-operation/solution-item-icon-2.svg',
  },
  {
    id: 3,
    title: '数据治理',
    img: '/platform/images/data-operation/solution-item-icon-3.svg',
  },
  {
    id: 4,
    title: '数据资产治理',
    img: '/platform/images/data-operation/solution-item-icon-4.svg',
  },
  {
    id: 5,
    title: '指标体系',
    img: '/platform/images/data-operation/solution-item-icon-5.svg',
  },
  {
    id: 6,
    title: '主数据管理',
    img: '/platform/images/data-operation/solution-item-icon-6.svg',
  },
];

const sectors = [
  {
    id: 1,
    title: '金融',
    img: '/platform/images/data-operation/solution-sector-icon-1.png',
  },
  {
    id: 2,
    title: '医疗',
    img: '/platform/images/data-operation/solution-sector-icon-2.png',
  },
  {
    id: 3,
    title: '生物',
    img: '/platform/images/data-operation/solution-sector-icon-3.png',
  },
  {
    id: 4,
    title: '能源',
    img: '/platform/images/data-operation/solution-sector-icon-4.png',
  },
  {
    id: 5,
    title: '政务',
    img: '/platform/images/data-operation/solution-sector-icon-5.png',
  },
  {
    id: 6,
    title: '人工智能',
    img: '/platform/images/data-operation/solution-sector-icon-6.png',
  },
  {
    id: 7,
    title: '大模型',
    img: '/platform/images/data-operation/solution-sector-icon-7.png',
  },
  {
    id: 8,
    title: '敬请期待',
    img: null,
  },
];

// 成熟的解决方案
const Solution: React.FC = () => {
  return (
    <section className='solution'>
      <div className='solution-container'>
        <h2 className='solution-title'>成熟的数字化解决方案</h2>
        <div className='solution-content'>
          <div className='solution-content-left'>
            <h3>通用解决方案</h3>
            <div className='solution-content-left-items'>
              {solutionItems.map(item => (
                <div key={item.id} className='solution-content-left-item'>
                  <div className='solution-content-left-item-img'>
                    <img src={item.img} alt={item.title} />
                  </div>
                  <p>{item.title}</p>
                </div>
              ))}
            </div>
          </div>
          <div className='solution-content-right'>
            {sectors.map(item => (
              <div
                key={item.id}
                className={`solution-content-right-item ${
                  item.id === 8 ? 'info-none' : ''
                }`}
              >
                {item.img && (
                  <div className='solution-content-right-item-img'>
                    <img src={item.img} alt={item.title} />
                  </div>
                )}
                <p>{item.title}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Solution;
