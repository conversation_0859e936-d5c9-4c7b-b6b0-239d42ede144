import React, { useState } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Progress,
  Timeline,
  Badge,
  Space,
  Steps,
  Divider,
} from 'antd';
import {
  TrophyOutlined,
  DollarOutlined,
  RocketOutlined,
  TeamOutlined,
  FileTextOutlined,
  ExperimentOutlined,
  UploadOutlined,
  EyeOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import './AchievementTransferPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Step } = Steps;

const AchievementTransferPage: React.FC = () => {
  const [isProjectModalVisible, setIsProjectModalVisible] = useState(false);
  const [isPatentModalVisible, setIsPatentModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('projects');

  // 成果转化项目数据
  const achievementProjects = [
    {
      key: '1',
      name: 'AI智能诊断系统',
      category: '医疗健康',
      stage: '产业化',
      value: '5000万',
      progress: 85,
      partner: '华大医疗',
      status: 'success',
    },
    {
      key: '2',
      name: '新能源材料技术',
      category: '新材料',
      stage: '中试',
      value: '3000万',
      progress: 60,
      partner: '比亚迪',
      status: 'process',
    },
    {
      key: '3',
      name: '智能制造平台',
      category: '智能制造',
      stage: '技术验证',
      value: '8000万',
      progress: 40,
      partner: '富士康',
      status: 'process',
    },
  ];

  // 知识产权数据
  const intellectualProperty = [
    {
      key: '1',
      title: '深度学习图像识别算法',
      type: '发明专利',
      status: '已授权',
      applicant: '张教授团队',
      value: '500万',
      applications: 12,
    },
    {
      key: '2',
      title: '新型锂电池材料',
      type: '发明专利',
      status: '审查中',
      applicant: '李研究员',
      value: '800万',
      applications: 8,
    },
    {
      key: '3',
      title: '智能控制算法',
      type: '软件著作权',
      status: '已登记',
      applicant: '王教授',
      value: '300万',
      applications: 15,
    },
  ];

  // 成功案例时间线
  const successCases = [
    {
      time: '2024年1月',
      title: 'AI诊断系统成功商业化',
      description: '与华大医疗合作的AI智能诊断系统正式投入市场，预计年产值超过5000万',
      type: 'success',
    },
    {
      time: '2023年10月',
      title: '新材料技术中试成功',
      description: '新能源材料技术通过中试验证，获得比亚迪战略投资',
      type: 'success',
    },
    {
      time: '2023年6月',
      title: '智能制造平台技术突破',
      description: '智能制造平台核心技术取得重大突破，获得富士康技术合作意向',
      type: 'process',
    },
    {
      time: '2023年3月',
      title: '成立技术转移中心',
      description: '正式成立技术转移中心，建立完善的成果转化服务体系',
      type: 'default',
    },
  ];

  // 转化服务数据
  const transferServices = [
    {
      title: '技术评估服务',
      description: '专业的技术价值评估和市场前景分析',
      icon: <ExperimentOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      features: ['技术可行性评估', '市场价值分析', '产业化路径规划', '风险评估报告'],
    },
    {
      title: '知识产权服务',
      description: '全方位的知识产权保护和运营服务',
      icon: <FileTextOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      features: ['专利申请代理', '商标注册服务', '著作权登记', '知识产权运营'],
    },
    {
      title: '产业化服务',
      description: '从实验室到市场的全链条产业化服务',
      icon: <RocketOutlined style={{ fontSize: 24, color: '#fa8c16' }} />,
      features: ['中试验证服务', '生产工艺优化', '产业化融资', '市场推广支持'],
    },
    {
      title: '合作对接服务',
      description: '连接科研院所与产业界的桥梁服务',
      icon: <TeamOutlined style={{ fontSize: 24, color: '#722ed1' }} />,
      features: ['企业需求对接', '技术路演服务', '合作协议起草', '项目跟踪服务'],
    },
  ];

  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      ellipsis: true,
      render: (text: any) => <strong>{text}</strong>,
    },
    {
      title: '技术领域',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (text: any) => <Tag color='blue'>{text}</Tag>,
    },
    {
      title: '转化阶段',
      dataIndex: 'stage',
      key: 'stage',
      width: 100,
      render: (text: any) => {
        const colors = { 产业化: 'green', 中试: 'orange', 技术验证: 'blue' };
        return <Tag color={colors[text as keyof typeof colors]}>{text}</Tag>;
      },
    },
    {
      title: '预估价值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      render: (text: any) => <span style={{ color: '#cf1322', fontWeight: 600 }}>{text}</span>,
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: any) => <Progress percent={progress} size='small' />,
    },
    {
      title: '合作伙伴',
      dataIndex: 'partner',
      key: 'partner',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: (_: any) => (
        <Space size='small'>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            查看
          </Button>
          <Button type='link' size='small' icon={<ShareAltOutlined />}>
            推广
          </Button>
        </Space>
      ),
    },
  ];

  const patentColumns = [
    {
      title: '知识产权名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
      render: (text: any) => <strong>{text}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text: any) => <Tag color='purple'>{text}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: any) => {
        const colors = { 已授权: 'green', 审查中: 'orange', 已登记: 'blue' };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      key: 'applicant',
      width: 120,
      ellipsis: true,
    },
    {
      title: '评估价值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      render: (text: any) => <span style={{ color: '#cf1322', fontWeight: 600 }}>{text}</span>,
    },
    {
      title: '应用意向',
      dataIndex: 'applications',
      key: 'applications',
      width: 100,
      render: (count: any) => <Badge count={count} color='#1890ff' />,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any) => (
        <Space size='small'>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            详情
          </Button>
          <Button type='link' size='small' icon={<DownloadOutlined />}>
            下载
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className='achievement-transfer-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>成果转化与技术转移</h1>
            <p>推动科技成果产业化，构建产学研深度融合的创新生态</p>
            <div className='header-actions'>
              <Button
                type='primary'
                size='large'
                icon={<UploadOutlined />}
                onClick={() => setIsProjectModalVisible(true)}
              >
                提交转化项目
              </Button>
              <Button
                size='large'
                icon={<FileTextOutlined />}
                onClick={() => setIsPatentModalVisible(true)}
              >
                申报知识产权
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='转化项目'
                  value={856}
                  suffix='个'
                  prefix={<TrophyOutlined style={{ color: '#1890ff' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='产业化价值'
                  value={285}
                  suffix='亿元'
                  prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='知识产权'
                  value={2341}
                  suffix='项'
                  prefix={<FileTextOutlined style={{ color: '#fa8c16' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='成功转化率'
                  value={94.5}
                  suffix='%'
                  prefix={<RocketOutlined style={{ color: '#722ed1' }} />}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* 主要内容 */}
      <section className='content-section'>
        <div className='container'>
          <Card>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane
                tab={
                  <span>
                    <TrophyOutlined />
                    转化项目
                  </span>
                }
                key='projects'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search placeholder='搜索项目名称' style={{ width: 250 }} />
                    <Select placeholder='技术领域' style={{ width: 150 }}>
                      <Option value='medical'>医疗健康</Option>
                      <Option value='material'>新材料</Option>
                      <Option value='manufacturing'>智能制造</Option>
                      <Option value='energy'>新能源</Option>
                    </Select>
                    <Select placeholder='转化阶段' style={{ width: 150 }}>
                      <Option value='concept'>概念验证</Option>
                      <Option value='pilot'>中试</Option>
                      <Option value='production'>产业化</Option>
                    </Select>
                  </div>
                  <Button
                    type='primary'
                    icon={<UploadOutlined />}
                    onClick={() => setIsProjectModalVisible(true)}
                  >
                    提交项目
                  </Button>
                </div>
                <Table
                  columns={projectColumns}
                  dataSource={achievementProjects}
                  pagination={{ pageSize: 10 }}
                />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <FileTextOutlined />
                    知识产权
                  </span>
                }
                key='patents'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search placeholder='搜索专利名称' style={{ width: 250 }} />
                    <Select placeholder='专利类型' style={{ width: 150 }}>
                      <Option value='invention'>发明专利</Option>
                      <Option value='utility'>实用新型</Option>
                      <Option value='design'>外观设计</Option>
                      <Option value='software'>软件著作权</Option>
                    </Select>
                    <Select placeholder='状态' style={{ width: 120 }}>
                      <Option value='granted'>已授权</Option>
                      <Option value='pending'>审查中</Option>
                      <Option value='registered'>已登记</Option>
                    </Select>
                  </div>
                  <Button
                    type='primary'
                    icon={<FileTextOutlined />}
                    onClick={() => setIsPatentModalVisible(true)}
                  >
                    申报专利
                  </Button>
                </div>
                <Table
                  columns={patentColumns}
                  dataSource={intellectualProperty}
                  pagination={{ pageSize: 10 }}
                />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <RocketOutlined />
                    成功案例
                  </span>
                }
                key='cases'
              >
                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={16}>
                    <Timeline mode='left'>
                      {successCases.map((case_, index) => (
                        <Timeline.Item
                          key={index}
                          dot={
                            case_.type === 'success' ? (
                              <CheckCircleOutlined style={{ color: '#52c41a' }} />
                            ) : case_.type === 'process' ? (
                              <ClockCircleOutlined style={{ color: '#1890ff' }} />
                            ) : (
                              <CalendarOutlined style={{ color: '#8c8c8c' }} />
                            )
                          }
                          color={
                            case_.type === 'success'
                              ? 'green'
                              : case_.type === 'process'
                                ? 'blue'
                                : 'gray'
                          }
                        >
                          <div className='timeline-content'>
                            <h4>{case_.title}</h4>
                            <p className='timeline-time'>{case_.time}</p>
                            <p>{case_.description}</p>
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  </Col>
                  <Col xs={24} lg={8}>
                    <Card title='转化流程' size='small'>
                      <Steps direction='vertical' size='small' current={2}>
                        <Step title='技术评估' description='评估技术成熟度和市场价值' />
                        <Step title='知识产权保护' description='申请专利和其他知识产权' />
                        <Step title='中试验证' description='进行小规模试验验证' />
                        <Step title='产业化' description='大规模生产和市场推广' />
                        <Step title='市场化' description='正式投入市场运营' />
                      </Steps>
                    </Card>
                  </Col>
                </Row>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <TeamOutlined />
                    转化服务
                  </span>
                }
                key='services'
              >
                <Row gutter={[24, 24]}>
                  {transferServices.map((service, index) => (
                    <Col xs={24} md={12} key={index}>
                      <Card className='service-card'>
                        <div className='service-header'>
                          {service.icon}
                          <h3>{service.title}</h3>
                        </div>
                        <p>{service.description}</p>
                        <Divider />
                        <ul>
                          {service.features.map((feature, fIndex) => (
                            <li key={fIndex}>{feature}</li>
                          ))}
                        </ul>
                        <div className='service-actions'>
                          <Button type='primary' size='small'>
                            了解详情
                          </Button>
                          <Button size='small'>申请服务</Button>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>
            </Tabs>
          </Card>
        </div>
      </section>

      {/* 项目提交模态框 */}
      <Modal
        title='提交转化项目'
        visible={isProjectModalVisible}
        onCancel={() => setIsProjectModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='项目名称' name='name' rules={[{ required: true }]}>
            <Input placeholder='请输入项目名称' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='技术领域' name='category' rules={[{ required: true }]}>
                <Select placeholder='选择技术领域'>
                  <Option value='medical'>医疗健康</Option>
                  <Option value='material'>新材料</Option>
                  <Option value='manufacturing'>智能制造</Option>
                  <Option value='energy'>新能源</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='转化阶段' name='stage' rules={[{ required: true }]}>
                <Select placeholder='选择转化阶段'>
                  <Option value='concept'>概念验证</Option>
                  <Option value='pilot'>中试</Option>
                  <Option value='production'>产业化</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='项目描述' name='description' rules={[{ required: true }]}>
            <TextArea rows={4} placeholder='请详细描述项目技术特点和应用场景' />
          </Form.Item>
          <Form.Item label='预估价值' name='value'>
            <Input placeholder='请输入预估市场价值' addonAfter='万元' />
          </Form.Item>
          <Form.Item label='项目文档' name='documents'>
            <Upload>
              <Button icon={<UploadOutlined />}>上传文档</Button>
            </Upload>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                提交申请
              </Button>
              <Button onClick={() => setIsProjectModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 知识产权申报模态框 */}
      <Modal
        title='申报知识产权'
        visible={isPatentModalVisible}
        onCancel={() => setIsPatentModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='知识产权名称' name='title' rules={[{ required: true }]}>
            <Input placeholder='请输入知识产权名称' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='类型' name='type' rules={[{ required: true }]}>
                <Select placeholder='选择类型'>
                  <Option value='invention'>发明专利</Option>
                  <Option value='utility'>实用新型</Option>
                  <Option value='design'>外观设计</Option>
                  <Option value='software'>软件著作权</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='申请人' name='applicant' rules={[{ required: true }]}>
                <Input placeholder='请输入申请人' />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='技术描述' name='description' rules={[{ required: true }]}>
            <TextArea rows={4} placeholder='请详细描述技术特点和创新点' />
          </Form.Item>
          <Form.Item label='相关文档' name='documents'>
            <Upload>
              <Button icon={<UploadOutlined />}>上传文档</Button>
            </Upload>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                提交申请
              </Button>
              <Button onClick={() => setIsPatentModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AchievementTransferPage;
