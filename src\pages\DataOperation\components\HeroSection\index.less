.data-operation-hero-section {
  width: 100%;
  height: 603px;
  background: url(/platform/images/data-operation/data-operation-hero-bg.png) lightgray 50% / cover
    no-repeat;

  .data-operation-hero-container {
    padding-top: 124px;
    padding-left: 339px;
    .data-operation-hero-title {
      > h1 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        font-family: 'Alibaba PuHuiTi';
        font-size: 48px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 72px */
        > span {
          color: var(---Brand1-5, #37f);
        }
      }
      > p {
        color: var(---65, rgba(0, 0, 0, 0.65));
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 36px */
        margin-top: 10px;
      }
    }
    .data-operation-hero-btn {
      margin-top: 40px;
      display: flex;
      height: 48px;
      padding: 8px 20px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
      overflow: hidden;
      color: var(---, #fff);
      text-overflow: ellipsis;

      /* t2 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      &:hover {
        background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
        color: var(---, #fff);
      }
    }
  }
}
