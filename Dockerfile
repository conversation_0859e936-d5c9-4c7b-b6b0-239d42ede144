FROM nginx:1.25.4-alpine3.18

WORKDIR /app

# 安装 curl（可选）
RUN apk add --no-cache curl

# 拷贝 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建并设置 docker-entrypoint.sh
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'if [ -z "$ADMIN_SERVER" ]; then' >> /docker-entrypoint.sh && \
    echo '  echo "Error: ADMIN_SERVER environment variable is not set"' >> /docker-entrypoint.sh && \
    echo '  exit 1' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    echo 'envsubst "\$ADMIN_SERVER" < /etc/nginx/nginx.conf > /etc/nginx/nginx.conf.tmp' >> /docker-entrypoint.sh && \
    echo 'mv /etc/nginx/nginx.conf.tmp /etc/nginx/nginx.conf' >> /docker-entrypoint.sh && \
    echo 'nginx -g "daemon off;"' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

# 🧨 重点：复制 dist 文件夹到 platform 子目录
COPY dist /usr/share/nginx/html/platform

EXPOSE 8000

CMD ["/docker-entrypoint.sh"]
