.product-advantages-section {
  padding: 52px 0;
  background: #ffffff;

  .section-container {
    max-width: 1624px;
    width: 1624px;
    margin: 0 auto;
  }

  .section-title {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    margin-bottom: 32px;
  }

  .advantages-content {
    position: relative;
  }

  .advantages-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .bg-gradient {
      width: 100%;
      height: 466px;
    }
  }

  .advantages-grid {
    position: relative;
    z-index: 2;
    display: flex;
    gap: 32px;
    height: 420px;
    align-items: center;
    // backdrop-filter: blur(1148px);
    mask-image: linear-gradient(
      to right,
      transparent 0%,
      transparent 10%,
      black 30%,
      black 70%,
      transparent 90%,
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      transparent 15%,
      black 30%,
      black 70%,
      transparent 85%,
      transparent 100%
    );
  }

  .advantage-card {
    background: #ffffff;
    border-radius: 9.6px;
    overflow: hidden;
    box-shadow: 0px 8px 16px 0px rgba(134, 156, 199, 0.12);
    width: 480px;
    height: 336px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;

    .advantage-image {
      height: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .advantage-content {
      flex: 1;
      padding: 32px 25.6px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .advantage-title {
        font-family: 'Alibaba PuHuiTi', sans-serif;
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
        line-height: 1.5;
      }

      .advantage-description {
        font-family: 'Alibaba PuHuiTi', sans-serif;
        font-size: 12.8px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        line-height: 1.5;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 6;
        -webkit-box-orient: vertical;
      }
    }
  }

  .arrow-right {
    right: 192px;
  }
  .arrow-left {
    left: 192px;
  }
  .action-btn {
    width: 48px;
    height: 48px;
    background: #ffffff;
    z-index: 10;
    top: 50%;
    position: absolute;
    transform: translateY(-50%);
    border: none;
    border-radius: 122px;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    img {
      width: 24px;
      height: 24px;
    }

    &:hover {
      box-shadow: 0px 12px 24px 0px rgba(134, 156, 199, 0.2);
    }
  }

  .swiper-wrapper {
    align-items: center;
    .swiper-slide-active {
      width: 620px;
      height: 410px;
      box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
      margin-bottom: 10px;
    }
  }
}
