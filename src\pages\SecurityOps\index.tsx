import React from 'react';
import { Card, Row, Col, Statistic, Button, Table, Tag, Progress } from 'antd';
import {
  SafetyOutlined,
  SecurityScanOutlined,
  EyeOutlined,
  FileTextOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import './index.less';

const SecurityOps: React.FC = () => {
  // 安全统计数据
  const securityStats = [
    { title: '威胁检测数', value: 127, suffix: '个' },
    { title: '安全事件', value: 23, suffix: '个' },
    { title: '系统安全评分', value: 94.5, suffix: '分' },
    { title: '合规率', value: 98.7, suffix: '%' },
  ];

  // 安全事件表格列配置
  const securityEventColumns = [
    {
      title: '事件类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap: Record<string, { color: string; icon: any }> = {
          恶意攻击: { color: 'red', icon: <AlertOutlined /> },
          异常登录: { color: 'orange', icon: <ExclamationCircleOutlined /> },
          数据泄露: { color: 'red', icon: <CloseCircleOutlined /> },
          权限异常: { color: 'orange', icon: <ExclamationCircleOutlined /> },
          正常: { color: 'green', icon: <CheckCircleOutlined /> },
        };
        const config = typeMap[type] || { color: 'default', icon: null };
        return (
          <Tag color={config.color} icon={config.icon}>
            {type}
          </Tag>
        );
      },
    },
    {
      title: '威胁等级',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => (
        <Tag color={level === '高' ? 'red' : level === '中' ? 'orange' : 'green'}>{level}</Tag>
      ),
    },
    {
      title: '来源IP',
      dataIndex: 'sourceIp',
      key: 'sourceIp',
    },
    {
      title: '目标资源',
      dataIndex: 'target',
      key: 'target',
    },
    {
      title: '检测时间',
      dataIndex: 'detectTime',
      key: 'detectTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag
          color={status === '已处理' ? 'success' : status === '处理中' ? 'processing' : 'warning'}
        >
          {status}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <div>
          <Button type='link' size='small'>
            详情
          </Button>
          <Button type='link' size='small'>
            处理
          </Button>
          <Button type='link' size='small' danger>
            忽略
          </Button>
        </div>
      ),
    },
  ];

  // 安全事件模拟数据
  const securityEventData = [
    {
      key: '1',
      type: '恶意攻击',
      level: '高',
      sourceIp: '*************',
      target: '/api/user/login',
      detectTime: '2024-01-15 14:30:25',
      status: '处理中',
    },
    {
      key: '2',
      type: '异常登录',
      level: '中',
      sourceIp: '*********',
      target: '管理后台',
      detectTime: '2024-01-15 13:45:18',
      status: '已处理',
    },
    {
      key: '3',
      type: '权限异常',
      level: '中',
      sourceIp: '**********',
      target: '数据库访问',
      detectTime: '2024-01-15 12:20:32',
      status: '待处理',
    },
    {
      key: '4',
      type: '数据泄露',
      level: '高',
      sourceIp: '************',
      target: '用户数据表',
      detectTime: '2024-01-15 11:15:07',
      status: '已处理',
    },
  ];

  return (
    <div className='security-ops'>
      {/* 页面头部 */}
      <div className='page-header'>
        <div className='header-content'>
          <div className='header-info'>
            <h1>
              <SafetyOutlined /> 安全运营
            </h1>
            <p>全方位的安全监控、威胁检测和事件响应平台，保障系统和数据安全</p>
          </div>
        </div>
      </div>

      {/* 安全统计 */}
      <div className='security-stats'>
        <Row gutter={[24, 24]}>
          {securityStats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  valueStyle={{
                    color: index === 0 || index === 1 ? '#ff4d4f' : '#52c41a',
                  }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 功能模块 */}
      <div className='security-features'>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <EyeOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                </div>
              }
            >
              <Card.Meta title='威胁监控' description='实时监控系统威胁' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                查看监控
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <FileTextOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                </div>
              }
            >
              <Card.Meta title='日志审计' description='全面的日志收集、分析和审计功能' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                审计日志
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <SecurityScanOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
                </div>
              }
            >
              <Card.Meta title='安全策略' description='灵活的安全策略配置和管理' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                策略管理
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <CheckCircleOutlined style={{ fontSize: '48px', color: '#722ed1' }} />
                </div>
              }
            >
              <Card.Meta title='合规检查' description='自动化合规检查和报告生成' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                合规报告
              </Button>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 安全态势面板 */}
      <div className='security-dashboard-panel'>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <Card title='安全健康度' className='health-card'>
              <div className='health-item'>
                <div className='health-label'>防火墙状态</div>
                <Progress percent={95} status='active' strokeColor='#52c41a' />
              </div>
              <div className='health-item'>
                <div className='health-label'>入侵检测</div>
                <Progress percent={88} status='active' strokeColor='#1890ff' />
              </div>
              <div className='health-item'>
                <div className='health-label'>病毒防护</div>
                <Progress percent={92} status='active' strokeColor='#52c41a' />
              </div>
              <div className='health-item'>
                <div className='health-label'>数据加密</div>
                <Progress percent={100} status='success' strokeColor='#52c41a' />
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title='威胁趋势' className='trend-card'>
              <div className='trend-item'>
                <div className='trend-info'>
                  <span className='trend-label'>今日威胁</span>
                  <span className='trend-value'>23</span>
                </div>
                <Tag color='red'>+15%</Tag>
              </div>
              <div className='trend-item'>
                <div className='trend-info'>
                  <span className='trend-label'>本周威胁</span>
                  <span className='trend-value'>156</span>
                </div>
                <Tag color='orange'>+8%</Tag>
              </div>
              <div className='trend-item'>
                <div className='trend-info'>
                  <span className='trend-label'>本月威胁</span>
                  <span className='trend-value'>672</span>
                </div>
                <Tag color='green'>-12%</Tag>
              </div>
              <div className='trend-item'>
                <div className='trend-info'>
                  <span className='trend-label'>已阻断</span>
                  <span className='trend-value'>651</span>
                </div>
                <Tag color='green'>97%</Tag>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 安全事件列表 */}
      <div className='security-events'>
        <Card title='安全事件' extra={<Button type='primary'>查看全部</Button>}>
          <Table
            columns={securityEventColumns}
            dataSource={securityEventData}
            pagination={{ pageSize: 5, showSizeChanger: false }}
          />
        </Card>
      </div>
    </div>
  );
};

export default SecurityOps;
