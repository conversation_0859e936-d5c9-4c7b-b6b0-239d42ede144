import React from 'react';
import './index.less';

const solvePlanList = [
  {
    id: 1,
    title: '科研赋能中心',
    shortening: 'INSIGHT',
    img: '/platform/images/about/solve-plan-1.png',
  },
  {
    id: 2,
    title: '算力赋能中心',
    shortening: 'CORE',
    img: '/platform/images/about/solve-plan-2.png',
  },
  {
    id: 3,
    title: '安全研赋能中心',
    shortening: 'SHIELD',
    img: '/platform/images/about/solve-plan-3.png',
  },
  {
    id: 4,
    title: '医疗赋能中心',
    shortening: 'CARE',
    img: '/platform/images/about/solve-plan-4.png',
  },
];

const SolvePlan: React.FC = () => {
  return (
    <section className='about-solve-plan'>
      <div className='about-solve-plan-container'>
        <div className='about-solve-plan-content'>
          <h2>面向多元场景的智能化解决方案</h2>
          <div className='about-solve-plan-content-info'>
            <h3>01 科研赋能中心</h3>
            <p>
              搭建多模态健康医疗大数据科研应用平台，构建标准数据层、科研专用数据库以及患者360全景数据库等多样化数据仓库，针对典型病种建立多模态专病数据库，开发部署数据统计分析工具，覆盖横断面、前瞻性队列研究等场景，为科研人员提供高效的数据分析服务；基于开源模型研究针对专科大模型，为医生提供循证医学辅助决策，加速创新药物研发进程，推动专科医学进步与创新；基于可信数据空间的医疗科研文献数据构建医疗科研文献库，训练智能问答垂直类大模型，为科研人员提供全方位的智能辅助。
            </p>
          </div>
        </div>
        <div className='about-solve-plan-list'>
          {solvePlanList.map(item => (
            <div
              key={item.id}
              className='about-solve-plan-list-item'
              style={{ backgroundImage: `url(${item.img})` }}
            >
              <h3>{item.title}</h3>
              <p>{item.shortening}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SolvePlan;
