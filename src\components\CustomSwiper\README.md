# CustomSwiper 组件

一个基于 Swiper.js 的通用轮播组件，支持自定义内容和配置。

## 功能特性

- 🎨 支持完全自定义的slide内容
- 🎯 灵活的配置选项
- 🖱️ 可选的导航箭头
- 📱 响应式设计
- 🔧 TypeScript支持

## 基本用法

```tsx
import CustomSwiper from '@/components/CustomSwiper';
import type { SlideData } from '@/components/CustomSwiper';

const slides: SlideData[] = [
  {
    id: 1,
    content: <div>第一张幻灯片内容</div>
  },
  {
    id: 2,
    content: <div>第二张幻灯片内容</div>
  },
  {
    id: 3,
    content: <div>第三张幻灯片内容</div>
  }
];

function MyComponent() {
  return <CustomSwiper slides={slides} />;
}
```

## API 参数

### CustomSwiperProps

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| slides | SlideData[] | - | 幻灯片数据数组（必填） |
| showArrows | boolean | true | 是否显示导航箭头 |
| arrowConfig | ArrowConfig | - | 箭头配置对象 |
| className | string | '' | 自定义CSS类名 |
| swiperOptions | SwiperOptions | - | Swiper原生配置选项 |
| slideSizeConfig | SlideSizeConfig | {normalWidth:'480px', activeWidth:'620px', normalHeight:'336px', activeHeight:'100%'} | slide尺寸配置 |
| maskConfig | MaskConfig | {enabled:false} | 遮罩配置 |
| onSlideChange | (activeIndex: number) => void | - | 幻灯片切换回调 |
| onSwiper | (swiper: SwiperObj) => void | - | 获取Swiper实例回调 |
| renderSlide | (slide: SlideData, index: number) => ReactNode | - | 自定义slide渲染函数 |

### SlideData

| 参数 | 类型 | 描述 |
|------|------|------|
| id | string \| number | 唯一标识符 |
| content | ReactNode | 幻灯片内容 |

### ArrowConfig

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| leftIcon | string | '/platform/images/arrow-left.svg' | 左箭头图标路径 |
| rightIcon | string | '/platform/images/arrow-right.svg' | 右箭头图标路径 |
| leftPosition | string | '192px' | 左箭头位置（距离左边缘） |
| rightPosition | string | '192px' | 右箭头位置（距离右边缘） |

### SlideSizeConfig

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| normalWidth | string | '480px' | 普通slide的宽度 |
| activeWidth | string | '620px' | 激活状态slide的宽度（中心slide） |
| normalHeight | string | '336px' | 普通slide的高度 |
| activeHeight | string | '100%' | 激活状态slide的高度（中心slide） |

### MaskConfig

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| enabled | boolean | false | 是否启用遮罩效果 |
| gradient | string | - | 自定义遮罩渐变（mask-image） |
| webkitGradient | string | - | 自定义Webkit遮罩渐变（-webkit-mask-image） |

## 使用示例

### 示例1：基本图片轮播

```tsx
const imageSlides: SlideData[] = [
  {
    id: 1,
    content: (
      <div className="image-slide">
        <img src="/path/to/image1.jpg" alt="图片1" />
        <h3>标题1</h3>
        <p>描述内容1</p>
      </div>
    )
  },
  // 更多slides...
];

<CustomSwiper
  slides={imageSlides}
  swiperOptions={{
    slidesPerView: 3,
    spaceBetween: 20,
    loop: true
  }}
/>
```

### 示例2：卡片轮播

```tsx
const cardSlides: SlideData[] = data.map(item => ({
  id: item.id,
  content: (
    <div className="card">
      <div className="card-header">
        <img src={item.image} alt={item.title} />
      </div>
      <div className="card-body">
        <h3>{item.title}</h3>
        <p>{item.description}</p>
        <button onClick={() => handleClick(item.id)}>
          查看详情
        </button>
      </div>
    </div>
  )
}));

<CustomSwiper
  slides={cardSlides}
  showArrows={true}
  arrowConfig={{
    leftPosition: '50px',
    rightPosition: '50px'
  }}
  onSlideChange={(index) => console.log('当前slide:', index)}
/>
```

### 示例3：自定义渲染函数

```tsx
const products = [
  { id: 1, name: '产品1', price: 99, image: '/img1.jpg' },
  { id: 2, name: '产品2', price: 199, image: '/img2.jpg' },
  // 更多产品...
];

const productSlides: SlideData[] = products.map(product => ({
  id: product.id,
  content: product // 将原始数据作为content
}));

<CustomSwiper
  slides={productSlides}
  renderSlide={(slide, index) => {
    const product = slide.content as typeof products[0];
    return (
      <div className="product-card">
        <img src={product.image} alt={product.name} />
        <h3>{product.name}</h3>
        <p className="price">￥{product.price}</p>
        <button>加入购物车</button>
      </div>
    );
  }}
  swiperOptions={{
    slidesPerView: 'auto',
    spaceBetween: 16,
    centeredSlides: true
  }}
/>
```

### 示例4：自定义slide尺寸（中心模式）

```tsx
<CustomSwiper
  slides={slides}
  slideSizeConfig={{
    normalWidth: '400px',    // 普通slide宽度
    activeWidth: '600px',    // 中心slide宽度
    normalHeight: '280px',   // 普通slide高度
    activeHeight: '100%',    // 中心slide高度（占满容器）
  }}
  swiperOptions={{
    slidesPerView: 3,
    centeredSlides: true,    // 开启中心模式
    spaceBetween: 20,
    loop: true,
  }}
/>
```

### 示例5：仅高度居中（保持normal宽度）

```tsx
<CustomSwiper
  slides={slides}
  slideSizeConfig={{
    normalWidth: '480px',    // 所有slide使用相同宽度
    activeWidth: '480px',    // active slide也使用相同宽度
    normalHeight: '200px',   // normal slide较小高度，会在容器中居中
    activeHeight: '100%',    // active slide占满容器高度
  }}
  swiperOptions={{
    slidesPerView: 3,
    centeredSlides: true,
    spaceBetween: 20,
  }}
/>
```

### 示例6：使用遮罩效果

```tsx
<CustomSwiper
  slides={slides}
  maskConfig={{
    enabled: true,
    gradient: `linear-gradient(
      to right,
      transparent 0%,
      transparent 10%,
      black 30%,
      black 70%,
      transparent 90%,
      transparent 100%
    )`,
    webkitGradient: `linear-gradient(
      to right,
      transparent 0%,
      transparent 15%,
      black 30%,
      black 70%,
      transparent 85%,
      transparent 100%
    )`
  }}
  swiperOptions={{
    slidesPerView: 3,
    centeredSlides: true,
    spaceBetween: 20,
  }}
/>
```

### 示例7：响应式配置

```tsx
<CustomSwiper
  slides={slides}
  slideSizeConfig={{
    normalWidth: '320px',
    activeWidth: '480px',
    normalHeight: '240px',
    activeHeight: '100%',
  }}
  swiperOptions={{
    slidesPerView: 1,
    spaceBetween: 10,
    breakpoints: {
      640: {
        slidesPerView: 2,
        spaceBetween: 20,
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 30,
      },
    },
  }}
/>
```

## 样式自定义

组件提供了CSS类名，可以通过这些类名进行样式自定义：

```less
.custom-swiper-container {
  // 容器样式
  
  .custom-swiper-slide {
    // 单个slide样式
  }
  
  .custom-swiper-arrow {
    // 箭头样式
    
    &.custom-swiper-arrow-left {
      // 左箭头样式
    }
    
    &.custom-swiper-arrow-right {
      // 右箭头样式
    }
  }
}
```

## 中心模式特性

当配置 `centeredSlides: true` 和 `slideSizeConfig` 时，组件会自动应用中心模式效果：

- ✨ **中心slide更大**: 通过 `activeWidth` 和 `activeHeight` 设置中心slide的尺寸
- 🎯 **侧边slide缩小**: 通过 `normalWidth` 和 `normalHeight` 设置普通slide的尺寸  
- 💫 **平滑过渡**: 内置宽度、高度和透明度的平滑过渡动画
- 🎨 **深度效果**: 侧边slide会有透明度和缩放效果
- 🎪 **居中显示**: normal slide会在容器中自动垂直居中显示
- 🎭 **遮罩效果**: 可选的内置遮罩功能，箭头不受遮罩影响

## 注意事项

1. 确保已安装并正确配置了 `swiper` 依赖
2. slides数组不能为空
3. 当slides数量小于等于1时，导航箭头会自动隐藏
4. 组件会根据slides数量自动决定是否开启loop模式
5. 所有Swiper原生配置都可以通过swiperOptions传入
6. 使用中心模式时，建议设置 `centeredSlides: true` 和 `slidesPerView` 为奇数（如3或5）
7. slide尺寸支持任何CSS长度单位（px, %, vw, rem等）
8. `activeHeight: '100%'` 表示中心slide占满容器高度，`normalHeight` 设置的普通slide会垂直居中显示
9. 如果只想要高度差异而不需要宽度差异，可以将 `normalWidth` 和 `activeWidth` 设置为相同值
10. 遮罩功能完全可选，启用后仅影响slide内容，箭头始终可见且功能正常
11. 自定义遮罩渐变时，建议同时设置 `gradient` 和 `webkitGradient` 以确保跨浏览器兼容性
