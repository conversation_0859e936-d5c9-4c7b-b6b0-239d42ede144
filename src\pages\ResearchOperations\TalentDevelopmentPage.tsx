import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Input,
  Select,
  Modal,
  Form,
  Statistic,
  Progress,
  Avatar,
  Rate,
  Badge,
  Steps,
  List,
  Divider,
  Descriptions,
  Timeline,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  BookOutlined,
  PlusOutlined,
  EyeOutlined,
  StarOutlined,
  RocketOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import './TalentDevelopmentPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Step } = Steps;

const TalentDevelopmentPage: React.FC = () => {
  const [isRecruitModalVisible, setIsRecruitModalVisible] = useState(false);
  const [isMentorModalVisible, setIsMentorModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 统计数据
  const stats = [
    { title: '在培人才', value: 1256, prefix: <UserOutlined />, color: '#1890ff' },
    { title: '培养计划', value: 48, prefix: <BookOutlined />, color: '#52c41a' },
    { title: '导师专家', value: 189, prefix: <TeamOutlined />, color: '#fa8c16' },
    { title: '就业率', value: '96%', prefix: <TrophyOutlined />, color: '#722ed1' },
  ];

  // 招聘职位数据
  const jobData = [
    {
      key: '1',
      title: 'AI算法工程师',
      company: '华为技术有限公司',
      location: '深圳',
      salary: '25-40K',
      experience: '3-5年',
      education: '硕士及以上',
      skills: ['Python', '深度学习', 'TensorFlow', 'PyTorch'],
      type: 'full-time',
      urgent: true,
      publishDate: '2024-01-15',
      deadline: '2024-02-15',
      applicants: 156,
      description: '负责AI算法的研发和优化，推进人工智能技术在产品中的应用',
    },
    {
      key: '2',
      title: '新材料研发工程师',
      company: '中科院材料所',
      location: '北京',
      salary: '20-35K',
      experience: '2-4年',
      education: '博士',
      skills: ['材料科学', '纳米技术', '表征分析', '产业化'],
      type: 'full-time',
      urgent: false,
      publishDate: '2024-01-10',
      deadline: '2024-02-10',
      applicants: 89,
      description: '从事新材料的设计、合成、表征及产业化研究工作',
    },
    {
      key: '3',
      title: '生物技术研究员',
      company: '北京大学医学院',
      location: '北京',
      salary: '15-25K',
      experience: '1-3年',
      education: '硕士及以上',
      skills: ['分子生物学', '细胞培养', '基因工程', '蛋白质工程'],
      type: 'internship',
      urgent: false,
      publishDate: '2024-01-08',
      deadline: '2024-02-08',
      applicants: 67,
      description: '参与生物技术相关研究项目，协助完成实验和数据分析',
    },
  ];

  // 培养计划数据
  const programData = [
    {
      key: '1',
      name: '青年科学家培养计划',
      level: '高端计划',
      duration: '3年',
      quota: 20,
      enrolled: 18,
      mentor: '院士团队',
      funding: '50万/年',
      requirements: ['博士学位', '35岁以下', '优秀科研成果'],
      benefits: ['院士指导', '国际交流', '研究经费', '优先项目'],
      nextEnrollment: '2024-03-01',
      status: 'recruiting',
    },
    {
      key: '2',
      name: '产业技术人才计划',
      level: '专业计划',
      duration: '2年',
      quota: 50,
      enrolled: 45,
      mentor: '产业专家',
      funding: '30万/年',
      requirements: ['硕士学位', '相关工作经验', '技术专长'],
      benefits: ['企业实践', '技术培训', '项目参与', '职业发展'],
      nextEnrollment: '2024-04-01',
      status: 'recruiting',
    },
    {
      key: '3',
      name: '国际合作交流计划',
      level: '交流计划',
      duration: '1年',
      quota: 30,
      enrolled: 28,
      mentor: '国际专家',
      funding: '20万/年',
      requirements: ['英语流利', '研究经历', '学术背景'],
      benefits: ['海外交流', '联合培养', '国际视野', '合作研究'],
      nextEnrollment: '2024-05-01',
      status: 'full',
    },
  ];

  // 导师数据
  const mentorData = [
    {
      key: '1',
      name: '张院士',
      title: '中科院院士',
      organization: '清华大学',
      field: '人工智能',
      experience: '30年',
      students: 45,
      achievements: ['国家科技进步奖', '长江学者', '杰青'],
      expertise: ['机器学习', '计算机视觉', '自然语言处理'],
      rating: 4.9,
      contact: '<EMAIL>',
      available: true,
      avatar: '/platform/images/research-operations/mentor-1.jpg',
    },
    {
      key: '2',
      name: '李教授',
      title: '长江学者',
      organization: '北京大学',
      field: '新材料',
      experience: '20年',
      students: 32,
      achievements: ['国家杰青', '万人计划', '科技创新奖'],
      expertise: ['纳米材料', '功能材料', '材料表征'],
      rating: 4.8,
      contact: '<EMAIL>',
      available: true,
      avatar: '/platform/images/research-operations/mentor-2.jpg',
    },
    {
      key: '3',
      name: '王研究员',
      title: '杰青获得者',
      organization: '中科院生物所',
      field: '生物技术',
      experience: '15年',
      students: 28,
      achievements: ['优青', '青年科技奖', '创新人才'],
      expertise: ['基因工程', '合成生物学', '生物制药'],
      rating: 4.7,
      contact: '<EMAIL>',
      available: false,
      avatar: '/platform/images/research-operations/mentor-3.jpg',
    },
  ];

  // 技能评估数据
  const skillAssessment = [
    {
      category: '专业技能',
      skills: [
        { name: '算法设计', level: 85, target: 90 },
        { name: '数据分析', level: 78, target: 85 },
        { name: '系统架构', level: 72, target: 80 },
        { name: '项目管理', level: 65, target: 75 },
      ],
    },
    {
      category: '通用技能',
      skills: [
        { name: '团队协作', level: 88, target: 90 },
        { name: '沟通表达', level: 82, target: 85 },
        { name: '创新思维', level: 75, target: 85 },
        { name: '学习能力', level: 90, target: 95 },
      ],
    },
  ];

  const jobColumns = [
    {
      title: '职位信息',
      dataIndex: 'title',
      key: 'title',
      width: 260,
      render: (text: string, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {text}
            {record.urgent && (
              <Tag color='red' style={{ marginLeft: 8 }}>
                紧急
              </Tag>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.company} • {record.location}
          </div>
          <div style={{ marginTop: 4 }}>
            <Tag color={record.type === 'full-time' ? 'blue' : 'green'}>
              {record.type === 'full-time' ? '全职' : '实习'}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: '薪资待遇',
      dataIndex: 'salary',
      key: 'salary',
      width: 120,
      render: (salary: string) => (
        <span style={{ color: '#f5222d', fontWeight: 'bold' }}>{salary}</span>
      ),
    },
    {
      title: '要求',
      key: 'requirements',
      width: 200,
      render: (_: any, record: any) => (
        <div>
          <div>
            <strong>经验:</strong> {record.experience}
          </div>
          <div>
            <strong>学历:</strong> {record.education}
          </div>
          <div style={{ marginTop: 4 }}>
            {record.skills.slice(0, 2).map((skill: string) => (
              <Tag key={skill} style={{ fontSize: 11 }}>
                {skill}
              </Tag>
            ))}
            {record.skills.length > 2 && (
              <Tag style={{ fontSize: 11, color: '#666' }}>+{record.skills.length - 2}</Tag>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '申请情况',
      key: 'application',
      width: 120,
      render: (_: any, record: any) => (
        <div>
          <div>{record.applicants} 人申请</div>
          <div style={{ fontSize: '12px', color: '#666' }}>截止: {record.deadline}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: () => (
        <div>
          <Button type='primary' size='small'>
            立即申请
          </Button>
          <Button type='link' icon={<EyeOutlined />} size='small'>
            详情
          </Button>
        </div>
      ),
    },
  ];

  const mentorColumns = [
    {
      title: '导师信息',
      dataIndex: 'name',
      key: 'name',
      width: 240,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.avatar} size={40} style={{ marginRight: 12 }} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{record.organization}</div>
          </div>
        </div>
      ),
    },
    {
      title: '专业领域',
      dataIndex: 'field',
      key: 'field',
      width: 160,
      render: (field: string, record: any) => (
        <div>
          <Tag color='blue'>{field}</Tag>
          <div style={{ marginTop: 4, fontSize: '12px' }}>{record.experience} 研究经验</div>
        </div>
      ),
    },
    {
      title: '指导情况',
      key: 'guidance',
      width: 140,
      render: (_: any, record: any) => (
        <div>
          <div>在读学生: {record.students} 人</div>
          <div style={{ marginTop: 4 }}>
            <Rate disabled defaultValue={record.rating} style={{ fontSize: '12px' }} />
            <span style={{ marginLeft: 8, fontSize: '12px' }}>({record.rating})</span>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'available',
      key: 'available',
      width: 120,
      render: (available: boolean) => (
        <Badge
          status={available ? 'success' : 'default'}
          text={available ? '可接收学生' : '暂不接收'}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      render: (_: any, record: any) => (
        <div>
          <Button type='primary' size='small' disabled={!record.available}>
            申请指导
          </Button>
          <Button type='link' icon={<EyeOutlined />} size='small'>
            详情
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className='talent-development-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>人才培养平台</h1>
            <p>构建全方位人才培养体系，助力科技人才成长发展</p>
            <div className='header-actions'>
              <Button
                type='primary'
                size='large'
                icon={<PlusOutlined />}
                onClick={() => setIsRecruitModalVisible(true)}
              >
                发布职位
              </Button>
              <Button
                size='large'
                icon={<TeamOutlined />}
                onClick={() => setIsMentorModalVisible(true)}
              >
                申请导师
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            {stats.map((stat, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className='stat-card'>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    prefix={stat.prefix}
                    valueStyle={{ color: stat.color }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 主要内容标签页 */}
      <section className='content-section'>
        <div className='container'>
          <Card>
            <Tabs defaultActiveKey='jobs' size='large'>
              {/* 职位招聘 */}
              <TabPane
                tab={
                  <span>
                    <RocketOutlined />
                    职位招聘
                  </span>
                }
                key='jobs'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索职位或公司'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索职位:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部领域</Option>
                      <Option value='ai'>人工智能</Option>
                      <Option value='materials'>新材料</Option>
                      <Option value='biotech'>生物技术</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类型</Option>
                      <Option value='full-time'>全职</Option>
                      <Option value='internship'>实习</Option>
                    </Select>
                  </div>
                </div>
                <Table
                  dataSource={jobData}
                  columns={jobColumns}
                  pagination={{ pageSize: 8 }}
                  scroll={{ x: 1000 }}
                />
              </TabPane>

              {/* 培养计划 */}
              <TabPane
                tab={
                  <span>
                    <BookOutlined />
                    培养计划
                  </span>
                }
                key='programs'
              >
                <Row gutter={[16, 16]}>
                  {programData.map(program => (
                    <Col xs={24} lg={8} key={program.key}>
                      <Card
                        title={program.name}
                        extra={<Tag color='gold'>{program.level}</Tag>}
                        className='program-card'
                      >
                        <div className='program-info'>
                          <Descriptions column={1} size='small'>
                            <Descriptions.Item label='培养周期'>
                              {program.duration}
                            </Descriptions.Item>
                            <Descriptions.Item label='招生名额'>
                              {program.enrolled}/{program.quota}
                              <Progress
                                percent={Math.round((program.enrolled / program.quota) * 100)}
                                size='small'
                                style={{ marginLeft: 8, width: 60 }}
                              />
                            </Descriptions.Item>
                            <Descriptions.Item label='指导团队'>{program.mentor}</Descriptions.Item>
                            <Descriptions.Item label='资助标准'>
                              {program.funding}
                            </Descriptions.Item>
                            <Descriptions.Item label='下次招生'>
                              {program.nextEnrollment}
                            </Descriptions.Item>
                          </Descriptions>

                          <Divider orientation='left' style={{ fontSize: '14px' }}>
                            申请要求
                          </Divider>
                          <ul style={{ paddingLeft: 20, margin: 0, fontSize: '12px' }}>
                            {program.requirements.map((req, i) => (
                              <li key={i} style={{ color: '#666' }}>
                                {req}
                              </li>
                            ))}
                          </ul>

                          <Divider orientation='left' style={{ fontSize: '14px' }}>
                            培养优势
                          </Divider>
                          <div>
                            {program.benefits.map((benefit, i) => (
                              <Tag key={i} color='blue' style={{ marginBottom: 4 }}>
                                {benefit}
                              </Tag>
                            ))}
                          </div>

                          <div style={{ marginTop: 16 }}>
                            <Button
                              type='primary'
                              size='small'
                              block
                              disabled={program.status === 'full'}
                            >
                              {program.status === 'full' ? '名额已满' : '立即申请'}
                            </Button>
                          </div>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>

              {/* 导师制度 */}
              <TabPane
                tab={
                  <span>
                    <TeamOutlined />
                    导师制度
                  </span>
                }
                key='mentors'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索导师姓名或机构'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索导师:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部领域</Option>
                      <Option value='ai'>人工智能</Option>
                      <Option value='materials'>新材料</Option>
                      <Option value='biotech'>生物技术</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部状态</Option>
                      <Option value='available'>可接收</Option>
                      <Option value='full'>已满员</Option>
                    </Select>
                  </div>
                </div>
                <Table
                  dataSource={mentorData}
                  columns={mentorColumns}
                  pagination={{ pageSize: 6 }}
                  scroll={{ x: 1000 }}
                />
              </TabPane>

              {/* 技能评估 */}
              <TabPane
                tab={
                  <span>
                    <TrophyOutlined />
                    技能评估
                  </span>
                }
                key='assessment'
              >
                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={16}>
                    <Card title='个人技能雷达图'>
                      {skillAssessment.map((category, categoryIndex) => (
                        <div key={categoryIndex} style={{ marginBottom: 24 }}>
                          <h4>{category.category}</h4>
                          {category.skills.map((skill, skillIndex) => (
                            <div key={skillIndex} style={{ marginBottom: 16 }}>
                              <div
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  marginBottom: 4,
                                }}
                              >
                                <span>{skill.name}</span>
                                <span>
                                  {skill.level}% / {skill.target}%
                                </span>
                              </div>
                              <Progress
                                percent={skill.level}
                                strokeColor={skill.level >= skill.target ? '#52c41a' : '#1890ff'}
                                trailColor='#f0f0f0'
                              />
                              {skill.level < skill.target && (
                                <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                                  距离目标还需提升 {skill.target - skill.level}%
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </Card>
                  </Col>

                  <Col xs={24} lg={8}>
                    <Card title='成长建议' style={{ marginBottom: 16 }}>
                      <Timeline>
                        <Timeline.Item color='blue' dot={<BulbOutlined />}>
                          <div style={{ fontSize: '14px' }}>
                            <strong>参加专业培训</strong>
                            <div style={{ color: '#666' }}>
                              建议参加AI算法进阶课程，提升算法设计能力
                            </div>
                          </div>
                        </Timeline.Item>
                        <Timeline.Item color='green' dot={<BookOutlined />}>
                          <div style={{ fontSize: '14px' }}>
                            <strong>实践项目经验</strong>
                            <div style={{ color: '#666' }}>
                              参与更多实际项目，提升系统架构设计能力
                            </div>
                          </div>
                        </Timeline.Item>
                        <Timeline.Item color='orange' dot={<TeamOutlined />}>
                          <div style={{ fontSize: '14px' }}>
                            <strong>领导力培养</strong>
                            <div style={{ color: '#666' }}>
                              争取项目管理机会，提升团队协作和管理能力
                            </div>
                          </div>
                        </Timeline.Item>
                      </Timeline>
                    </Card>

                    <Card title='推荐活动'>
                      <List
                        size='small'
                        dataSource={[
                          { title: 'AI技术前沿讲座', date: '2024-02-20', type: '学术讲座' },
                          { title: '项目管理实战训练', date: '2024-02-25', type: '技能培训' },
                          { title: '创新创业大赛', date: '2024-03-01', type: '竞赛活动' },
                        ]}
                        renderItem={item => (
                          <List.Item>
                            <div style={{ width: '100%' }}>
                              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                                {item.title}
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <CalendarOutlined style={{ marginRight: 4 }} />
                                {item.date} • {item.type}
                              </div>
                            </div>
                          </List.Item>
                        )}
                      />
                    </Card>
                  </Col>
                </Row>
              </TabPane>

              {/* 职业发展 */}
              <TabPane
                tab={
                  <span>
                    <StarOutlined />
                    职业发展
                  </span>
                }
                key='career'
              >
                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={12}>
                    <Card title='技术专家发展路径'>
                      <Steps direction='vertical' size='small'>
                        <Step
                          status='finish'
                          title='初级工程师'
                          description='掌握基础技术，完成简单项目'
                          icon={<CheckCircleOutlined />}
                        />
                        <Step
                          status='process'
                          title='中级工程师'
                          description='独立解决复杂问题，指导初级工程师'
                          icon={<ClockCircleOutlined />}
                        />
                        <Step
                          status='wait'
                          title='高级工程师'
                          description='技术专家，负责核心技术方案设计'
                        />
                        <Step
                          status='wait'
                          title='技术总监'
                          description='技术领导者，制定技术发展战略'
                        />
                      </Steps>
                    </Card>
                  </Col>

                  <Col xs={24} lg={12}>
                    <Card title='管理岗位发展路径'>
                      <Steps direction='vertical' size='small'>
                        <Step
                          status='finish'
                          title='项目成员'
                          description='参与项目执行，完成分配任务'
                          icon={<CheckCircleOutlined />}
                        />
                        <Step
                          status='wait'
                          title='项目经理'
                          description='负责项目管理，协调团队资源'
                        />
                        <Step
                          status='wait'
                          title='部门主管'
                          description='管理部门运营，制定发展规划'
                        />
                        <Step
                          status='wait'
                          title='高级管理者'
                          description='企业战略决策，业务发展领导'
                        />
                      </Steps>
                    </Card>
                  </Col>
                </Row>

                <Card title='职业发展资源' style={{ marginTop: 24 }}>
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={8}>
                      <Card size='small'>
                        <div style={{ textAlign: 'center' }}>
                          <BookOutlined
                            style={{ fontSize: '24px', color: '#1890ff', marginBottom: 8 }}
                          />
                          <h4>职业规划咨询</h4>
                          <p style={{ fontSize: '12px', color: '#666' }}>
                            专业职业规划师一对一指导，帮助制定个人发展计划
                          </p>
                          <Button type='primary' size='small'>
                            预约咨询
                          </Button>
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} md={8}>
                      <Card size='small'>
                        <div style={{ textAlign: 'center' }}>
                          <TeamOutlined
                            style={{ fontSize: '24px', color: '#52c41a', marginBottom: 8 }}
                          />
                          <h4>内推机会</h4>
                          <p style={{ fontSize: '12px', color: '#666' }}>
                            优质企业内推机会，帮助人才找到理想工作岗位
                          </p>
                          <Button type='primary' size='small'>
                            查看机会
                          </Button>
                        </div>
                      </Card>
                    </Col>
                    <Col xs={24} md={8}>
                      <Card size='small'>
                        <div style={{ textAlign: 'center' }}>
                          <TrophyOutlined
                            style={{ fontSize: '24px', color: '#fa8c16', marginBottom: 8 }}
                          />
                          <h4>能力认证</h4>
                          <p style={{ fontSize: '12px', color: '#666' }}>
                            行业权威认证考试，提升个人专业竞争力
                          </p>
                          <Button type='primary' size='small'>
                            申请认证
                          </Button>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </Card>
              </TabPane>
            </Tabs>
          </Card>
        </div>
      </section>

      {/* 职位发布模态框 */}
      <Modal
        title='发布招聘职位'
        visible={isRecruitModalVisible}
        onOk={() => setIsRecruitModalVisible(false)}
        onCancel={() => setIsRecruitModalVisible(false)}
        width={700}
      >
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='职位名称' name='jobTitle' rules={[{ required: true }]}>
                <Input placeholder='请输入职位名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='工作地点' name='location' rules={[{ required: true }]}>
                <Input placeholder='请输入工作地点' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='薪资范围' name='salary' rules={[{ required: true }]}>
                <Input placeholder='如：20-30K' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='职位类型' name='type' rules={[{ required: true }]}>
                <Select placeholder='请选择职位类型'>
                  <Option value='full-time'>全职</Option>
                  <Option value='part-time'>兼职</Option>
                  <Option value='internship'>实习</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='职位描述' name='description' rules={[{ required: true }]}>
            <Input.TextArea rows={4} placeholder='请详细描述职位要求和工作内容' />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导师申请模态框 */}
      <Modal
        title='申请成为导师'
        visible={isMentorModalVisible}
        onOk={() => setIsMentorModalVisible(false)}
        onCancel={() => setIsMentorModalVisible(false)}
        width={700}
      >
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='姓名' name='mentorName' rules={[{ required: true }]}>
                <Input placeholder='请输入您的姓名' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='职务/职称' name='title' rules={[{ required: true }]}>
                <Input placeholder='如：教授、研究员等' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='所在机构' name='organization' rules={[{ required: true }]}>
                <Input placeholder='请输入所在机构' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='专业领域' name='field' rules={[{ required: true }]}>
                <Select placeholder='请选择专业领域'>
                  <Option value='ai'>人工智能</Option>
                  <Option value='materials'>新材料</Option>
                  <Option value='biotech'>生物技术</Option>
                  <Option value='energy'>新能源</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='个人简介' name='bio' rules={[{ required: true }]}>
            <Input.TextArea rows={4} placeholder='请简要介绍您的研究背景、主要成就等' />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TalentDevelopmentPage;
