import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { env } from '../config/env';
import { utils } from '../utils';
import type { ApiError, ApiResponse } from './types';

// 创建 axios 实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: env.API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  client.interceptors.request.use(
    config => {
      // 添加认证 token（登录和刷新token接口除外）
      const excludeAuthUrls = ['/login', '/auth/refresh'];
      const shouldExcludeAuth = excludeAuthUrls.some(url => config.url === url);
      const token = localStorage.getItem('access_token');
      if (token && !shouldExcludeAuth) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求 ID 用于追踪
      config.headers['X-Request-ID'] = utils.generateRequestId();

      // 开发环境日志
      if (env.DEBUG) {
        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });
      }

      return config;
    },
    error => {
      console.error('[API Request Error]', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 开发环境日志
      if (env.DEBUG) {
        console.log(
          `[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`,
          {
            status: response.status,
            data: response.data,
          }
        );
      }

      // 检查业务状态码
      if (response.data.code !== 200) {
        const error: ApiError = {
          code: response.data.code,
          message: response.data.message,
          timestamp: Date.now(),
        };
        return Promise.reject(error);
      }

      return response;
    },
    async error => {
      const response = error.response;

      // 开发环境日志
      if (env.DEBUG) {
        console.error('[API Response Error]', {
          status: response?.status,
          data: response?.data,
          message: error.message,
        });
      }

      // 处理不同状态码
      if (response) {
        switch (response.status) {
          case 401:
            // token 过期，尝试刷新
            if (await refreshToken()) {
              // 重新发送原请求
              return client.request(error.config);
            } else {
              // 刷新失败，跳转登录
              handleAuthError();
            }
            break;
          case 403:
            // 权限不足
            handlePermissionError();
            break;
          case 429:
            // 请求过于频繁
            handleRateLimitError();
            break;
          case 500:
            // 服务器错误
            handleServerError();
            break;
        }

        // 构造标准错误对象
        const apiError: ApiError = {
          code: response.status,
          message: response.data?.message || error.message,
          details: response.data,
          timestamp: Date.now(),
        };

        return Promise.reject(apiError);
      }

      // 网络错误
      const networkError: ApiError = {
        code: 0,
        message: '网络连接失败，请检查网络设置',
        timestamp: Date.now(),
      };

      return Promise.reject(networkError);
    }
  );

  return client;
};

// 工具函数已移动到 utils 中

const refreshToken = async (): Promise<boolean> => {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      return false;
    }

    const response = await axios.post(`${env.API_BASE_URL}/auth/refresh`, {
      refreshToken,
    });

    if (response.data.code === 200) {
      const { token, refreshToken: newRefreshToken } = response.data.data;
      localStorage.setItem('access_token', token);
      localStorage.setItem('refresh_token', newRefreshToken);
      return true;
    }

    return false;
  } catch {
    return false;
  }
};

const handleAuthError = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user_info');

  // 触发全局事件，让应用跳转到登录页
  window.dispatchEvent(new CustomEvent('auth_error'));
};

const handlePermissionError = () => {
  // 触发权限错误事件
  window.dispatchEvent(new CustomEvent('permission_error'));
};

const handleRateLimitError = () => {
  // 触发限流错误事件
  window.dispatchEvent(new CustomEvent('rate_limit_error'));
};

const handleServerError = () => {
  // 触发服务器错误事件
  window.dispatchEvent(new CustomEvent('server_error'));
};

// 导出 API 客户端实例
export const apiClient = createApiClient();

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiClient.get<ApiResponse<T>>(url, config).then(res => res.data.data),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.post<ApiResponse<T>>(url, data, config).then(res => res.data.data),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.put<ApiResponse<T>>(url, data, config).then(res => res.data.data),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiClient.delete<ApiResponse<T>>(url, config).then(res => res.data.data),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiClient.patch<ApiResponse<T>>(url, data, config).then(res => res.data.data),
};
