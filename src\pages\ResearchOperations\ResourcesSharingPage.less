.resources-sharing-page {
  min-height: 100vh;
  background: #f5f7fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 页面头部
  .page-header {
    background: linear-gradient(135deg, #52c41a 0%, #1890ff 100%);
    color: white;
    padding: 80px 0;
    margin-top: 64px;

    .header-content {
      text-align: center;

      h1 {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 16px;
        color: white;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      p {
        font-size: 18px;
        opacity: 0.9;
        margin-bottom: 40px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }

      .header-actions {
        display: flex;
        gap: 16px;
        justify-content: center;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: center;
        }

        .ant-btn {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
          border-radius: 8px;
        }
      }
    }
  }

  // 统计数据区域
  .stats-section {
    padding: 60px 0;
    background: white;

    .stat-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-weight: 600;
          font-size: 24px;
        }
      }
    }
  }

  // 资源区域
  .resources-section {
    padding: 60px 0;
    background: #f8f9fa;

    .ant-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .ant-tabs {
      .ant-tabs-tab {
        font-size: 16px;
        font-weight: 500;
        padding: 12px 24px;

        .anticon {
          margin-right: 8px;
        }
      }

      .ant-tabs-content-holder {
        padding: 24px 0;
      }
    }

    // 标签页头部控制区域
    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .search-filters {
        display: flex;
        gap: 12px;

        @media (max-width: 768px) {
          flex-direction: column;
        }

        .ant-input-search,
        .ant-select {
          border-radius: 6px;
        }
      }

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }

    // 表格样式
    .ant-table-wrapper {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .ant-table {
        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
          color: #262626;
          border-bottom: 1px solid #f0f0f0;
          white-space: nowrap;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }

        .ant-table-tbody > tr > td {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    // 文档卡片样式
    .document-info {
      .document-tags {
        margin: 12px 0;

        .ant-tag {
          margin-bottom: 4px;
          border-radius: 4px;
        }
      }

      .document-abstract {
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        margin-top: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    // 知识产权卡片
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #262626;
      }

      .ant-descriptions-item-content {
        color: #595959;
      }
    }
  }

  // 设备共享申请模态框
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;

      .ant-modal-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .ant-form {
      .ant-form-item-label > label {
        font-weight: 500;
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        border-radius: 6px;
      }

      .ant-upload.ant-upload-select-picture-card {
        border-radius: 6px;
        border: 1px dashed #d9d9d9;
        background: #fafafa;

        &:hover {
          border-color: #1890ff;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .stats-section,
    .resources-section {
      padding: 40px 0;
    }

    .ant-table-wrapper {
      .ant-table {
        font-size: 12px;
      }
    }

    .ant-card {
      .ant-card-body {
        padding: 16px;
      }
    }
  }

  // 设备状态徽章
  .ant-badge {
    &.ant-badge-status {
      .ant-badge-status-text {
        font-size: 13px;
        color: #595959;
      }
    }
  }

  // 评分样式
  .ant-rate {
    font-size: 14px;

    .ant-rate-star {
      margin-right: 4px;
    }
  }

  // 进度条样式
  .ant-progress {
    &.ant-progress-line {
      .ant-progress-bg {
        border-radius: 4px;
      }
    }
  }

  // 标签样式优化
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    border: none;
    font-weight: 500;
  }

  // 按钮样式优化
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      }
    }

    &.ant-btn-link {
      padding: 0;
      height: auto;

      &:hover {
        background: none;
      }
    }
  }
}
