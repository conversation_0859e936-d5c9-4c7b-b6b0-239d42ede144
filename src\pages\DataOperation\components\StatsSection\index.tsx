import React, { useEffect, useRef, useState } from 'react';

import './index.less';

const StatsSection: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [animatedValues, setAnimatedValues] = useState<number[]>([0, 0, 0, 0, 0]);

  const stats = [
    {
      id: 1,
      value: 2000,
      suffix: '+',
      label: '咨询伙伴',
    },
    {
      id: 2,
      value: 500,
      suffix: '+',
      label: '合伙伙伴',
    },
    {
      id: 3,
      value: 100,
      suffix: '%',
      label: '服务保障',
    },
    {
      id: 4,
      value: 100,
      suffix: '%',
      label: '数据安全',
    },
  ];

  // 数字动画函数
  const animateValue = (start: number, end: number, duration: number, index: number) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeOutQuart缓动函数
      const easeProgress = 1 - Math.pow(1 - progress, 4);
      const current = start + (end - start) * easeProgress;

      setAnimatedValues(prev => {
        const newValues = [...prev];
        newValues[index] = current;
        return newValues;
      });

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    animate();
  };

  // 监听滚动事件，触发动画
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          // 延迟启动每个数字的动画
          stats.forEach((stat, index) => {
            setTimeout(() => {
              animateValue(0, stat.value, 2000 + index * 200, index);
            }, index * 300);
          });
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  const formatValue = (stat: (typeof stats)[0], index: number) => {
    const currentValue = animatedValues[index];
    return `${Math.floor(currentValue)}${stat.suffix}`;
  };

  return (
    <section ref={sectionRef} className='data-operation-stats-section'>
      {/* <div className='stats-background'>
        <img
          src='/platform/images/home/<USER>'
          alt='Stats Background'
          className='background-image'
        />
        <div className='background-overlay'></div>
      </div> */}

      <div className='stats-container'>
        {stats.map((stat, index) => (
          <div
            key={stat.id}
            className={`stat-item ${isVisible ? 'animate' : ''}`}
            style={
              {
                animationDelay: `${index * 0.2}s`,
              } as React.CSSProperties
            }
          >
            <div className='stat-number'>{formatValue(stat, index)}</div>
            <div className='stat-label'>{stat.label}</div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default StatsSection;
