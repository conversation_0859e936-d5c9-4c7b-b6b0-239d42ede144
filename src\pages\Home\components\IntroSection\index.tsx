import React from 'react';
import './index.less';

// 箭头图标组件
const ArrowIcon: React.FC = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="16" cy="16" r="16" fill="white"/>
    <path d="M12 10L18 16L12 22" stroke="#3377FF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const IntroSection: React.FC = () => {
  return (
    <section className='intro-section'>
      <div className='intro-container'>
        <div className='intro-content'>
          <div className='intro-text'>
            <h2 className='intro-title'>河南省人工智能行业赋能中心（医疗）</h2>
            <div className='intro-description'>
              <p>
                立足中原医学科学城，由河南省医学科学院牵头，联合郑州大学第一附属医院、河南空港数字城市开发建设有限公司等10家单位共同建设，是河南省发展和改革委员会审批通过的首批河南省人工智能行业赋能中心之一。
              </p>
              <p>
                中心整合省内顶级医疗资源、算力基础设施和AI优秀研发团队，首创"1+1+2+N+3"医疗可信数据空间架构，集成不低于2000P的分布式算力资源，打造中部地区规模最大的医疗AI算力池。
              </p>
              <p>
                中心以人才培养、产业孵化为目标，构建综合服务运营平台，打通"算力供给-数据赋能-模型进化-应用推广"的创新链条，面向医疗机构、科研机构、高校、企业、政策机构等多元主体，提供覆盖技术支撑、科研协同、产业赋能的全流程服务，推动河南医疗AI技术自主可控与产业能级跃升，为"健康中国2030"提供核心支撑。
              </p>
            </div>
          </div>
          <div className='intro-arrow'>
            <ArrowIcon />
          </div>
        </div>
        <div className='intro-visual'>
          <img 
            src='/platform/images/home/<USER>' 
            alt='介绍背景图片' 
            className='intro-bg-image'
          />
        </div>
      </div>
    </section>
  );
};

export default IntroSection;
