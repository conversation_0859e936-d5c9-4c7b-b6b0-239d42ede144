import React from 'react';
import DynamicHeader from '../DynamicHeader';

interface HeaderProps {
  showBackground?: boolean;
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ showBackground = true, className = '' }) => {
  return (
    <DynamicHeader
      // 不传递 menuConfig，让 DynamicHeader 根据路径自动匹配菜单
      showBackground={showBackground}
      className={className}
      showSearch={true}
      showActions={true}
    />
  );
};

export default Header;
