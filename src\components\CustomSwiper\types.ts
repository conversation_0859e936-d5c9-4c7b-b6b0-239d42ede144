import { ReactNode } from 'react';
import type { SwiperOptions } from 'swiper/types';
import type { Swiper as SwiperObj } from 'swiper';

export interface SlideData {
  id: string | number;
  content: ReactNode;
}

export interface ArrowConfig {
  leftIcon?: string;
  rightIcon?: string;
  leftPosition?: string;
  rightPosition?: string;
}

export interface SlideSizeConfig {
  normalWidth?: string;
  activeWidth?: string;
  normalHeight?: string;
  activeHeight?: string;
}

export interface MaskConfig {
  enabled?: boolean;
  gradient?: string;
  webkitGradient?: string;
}

export interface CustomSwiperProps {
  slides: SlideData[];
  showArrows?: boolean;
  arrowConfig?: ArrowConfig;
  className?: string;
  swiperOptions?: SwiperOptions;
  slideSizeConfig?: SlideSizeConfig;
  maskConfig?: MaskConfig;
  onSlideChange?: (activeIndex: number) => void;
  onSwiper?: (swiper: SwiperObj) => void;
  renderSlide?: (slide: SlideData, index: number) => ReactNode;
}
