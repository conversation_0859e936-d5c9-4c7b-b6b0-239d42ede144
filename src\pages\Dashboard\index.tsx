import React, { useState } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.less';

const DashboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('工作台');

  const handleTabClick = (tabName: string) => {
    setActiveTab(tabName);
  };

  // 小趋势图配置
  const getTrendChartOption = (data: number[], color: string) => ({
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
    xAxis: {
      type: 'category',
      show: false,
      data: data.map((_, index) => index),
    },
    yAxis: {
      type: 'value',
      show: false,
    },
    series: [
      {
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: color,
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color + '40' },
              { offset: 1, color: color + '10' },
            ],
          },
        },
      },
    ],
  });

  // 主趋势图配置
  const getMainTrendOption = () => ({
    title: {
      text: '峰值 5.03 mbps',
      left: 0,
      top: 0,
      textStyle: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'normal',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      data: ['CPU使用率', '内存使用率', '网络带宽'],
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        smooth: true,
        data: [65, 70, 80, 75, 85, 90, 95],
        lineStyle: { color: '#1890ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#1890ff40' },
              { offset: 1, color: '#1890ff10' },
            ],
          },
        },
      },
      {
        name: '内存使用率',
        type: 'line',
        smooth: true,
        data: [45, 50, 60, 55, 65, 70, 75],
        lineStyle: { color: '#52c41a' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#52c41a40' },
              { offset: 1, color: '#52c41a10' },
            ],
          },
        },
      },
      {
        name: '网络带宽',
        type: 'line',
        smooth: true,
        data: [55, 60, 70, 65, 75, 80, 85],
        lineStyle: { color: '#faad14' },
      },
    ],
  });

  // 环形进度图配置（只绘制环形，不显示文字）
  const getProgressOption = (value: number, color: string) => ({
    tooltip: {
      show: false,
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '90%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          {
            value: value,
            itemStyle: { color: color },
            label: { show: false },
            labelLine: { show: false },
          },
          {
            value: 100 - value,
            itemStyle: { color: '#f0f0f0' },
            label: { show: false },
            labelLine: { show: false },
            tooltip: { show: false },
          },
        ],
        emphasis: {
          disabled: true,
        },
        animation: false,
      },
    ],
  });

  // 饼图配置
  const getPieChartOption = (data: { name: string; value: number; color: string }[]) => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      top: 'center',
      formatter: (name: string) => {
        const item = data.find(d => d.name === name);
        return `${name} ${item ? Math.round((item.value / data.reduce((sum, d) => sum + d.value, 0)) * 100) : 0}%`;
      },
    },
    series: [
      {
        name: '资源分布',
        type: 'pie',
        radius: ['0%', '70%'],
        center: ['35%', '50%'],
        data: data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: { color: item.color },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });

  // 费用趋势图配置
  const getCostTrendOption = () => ({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].name}<br/>费用: ¥${params[0].value}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}',
      },
    },
    series: [
      {
        type: 'line',
        smooth: true,
        data: [35000, 38000, 42000, 39000, 45000, 48000, 46000],
        lineStyle: { color: '#1890ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#1890ff40' },
              { offset: 1, color: '#1890ff10' },
            ],
          },
        },
      },
    ],
  });

  // 用户活跃度图配置
  const getUserActivityOption = () => ({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].name}<br/>活跃用户: ${params[0].value}人`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}人',
      },
    },
    series: [
      {
        type: 'line',
        smooth: true,
        data: [650, 720, 800, 750, 850, 900, 856],
        lineStyle: { color: '#52c41a' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#52c41a40' },
              { offset: 1, color: '#52c41a10' },
            ],
          },
        },
      },
    ],
  });

  // 地区分布图配置（使用散点图模拟地图）
  const getMapOption = () => ({
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.data[3]}<br/>节点数: ${params.data[2]}`;
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '10%',
    },
    xAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 100,
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 100,
    },
    series: [
      {
        type: 'scatter',
        symbolSize: (val: any) => {
          return Math.max(val[2] / 3, 30);
        },
        data: [
          [50, 80, 128, '北京'],
          [65, 60, 95, '上海'],
          [55, 30, 76, '广州'],
          [58, 25, 82, '深圳'],
          [62, 55, 64, '杭州'],
        ],
        itemStyle: {
          color: (params: any) => {
            const value = params.data[2];
            if (value > 100) return '#f5222d';
            if (value > 80) return '#faad14';
            return '#1890ff';
          },
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            return `${params.data[3]}\n节点数: ${params.data[2]}`;
          },
          fontSize: 12,
          color: '#333',
        },
      },
    ],
  });

  // 工作台内容
  const renderWorkbench = () => (
    <>
      {/* 顶部栏 */}
      <div className='top-bar'>
        <div className='greeting'>
          <h2>上午好，欢迎来到算力资源调度看板</h2>
        </div>
      </div>

      {/* 数据统计卡片 */}
      <div className='stats-cards'>
        <div className='stats-card'>
          <div className='stats-header'>
            <span className='stats-label'>运行中</span>
            <div className='stats-trend'>
              <ReactECharts
                option={getTrendChartOption([25, 20, 15, 18, 10, 5], '#52c41a')}
                style={{ height: '30px', width: '100px' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>
          <div className='stats-number'>142</div>
          <div className='stats-change'>同比 ↑ 32%</div>
        </div>

        <div className='stats-card'>
          <div className='stats-header'>
            <span className='stats-label'>排队中</span>
            <div className='stats-trend'>
              <ReactECharts
                option={getTrendChartOption([15, 25, 20, 22, 18, 15], '#1890ff')}
                style={{ height: '30px', width: '100px' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>
          <div className='stats-number'>27</div>
          <div className='stats-change'>同比 ↓ 12%</div>
        </div>

        <div className='stats-card'>
          <div className='stats-header'>
            <span className='stats-label'>异常</span>
            <div className='stats-trend'>
              <ReactECharts
                option={getTrendChartOption([20, 18, 25, 15, 22, 20], '#f5222d')}
                style={{ height: '30px', width: '100px' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>
          <div className='stats-number'>9</div>
          <div className='stats-change'>同比 ↑ 32%</div>
        </div>

        <div className='info-card'>
          <div className='info-header'>
            <span className='info-title'>风险提示</span>
            <span className='info-subtitle'>本月账单报表</span>
          </div>
          <div className='info-content'>
            <div className='risk-item'>[高危] 共有234个PETB资金预缴额度(已透支2%)</div>
            <div className='risk-item'>[预警] 新资源调度监监测到峰值突水</div>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className='charts-section'>
        {/* 主趋势图 */}
        <div className='chart-container main-chart'>
          <div className='chart-header'>
            <h3>算力资源使用趋势</h3>
            <div className='chart-controls'>
              <button className='control-btn'>年</button>
              <button className='control-btn'>月</button>
              <button className='control-btn active'>周</button>
            </div>
          </div>
          <div className='chart-content'>
            <ReactECharts
              option={getMainTrendOption()}
              style={{ height: '300px', width: '100%' }}
              opts={{ renderer: 'svg' }}
            />
          </div>
        </div>

        {/* 底部图表区域 */}
        <div className='bottom-charts'>
          {/* 算力资源利用率 */}
          <div className='chart-container utilization-chart'>
            <h3>算力资源利用率</h3>
            <div className='progress-circles'>
              <div className='progress-circle'>
                <div className='circle-wrapper'>
                  <ReactECharts
                    option={getProgressOption(78, '#52c41a')}
                    style={{ height: '100px', width: '100px' }}
                    opts={{ renderer: 'svg' }}
                  />
                  <div className='progress-text'>
                    <div className='progress-value'>78%</div>
                  </div>
                </div>
                <div className='progress-label'>
                  <div>CPU</div>
                  <div>780/1000</div>
                </div>
              </div>

              <div className='progress-circle'>
                <div className='circle-wrapper'>
                  <ReactECharts
                    option={getProgressOption(63, '#1890ff')}
                    style={{ height: '100px', width: '100px' }}
                    opts={{ renderer: 'svg' }}
                  />
                  <div className='progress-text'>
                    <div className='progress-value'>63%</div>
                  </div>
                </div>
                <div className='progress-label'>
                  <div>内存</div>
                  <div>630/1000</div>
                </div>
              </div>

              <div className='progress-circle'>
                <div className='circle-wrapper'>
                  <ReactECharts
                    option={getProgressOption(92, '#f5222d')}
                    style={{ height: '100px', width: '100px' }}
                    opts={{ renderer: 'svg' }}
                  />
                  <div className='progress-text'>
                    <div className='progress-value'>92%</div>
                  </div>
                </div>
                <div className='progress-label'>
                  <div>储存</div>
                  <div>920/1000</div>
                </div>
              </div>
            </div>
          </div>

          {/* 地区算力分布 */}
          <div className='chart-container map-chart'>
            <h3>地区算力分布</h3>
            <div className='map-content'>
              <ReactECharts
                option={getMapOption()}
                style={{ height: '300px', width: '100%' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>

          {/* 资源调度来源 */}
          <div className='chart-container source-chart'>
            <h3>资源调度来源</h3>
            <div className='pie-chart-container'>
              <ReactECharts
                option={getPieChartOption([
                  { name: '本地服务器', value: 58, color: '#1890ff' },
                  { name: '公有云平台化', value: 32, color: '#52c41a' },
                  { name: '国际资源交换', value: 10, color: '#faad14' },
                ])}
                style={{ height: '300px', width: '100%' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>

          {/* 任务成本TOP8 */}
          <div className='chart-container top-tasks'>
            <h3>任务成本TOP8</h3>
            <div className='task-list'>
              <div className='task-item'>
                <span className='task-name'>全景模拟实验</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '85%' }}></div>
                </div>
                <span className='task-value'>282 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>黑白图像渲染</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '70%' }}></div>
                </div>
                <span className='task-value'>166 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>数据多模分析</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '75%' }}></div>
                </div>
                <span className='task-value'>172 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>数字家庭场景</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '35%' }}></div>
                </div>
                <span className='task-value'>63 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>商业分析测试</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '28%' }}></div>
                </div>
                <span className='task-value'>52 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>商业分析测试</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '28%' }}></div>
                </div>
                <span className='task-value'>52 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>商业分析测试</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '23%' }}></div>
                </div>
                <span className='task-value'>42 tokens/任务</span>
              </div>
              <div className='task-item'>
                <span className='task-name'>商业分析测试</span>
                <div className='task-progress'>
                  <div className='progress-bar' style={{ width: '18%' }}></div>
                </div>
                <span className='task-value'>32 tokens/任务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  // 作业内容
  const renderJobs = () => (
    <>
      <div className='top-bar'>
        <div className='greeting'>
          <h2>作业管理</h2>
        </div>
      </div>

      <div className='jobs-content'>
        <div className='jobs-stats'>
          <div className='job-stat-card'>
            <h3>运行中作业</h3>
            <div className='job-number'>156</div>
            <div className='job-trend'>↑ 12% 较昨日</div>
          </div>
          <div className='job-stat-card'>
            <h3>已完成作业</h3>
            <div className='job-number'>1,234</div>
            <div className='job-trend'>↑ 8% 较昨日</div>
          </div>
          <div className='job-stat-card'>
            <h3>失败作业</h3>
            <div className='job-number'>23</div>
            <div className='job-trend'>↓ 15% 较昨日</div>
          </div>
          <div className='job-stat-card'>
            <h3>平均执行时间</h3>
            <div className='job-number'>2.3h</div>
            <div className='job-trend'>↓ 5% 较昨日</div>
          </div>
        </div>

        <div className='jobs-table-container'>
          <div className='table-header'>
            <h3>最近作业列表</h3>
            <button className='new-job-btn'>新建作业</button>
          </div>
          <div className='jobs-table'>
            <div className='table-row header'>
              <div className='table-cell'>作业名称</div>
              <div className='table-cell'>状态</div>
              <div className='table-cell'>创建时间</div>
              <div className='table-cell'>执行时间</div>
              <div className='table-cell'>操作</div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>深度学习训练任务</div>
              <div className='table-cell status-running'>运行中</div>
              <div className='table-cell'>2024-01-15 10:30</div>
              <div className='table-cell'>2小时15分</div>
              <div className='table-cell'>
                <button className='action-btn'>查看</button>
                <button className='action-btn'>停止</button>
              </div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>数据分析处理</div>
              <div className='table-cell status-completed'>已完成</div>
              <div className='table-cell'>2024-01-15 09:15</div>
              <div className='table-cell'>1小时45分</div>
              <div className='table-cell'>
                <button className='action-btn'>查看</button>
                <button className='action-btn'>下载</button>
              </div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>图像识别任务</div>
              <div className='table-cell status-failed'>失败</div>
              <div className='table-cell'>2024-01-15 08:45</div>
              <div className='table-cell'>0小时30分</div>
              <div className='table-cell'>
                <button className='action-btn'>查看</button>
                <button className='action-btn'>重试</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  // 费用内容
  const renderCosts = () => (
    <>
      <div className='top-bar'>
        <div className='greeting'>
          <h2>费用管理</h2>
        </div>
      </div>

      <div className='costs-content'>
        <div className='cost-overview'>
          <div className='cost-card'>
            <h3>本月总费用</h3>
            <div className='cost-amount'>¥ 45,678.90</div>
            <div className='cost-trend'>↑ 12.5% 较上月</div>
          </div>
          <div className='cost-card'>
            <h3>预算使用率</h3>
            <div className='cost-amount'>78.5%</div>
            <div className='cost-trend'>剩余 ¥ 12,321.10</div>
          </div>
          <div className='cost-card'>
            <h3>平均每小时成本</h3>
            <div className='cost-amount'>¥ 12.34</div>
            <div className='cost-trend'>↓ 3.2% 较上月</div>
          </div>
          <div className='cost-card'>
            <h3>资源利用率</h3>
            <div className='cost-amount'>85.2%</div>
            <div className='cost-trend'>↑ 5.1% 较上月</div>
          </div>
        </div>

        <div className='cost-breakdown'>
          <div className='breakdown-chart'>
            <h3>费用构成分析</h3>
            <div className='pie-chart-container'>
              <ReactECharts
                option={getPieChartOption([
                  { name: '计算资源', value: 45, color: '#1890ff' },
                  { name: '存储费用', value: 30, color: '#52c41a' },
                  { name: '网络传输', value: 15, color: '#faad14' },
                  { name: '其他费用', value: 10, color: '#f5222d' },
                ])}
                style={{ height: '300px', width: '100%' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>

          <div className='cost-trend-chart'>
            <h3>费用趋势</h3>
            <div className='trend-graph'>
              <ReactECharts
                option={getCostTrendOption()}
                style={{ height: '200px', width: '100%' }}
                opts={{ renderer: 'svg' }}
              />
            </div>
          </div>
        </div>

        <div className='cost-details'>
          <h3>详细费用记录</h3>
          <div className='cost-table'>
            <div className='table-row header'>
              <div className='table-cell'>日期</div>
              <div className='table-cell'>服务类型</div>
              <div className='table-cell'>使用时长</div>
              <div className='table-cell'>费用</div>
              <div className='table-cell'>状态</div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>2024-01-15</div>
              <div className='table-cell'>GPU计算</div>
              <div className='table-cell'>8小时</div>
              <div className='table-cell'>¥ 1,234.56</div>
              <div className='table-cell status-paid'>已支付</div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>2024-01-14</div>
              <div className='table-cell'>存储服务</div>
              <div className='table-cell'>24小时</div>
              <div className='table-cell'>¥ 567.89</div>
              <div className='table-cell status-paid'>已支付</div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>2024-01-13</div>
              <div className='table-cell'>网络传输</div>
              <div className='table-cell'>2小时</div>
              <div className='table-cell'>¥ 123.45</div>
              <div className='table-cell status-pending'>待支付</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  // 用户内容
  const renderUsers = () => (
    <>
      <div className='top-bar'>
        <div className='greeting'>
          <h2>用户管理</h2>
        </div>
      </div>

      <div className='users-content'>
        <div className='users-stats'>
          <div className='user-stat-card'>
            <h3>总用户数</h3>
            <div className='user-number'>1,234</div>
            <div className='user-trend'>↑ 15% 较上月</div>
          </div>
          <div className='user-stat-card'>
            <h3>活跃用户</h3>
            <div className='user-number'>856</div>
            <div className='user-trend'>↑ 8% 较上月</div>
          </div>
          <div className='user-stat-card'>
            <h3>新增用户</h3>
            <div className='user-number'>89</div>
            <div className='user-trend'>本月新增</div>
          </div>
          <div className='user-stat-card'>
            <h3>在线用户</h3>
            <div className='user-number'>234</div>
            <div className='user-trend'>实时在线</div>
          </div>
        </div>

        <div className='users-table-container'>
          <div className='table-header'>
            <h3>用户列表</h3>
            <div className='table-actions'>
              <input type='text' placeholder='搜索用户...' className='search-input' />
              <button className='add-user-btn'>添加用户</button>
            </div>
          </div>
          <div className='users-table'>
            <div className='table-row header'>
              <div className='table-cell'>用户名</div>
              <div className='table-cell'>邮箱</div>
              <div className='table-cell'>角色</div>
              <div className='table-cell'>状态</div>
              <div className='table-cell'>最后登录</div>
              <div className='table-cell'>操作</div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>陈志强</div>
              <div className='table-cell'><EMAIL></div>
              <div className='table-cell'>管理员</div>
              <div className='table-cell status-active'>活跃</div>
              <div className='table-cell'>2024-01-15 10:30</div>
              <div className='table-cell'>
                <button className='action-btn'>编辑</button>
                <button className='action-btn'>删除</button>
              </div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>刘美丽</div>
              <div className='table-cell'><EMAIL></div>
              <div className='table-cell'>普通用户</div>
              <div className='table-cell status-active'>活跃</div>
              <div className='table-cell'>2024-01-15 09:15</div>
              <div className='table-cell'>
                <button className='action-btn'>编辑</button>
                <button className='action-btn'>删除</button>
              </div>
            </div>
            <div className='table-row'>
              <div className='table-cell'>赵建国</div>
              <div className='table-cell'><EMAIL></div>
              <div className='table-cell'>普通用户</div>
              <div className='table-cell status-inactive'>非活跃</div>
              <div className='table-cell'>2024-01-10 15:45</div>
              <div className='table-cell'>
                <button className='action-btn'>编辑</button>
                <button className='action-btn'>删除</button>
              </div>
            </div>
          </div>
        </div>

        <div className='user-activity'>
          <h3>用户活跃度分析</h3>
          <div className='activity-chart'>
            <ReactECharts
              option={getUserActivityOption()}
              style={{ height: '200px', width: '100%' }}
              opts={{ renderer: 'svg' }}
            />
          </div>
        </div>
      </div>
    </>
  );

  // 根据当前选中的tab渲染对应内容
  const renderContent = () => {
    switch (activeTab) {
      case '工作台':
        return renderWorkbench();
      case '作业':
        return renderJobs();
      case '费用':
        return renderCosts();
      case '用户':
        return renderUsers();
      default:
        return renderWorkbench();
    }
  };

  return (
    <div className='dashboard-page'>
      {/* 左侧导航栏 */}
      <div className='sidebar'>
        <nav className='nav-menu'>
          <div
            className={`nav-item ${activeTab === '工作台' ? 'active' : ''}`}
            onClick={() => handleTabClick('工作台')}
          >
            <span>工作台</span>
          </div>
          <div
            className={`nav-item ${activeTab === '作业' ? 'active' : ''}`}
            onClick={() => handleTabClick('作业')}
          >
            <span>作业</span>
          </div>
          <div
            className={`nav-item ${activeTab === '费用' ? 'active' : ''}`}
            onClick={() => handleTabClick('费用')}
          >
            <span>费用</span>
          </div>
          <div
            className={`nav-item ${activeTab === '用户' ? 'active' : ''}`}
            onClick={() => handleTabClick('用户')}
          >
            <span>用户</span>
          </div>
        </nav>
      </div>

      {/* 主内容区域 */}
      <div className='main-content'>{renderContent()}</div>
    </div>
  );
};

export default DashboardPage;
