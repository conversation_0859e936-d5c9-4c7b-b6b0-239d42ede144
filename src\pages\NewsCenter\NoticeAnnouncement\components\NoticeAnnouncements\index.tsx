import React from 'react';
import './index.less';

const NoticeAnnouncements: React.FC = () => {
  return (
    <div className='notice-announcement'>
      <div className='notice-announcement-container'>
        <div className='notice-announcement-header'>
          <h1>通知公告</h1>
          <div className='notice-announcement-header-content'>
            <h3>中心发布的重要事项、会议安排及工作动态</h3>
            <div className='notice-announcement-header-content-button'>
              <p>查看全部</p>
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
            </div>
          </div>
        </div>
        <div className='notice-announcement-content'>
          {/* 左上大区域 (2x2) */}
          <div className='grid-item-large'>
            <div className='main-announcement'>
              <img src='/platform/images/news-center/notice-announcement-item.png' alt='通知公告' />
            </div>
          </div>

          {/* 右上合并区域 (1x2) */}
          <div className='grid-item-tall'>
            <div className='main-announcement-content'>
              <h2>科研成果展示会将于9月1日在总部会议中心举行</h2>
              <p>
                为促进智慧医疗领域的交流与合作，展示最新科研成果与技术突破，本中心将于
                2025年9月1日在总部会议中心举办
                科研成果展示会。本次活动将汇聚来自医疗机构、科研院所及产业界的专家学者，共同探讨人工智能、大数据、云计算等前沿技术在医疗健康领域的应用与发展。
                <br />
                <br />
                展示会将设置成果发布、技术演示、专题论坛等多个环节，内容涵盖智能诊断、临床数据分析、医疗影像处理、科研数据管理等方向。参会者不仅能现场体验最新的科研成果，还能与研发团队面对面交流，深入了解技术背后的理念与实践。
              </p>
              <div className='main-announcement-button'>
                <p>2025-08-06</p>
                <p className='btn'>查看详情</p>
              </div>
            </div>
          </div>

          {/* 底部三个独立区域 */}
          <div className='grid-item-bottom-row'>
            <div className='grid-item-normal'>
              <div className='announcement-item'>
                <h3>平台V2.3已上线，优化了首页加载速度与数据图表交互</h3>
                <p>
                  本次升级优化了首页加载速度，采用骨架屏技术和渐进式加载策略，提升用户体验。新增群机器人功能，支持@指令快速响应，并优化了移动端触控交互
                </p>
                <div className='item-footer'>
                  <span>2025-08-06</span>
                  <span className='btn'>查看详情</span>
                </div>
              </div>
            </div>

            <div className='grid-item-normal'>
              <div className='announcement-item'>
                <h3>数据看板将于8月22日进行例行备份，期间暂停访问</h3>
                <p>
                  维护时间为8月22日2:00-5:00，期间数据暂停更新。本次升级将优化ETL流程，支持增量更新，并强化异常处理机制，确保数据稳定性
                </p>
                <div className='item-footer'>
                  <span>2025-07-30</span>
                  <span className='btn'>查看详情</span>
                </div>
              </div>
            </div>

            <div className='grid-item-normal'>
              <div className='announcement-item'>
                <h3>近期请注意防范钓鱼邮件与虚假登录链接</h3>
                <p>
                  警惕伪装成同事或系统的钓鱼邮件，注意发件人地址异常、泛化称呼或紧急威胁性措辞。切勿点击可疑链接，重要操作需二次验证
                  。
                </p>
                <div className='item-footer'>
                  <span>2025-07-28</span>
                  <span className='btn'>查看详情</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticeAnnouncements;
