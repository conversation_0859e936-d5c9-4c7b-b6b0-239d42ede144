.app-hero-section {
  width: 100%;
  height: 524px;
  padding-top: 64px;
  background-color: #f0f3fd;
  background: url(/platform/images/app-mall/hero-banner.jpg) no-repeat center center;
  background-size: cover;
  .app-hero-content {
    padding-top: 140px;
    width: 1240px;
    margin: 0 auto;
    .app-hero-title {
      width: 100%;
      text-align: center;
      h1 {
        margin: 0;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 52px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 78px */
        text-align: center;
      }
      h3 {
        margin: 0;
        color: var(---85, rgba(0, 0, 0, 0.85));
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        text-align: center;
        line-height: 150%; /* 30px */
      }
    }
    .app-hero-main {
      width: 100%;
      margin-top: 36px;
      .app-hero-search {
        width: 600px;
        height: 52px;
        margin: 0 auto;
        position: relative;
        .app-hero-search-input {
          width: 100%;
          height: 100%;
          border-radius: 12px;
          background: #fff;
          border-color: transparent;
        }
        > img {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .app-hero-hot-search {
        width: 600px;
        margin: 0 auto;
        margin-top: 16px;
        .app-hero-hot-search-item {
          display: flex;
          justify-content: center;
          gap: 12px;
          color: var(---45, rgba(0, 0, 0, 0.45));

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
        }
      }
    }
  }
}
