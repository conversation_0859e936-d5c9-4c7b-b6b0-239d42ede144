import React from 'react';
import { Card, Typography, Collapse, Tag, Alert, Table } from 'antd';
import { CodeOutlined, ApiOutlined, DatabaseOutlined, SecurityScanOutlined } from '@ant-design/icons';
import './index.less';

const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

const TechnicalGuide: React.FC = () => {
  const apiColumns = [
    {
      title: 'API名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '方法',
      dataIndex: 'method',
      key: 'method',
      render: (method: string) => (
        <Tag color={method === 'POST' ? 'blue' : method === 'GET' ? 'green' : 'orange'}>
          {method}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  const apiData = [
    {
      key: '1',
      name: '/api/analysis/upload',
      method: 'POST',
      description: '上传数据文件进行分析',
    },
    {
      key: '2',
      name: '/api/analysis/status',
      method: 'GET',
      description: '查询分析任务状态',
    },
    {
      key: '3',
      name: '/api/analysis/result',
      method: 'GET',
      description: '获取分析结果',
    },
    {
      key: '4',
      name: '/api/literature/search',
      method: 'POST',
      description: '智能文献检索',
    },
  ];

  return (
    <div className="technical-guide-page">
      <div className="guide-container">
        <div className="guide-header">
          <Title level={1}>技术指南</Title>
          <Paragraph>
            本平台是专为医疗机构、科研团队及药企研发设计的AI驱动型科研分析系统，
            集成自然语言处理、医学影像识别与多组学分析能力，助力高效产出可信赖的医学研究成果。
          </Paragraph>
        </div>

        <div className="guide-content">
          <Card title={<><CodeOutlined /> 技术架构</>} className="guide-card">
            <div className="architecture-section">
              <Title level={4}>系统架构概览</Title>
              <Paragraph>
                平台采用微服务架构设计，包含以下核心模块：
              </Paragraph>
              <ul>
                <li><Text strong>数据处理层：</Text>负责数据的预处理、清洗和格式转换</li>
                <li><Text strong>AI计算层：</Text>集成多种机器学习和深度学习算法</li>
                <li><Text strong>业务逻辑层：</Text>处理具体的业务流程和规则</li>
                <li><Text strong>接口服务层：</Text>提供RESTful API和WebSocket服务</li>
                <li><Text strong>前端展示层：</Text>基于React的现代化用户界面</li>
              </ul>
            </div>
          </Card>

          <Card title={<><ApiOutlined /> API接口文档</>} className="guide-card">
            <Alert
              message="API访问说明"
              description="所有API请求需要在Header中包含有效的Authorization Token。请联系管理员获取API密钥。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Table
              columns={apiColumns}
              dataSource={apiData}
              pagination={false}
              size="small"
            />
          </Card>

          <Card title={<><DatabaseOutlined /> 数据格式规范</>} className="guide-card">
            <Collapse>
              <Panel header="医学影像数据" key="1">
                <Paragraph>
                  支持的格式：DICOM、NIfTI、PNG、JPEG
                </Paragraph>
                <Paragraph>
                  <Text code>
                    {`{
  "imageType": "DICOM",
  "resolution": "512x512",
  "bitDepth": 16,
  "metadata": {
    "patientId": "P001",
    "studyDate": "2024-01-01",
    "modality": "CT"
  }
}`}
                  </Text>
                </Paragraph>
              </Panel>
              
              <Panel header="文献数据" key="2">
                <Paragraph>
                  支持的格式：PDF、DOC、DOCX、TXT
                </Paragraph>
                <Paragraph>
                  <Text code>
                    {`{
  "title": "文献标题",
  "authors": ["作者1", "作者2"],
  "journal": "期刊名称",
  "year": 2024,
  "doi": "10.1000/example",
  "abstract": "摘要内容"
}`}
                  </Text>
                </Paragraph>
              </Panel>
              
              <Panel header="实验数据" key="3">
                <Paragraph>
                  支持的格式：CSV、Excel、JSON
                </Paragraph>
                <Paragraph>
                  <Text code>
                    {`{
  "experimentId": "EXP001",
  "type": "clinical_trial",
  "subjects": 100,
  "variables": ["age", "gender", "treatment"],
  "measurements": [...]
}`}
                  </Text>
                </Paragraph>
              </Panel>
            </Collapse>
          </Card>

          <Card title={<><SecurityScanOutlined /> 安全与隐私</>} className="guide-card">
            <div className="security-section">
              <Title level={4}>数据安全保障</Title>
              <ul>
                <li><Text strong>数据加密：</Text>采用AES-256加密算法保护数据传输和存储</li>
                <li><Text strong>访问控制：</Text>基于角色的权限管理系统</li>
                <li><Text strong>审计日志：</Text>完整记录所有数据访问和操作行为</li>
                <li><Text strong>数据脱敏：</Text>自动识别和处理敏感信息</li>
              </ul>
              
              <Alert
                message="隐私保护"
                description="平台严格遵守GDPR、HIPAA等国际数据保护法规，确保用户数据的隐私和安全。"
                type="success"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
          </Card>

          <Card title="性能优化建议" className="guide-card">
            <div className="performance-section">
              <Title level={4}>最佳实践</Title>
              <ol>
                <li><Text strong>数据预处理：</Text>上传前对数据进行适当的预处理和压缩</li>
                <li><Text strong>批量操作：</Text>对于大量数据，建议使用批量处理接口</li>
                <li><Text strong>缓存策略：</Text>合理利用缓存机制减少重复计算</li>
                <li><Text strong>异步处理：</Text>对于耗时操作，使用异步任务处理</li>
              </ol>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TechnicalGuide;
