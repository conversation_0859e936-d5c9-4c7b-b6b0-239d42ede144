.industry-informations {
  width: 100%;
  padding: 52px 0;
  position: relative;
  background: var(---fill-7, #f5f7fa);

  .industry-informations-container {
    width: 1240px;
    margin: 0 auto;
    position: relative;
    .industry-informations-header {
      width: 100%;
      > h1 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
    }

    .industry-informations-content {
      margin-top: 32px;
      display: grid;
      height: 400px;
      column-gap: 16px;
      align-self: stretch;
      grid-template-rows: 1fr;
      grid-template-columns: 2fr 1fr 1fr;
      border-radius: 2px;

      .industry-informations-main-item {
        grid-column: 1; /* 第1列，占2个单位宽度 */
        height: 100%; /* 确保填满分配的grid区域 */
        box-sizing: border-box; /* 包含padding在内的高度计算 */

        > div {
          height: 100%;
          border-radius: 8px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .industry-informations-item {
        border-radius: 2px;
        background: var(---, #fff);
        display: flex;
        padding: 52px 32px;
        flex-direction: column;
        align-items: flex-start;
        gap: 30px;
        height: 100%; /* 确保填满分配的grid区域 */
        box-sizing: border-box; /* 包含padding在内的高度计算 */

        > h3 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          align-self: stretch;
          overflow: hidden;
          color: #000;
          text-overflow: ellipsis;

          /* t1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 24px */
        }
        .industry-informations-item-content {
          display: flex;
          padding: 20px 0;
          justify-content: center;
          align-items: flex-start;
          gap: 10px;
          flex: 1 0 0;
          align-self: stretch;
          border-top: 1px solid var(---fill-9, #e6eaf2);
          > p {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            flex: 1 0 0;
            overflow: hidden;
            color: var(---65, rgba(0, 0, 0, 0.65));
            text-overflow: ellipsis;

            /* t2-段落 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 180%; /* 28.8px */
          }
        }

        .industry-informations-item-bottom {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .industry-informations-item-bottom-date {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .industry-informations-item-bottom-date-month {
              overflow: hidden;
              color: var(---25, rgba(0, 0, 0, 0.25));
              text-overflow: ellipsis;

              /* t4 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 12px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%; /* 18px */
            }
            .industry-informations-item-bottom-date-day {
              overflow: hidden;
              color: var(---45, rgba(0, 0, 0, 0.45));
              text-overflow: ellipsis;

              /* h3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 18px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%; /* 27px */
            }
          }
          .industry-informations-item-bottom-arrow {
            display: flex;
            width: 48px;
            height: 48px;
            padding: 5px 3px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
            aspect-ratio: 1/1;
            border-radius: 100px;
            background: var(---fill-7, #f5f7fa);
            img {
            }
          }
        }
      }
    }
  }
}
