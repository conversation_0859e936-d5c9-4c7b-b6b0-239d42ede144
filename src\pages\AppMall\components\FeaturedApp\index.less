.featured-apps-section {
  width: 100%;
  padding: 52px 0;
  width: 100%;
  margin: 0 auto;
  background: var(---fill-7, #f5f7fa);
  .featured-apps-container {
    width: 1240px;
    margin: 0 auto;
  }
  .featured-apps-title {
    h2 {
      display: flex;
      width: 100%;
      height: 52px;
      justify-content: center;
      align-items: center;
      margin: 0;

      color: var(---85, rgba(0, 0, 0, 0.85));
      text-align: center;
      font-family: 'Alibaba PuHuiTi';
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 48px */
    }
    .featured-apps-title-content {
      display: flex;
      width: 100%;
      justify-content: center;
      align-items: center;
      gap: 12px;
      color: var(---85, rgba(0, 0, 0, 0.85));
      margin: 0;
      margin-top: 8px;

      /* t2 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      opacity: 0.8;
      p {
        margin: 0;
      }
      .featured-apps-title-content-link {
        color: var(---Brand1-5, #37f);
        cursor: pointer;
        > img:nth-child(2) {
          opacity: 0.8;
        }
        > img:nth-child(3) {
          opacity: 0.6;
        }
      }
    }
  }
  .featured-apps-main {
    margin-top: 44px;
    width: 100%;
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(3, 1fr);
    .featured-apps-main-item {
      height: 338px;
      display: flex;
      padding: 40px 52px;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      gap: 32px;
      flex: 1 0 0;
      border-radius: 28px;
      background: #fff;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      .featured-apps-main-item-img {
        width: 64px;
        height: 64px;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        > img {
          position: relative;
          z-index: 2;
          width: 64px;
          height: 64px;
        }
      }
      .featured-apps-main-item-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        .featured-apps-main-item-content-title {
          h3 {
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: 'Alibaba PuHuiTi';
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 30px */
            margin: 0;
          }
          p {
            margin: 0;
            margin-top: 8px;
            overflow: hidden;
            color: var(---65, rgba(0, 0, 0, 0.65));
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            opacity: 0.6;
          }
        }

        .featured-apps-main-item-content-money {
          display: flex;
          align-items: center;
          gap: 4px;
          > p {
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            opacity: 0.6;
            margin: 0;
          }
          > span {
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: Poppins;
            font-size: 32px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 48px */
            opacity: 1;
          }
        }
      }
    }
  }
}
