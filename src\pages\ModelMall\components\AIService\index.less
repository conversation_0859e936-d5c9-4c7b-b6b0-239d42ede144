.ai-service-section {
  width: 100%;
  background: var(---fill-7, #f5f7fa);

  .service-container {
    width: 1240px;
    margin: 0 auto;
    padding: 52px 0;
    .service-title {
      width: 100%;
      padding-bottom: 12px;
      > h2 {
        width: 100%;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 48px */
      }
      .service-title-desc {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 8px;
        > p {
          color: var(---85, rgba(0, 0, 0, 0.85));

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
        }
        .service-title-desc-btn {
          color: var(---Brand1-5, #37f);
          margin-left: 4px;
          cursor: pointer;
          > img:nth-child(2) {
            opacity: 0.8;
          }
          > img:nth-child(3) {
            opacity: 0.6;
          }
        }
      }
    }
    .service-content {
      margin-top: 32px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      .service-content-item {
        width: 100%;
        height: 100%;
        border-radius: 12px;
        background: var(---, #fff);
        padding: 16px;
        overflow: hidden;
        cursor: pointer;
        .service-content-item-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 64px;
          gap: 16px;
          .item-img {
            width: 64px;
            height: 64px;
            border-radius: 10px;
            overflow: hidden;
            background: var(---, #fff);
            > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .item-info {
            flex: 1;
            height: 64px;
            > p {
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              color: var(---85, rgba(0, 0, 0, 0.85));
              text-overflow: ellipsis;

              /* h3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 18px;
              font-style: normal;
              font-weight: 600;
              line-height: 150%; /* 27px */
            }
            .item-tags {
              height: 24px;
              line-height: 24px;
              margin-top: 13px;
              display: flex;
              gap: 8px;
              > p {
                padding: 0 8px;
                border-radius: 4px;
                background: var(---04, rgba(0, 0, 0, 0.04));
                color: var(---45, rgba(0, 0, 0, 0.45));
                text-align: center;

                /* t3 */
                font-family: 'Alibaba PuHuiTi';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 150%; /* 21px */
              }
            }
          }
        }

        .service-content-item-bottom {
          height: 36px;
          .items-nums-wrap {
            display: flex;
            justify-content: space-between;
            height: 36px;
            align-items: center;
            margin-top: 24px;
          }
          .item-nums {
            height: 36px;
            display: flex;
            gap: 12px;
            > p {
              display: flex;
              align-items: center;
              color: var(---45, rgba(0, 0, 0, 0.45));

              /* t2 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
            }
            > img {
              width: 20px;
              height: 20px;
            }
          }
          .item-date {
            color: var(---25, rgba(0, 0, 0, 0.25));

            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
          }
        }
        .service-content-btns {
          width: 100%;
          display: none;
          gap: 12px;
          height: 36px;
          align-items: center;
          margin-top: 24px;
          .info-btn {
            width: 50%;
            height: 36px;
            border-radius: var(--, 12px);
            background: var(---, #fff);
            border-radius: 4px;
            border: 1px solid var(--fill-9, #e6eaf2);
            background: var(--fill-7, #f5f7fa);
            text-overflow: ellipsis;

            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
          .use-btn {
            width: 50%;
            height: 36px;
            border-radius: 4px;
            background: var(--Brand1-5, #006bff);
            overflow: hidden;
            color: var(---, #fff);
            text-overflow: ellipsis;

            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
        }
        &:hover {
          .service-content-item-bottom .items-nums-wrap {
            display: none;
          }
          .service-content-btns {
            display: flex;
          }
        }
      }
    }
  }
}
