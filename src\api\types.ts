// API 相关类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
}

// 错误响应类型
export interface ApiError {
  code: number;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role: 'admin' | 'user';
  roles?: string[]; // 角色数组
  permissions?: string[]; // 权限数组
  menuList?: MenuInfo[]; // 菜单列表
  createdAt: string;
  updatedAt: string;
}

// 菜单信息
export interface MenuInfo {
  id: string;
  name: string;
  path: string;
  icon?: string;
  parentId?: string;
  orderNum?: number;
  children?: MenuInfo[];
}

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
  requestId?: string; // 请求追踪ID
}

// 登录响应
export interface LoginResponse {
  token: string;
  user?: User;
  expiresAt?: number;
}

// 用户信息响应
export interface UserInfoResponse {
  user: User;
  roleKeyList: string[];
  permissionList: string[];
  menuList: MenuInfo[];
}
