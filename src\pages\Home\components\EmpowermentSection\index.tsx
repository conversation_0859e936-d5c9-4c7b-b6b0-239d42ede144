import React, { useState } from 'react';
import './index.less';

// 箭头图标组件
const ArrowIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18L15 12L9 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

interface EmpowermentItem {
  id: number;
  title: string;
  description: string;
  image: string;
}

const empowermentItems: EmpowermentItem[] = [
  {
    id: 1,
    title: '医疗赋能',
    description: '搭建多模态健康医疗大数据科研应用平台，构建标准数据层、科研专用数据库以及患者360全景数据库等多样化数据仓库，针对典型病种建立多模态专病数据库，开发部署数据统计分析工具，为科研人员提供高效的数据分析服务。基于开源模型研究针对心血管疾病、肿瘤疾病的专科大模型，为医生提供循证医学辅助决策，加速创新药物研发进程，推动专科医学进步与创新。研发基于医疗科研文献库的智能问答应用，为科研人员提供全方位的智能辅助。',
    image: '/images/home/<USER>'
  },
  {
    id: 2,
    title: '科研赋能',
    description: '提供强大的科研支撑平台，整合多源数据资源，构建智能化科研环境，加速科研成果转化。',
    image: '/images/home/<USER>'
  },
  {
    id: 3,
    title: '人才赋能',
    description: '建立完善的人才培养体系，提供专业技能培训，培育AI医疗领域专业人才。',
    image: '/images/home/<USER>'
  }
];

const EmpowermentSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState(1);

  const activeItem = empowermentItems.find(item => item.id === activeTab) || empowermentItems[0];

  return (
    <section className='empowerment-section'>
      <div className='empowerment-background'>
        <img 
          src='/images/home/<USER>' 
          alt='背景图片' 
          className='empowerment-bg-image'
        />
        <div className='empowerment-overlay'></div>
      </div>
      
      <div className='empowerment-container'>
        <div className='empowerment-header'>
          <h2 className='empowerment-title'>赋能中心</h2>
          <p className='empowerment-subtitle'>
            多元化业务<br />
            一站式解决方案
          </p>
        </div>

        <div className='empowerment-content'>
          <div className='empowerment-tabs'>
            {empowermentItems.map((item, index) => (
              <div 
                key={item.id}
                className={`empowerment-tab ${activeTab === item.id ? 'active' : ''}`}
                onClick={() => setActiveTab(item.id)}
              >
                <span className='tab-number'>0{index + 1}.</span>
                <span className='tab-title'>{item.title}</span>
                <ArrowIcon />
              </div>
            ))}
            <div className='empowerment-tab'>
              <span className='tab-number'>04.</span>
              <span className='tab-title'>产业赋能</span>
              <ArrowIcon />
            </div>
            <div className='empowerment-tab'>
              <span className='tab-number'>05.</span>
              <span className='tab-title'>安全赋能</span>
              <ArrowIcon />
            </div>
          </div>

          <div className='empowerment-cards'>
            <div className='empowerment-card active'>
              <div className='card-image'>
                <img src={activeItem.image} alt={activeItem.title} />
              </div>
              <div className='card-content'>
                <h3 className='card-title'>{activeItem.title}</h3>
                <p className='card-description'>{activeItem.description}</p>
              </div>
            </div>
            
            <div className='empowerment-card'>
              <div className='card-image'>
                <img src='/images/home/<USER>' alt='科研赋能' />
              </div>
              <div className='card-content'>
                <h3 className='card-title'>科研赋能</h3>
              </div>
            </div>
            
            <div className='empowerment-card'>
              <div className='card-image'>
                <img src='/images/home/<USER>' alt='人才赋能' />
              </div>
              <div className='card-content'>
                <h3 className='card-title'>人才赋能</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EmpowermentSection;
