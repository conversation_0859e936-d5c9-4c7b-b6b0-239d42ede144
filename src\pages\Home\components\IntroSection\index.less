@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.intro-section {
  width: 100%;
  padding: 80px 340px;
  background: #ffffff;

  .intro-container {
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    gap: 200px;
    align-items: center;
  }

  .intro-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;

    .intro-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .intro-title {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 500;
        font-size: 32px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.85);
        margin: 0;
      }

      .intro-description {
        display: flex;
        flex-direction: column;
        gap: 16px;

        p {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 400;
          font-size: 16px;
          line-height: 1.8;
          color: rgba(0, 0, 0, 0.65);
          margin: 0;
          text-align: justify;
        }
      }
    }

    .intro-arrow {
      flex-shrink: 0;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateX(5px);
      }
    }
  }

  .intro-visual {
    flex-shrink: 0;
    width: 470px;
    height: 640px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    .intro-bg-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .intro-section {
    padding: 60px 20px;

    .intro-container {
      flex-direction: column;
      gap: 40px;
      text-align: center;

      .intro-content {
        .intro-text {
          .intro-title {
            font-size: 28px;
          }

          .intro-description {
            p {
              font-size: 15px;
              text-align: left;
            }
          }
        }
      }

      .intro-visual {
        width: 100%;
        max-width: 470px;
        height: auto;
        aspect-ratio: 470/640;
      }
    }
  }
}

@media (max-width: @screen-md) {
  .intro-section {
    padding: 40px 16px;

    .intro-container {
      .intro-content {
        .intro-text {
          .intro-title {
            font-size: 24px;
          }

          .intro-description {
            p {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
