// 全局样式
@import './variables.less';
@import './mixins.less';

// 重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: @font-family;
  font-size: @font-size-base;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
}

// 链接样式
a {
  color: @primary-color;
  text-decoration: none;
  transition: color 0.3s;

  &:hover {
    color: lighten(@primary-color, 10%);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  .border-radius(4px);

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.flex-center {
  .center-flex();
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

// 间距工具类
.generate-spacing(@property, @prefix) {
  .@{prefix}-xs {
    @{property}: @padding-xs;
  }
  .@{prefix}-sm {
    @{property}: @padding-sm;
  }
  .@{prefix}-md {
    @{property}: @padding-md;
  }
  .@{prefix}-lg {
    @{property}: @padding-lg;
  }
  .@{prefix}-xl {
    @{property}: @padding-xl;
  }
}

.generate-spacing(padding, p);
.generate-spacing(margin, m);
.generate-spacing(padding-top, pt);
.generate-spacing(padding-bottom, pb);
.generate-spacing(padding-left, pl);
.generate-spacing(padding-right, pr);
.generate-spacing(margin-top, mt);
.generate-spacing(margin-bottom, mb);
.generate-spacing(margin-left, ml);
.generate-spacing(margin-right, mr);
