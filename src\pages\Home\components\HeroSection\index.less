.about-hero-section {
  display: flex;
  height: 100vh;
  padding: 351px 0 28px 0;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  align-self: stretch;
  background-image: url('/platform/images/about/hero-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .about-hero-section-container {
    width: 1240px;
    margin: 0 auto;
    .about-hero-section-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 64px;
      .about-hero-section-content-title {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 32px;
        > h1 {
          color: var(---Brand1-5, #37f);
          text-align: center;
          font-family: 'Alibaba PuHuiTi';
          font-size: 72px;
          font-style: normal;
          font-weight: 700;
          line-height: 120%; /* 86.4px */
        }
        .about-hero-section-content-title-line {
          width: 168px;
          height: 3px;
          background: var(---Brand1-5, #37f);
        }
      }
      .about-hero-section-content-description {
        > h2 {
          color: var(---, #fff);
          text-align: center;
          font-family: 'Alibaba PuHuiTi';
          font-size: 32px;
          font-style: normal;
          font-weight: 400;
          line-height: 120%; /* 38.4px */
        }
      }
    }
  }

  .about-hero-section-scroll-down {
    position: absolute;
    bottom: 28px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;

    > img {
      display: flex;
      width: 24px;
      height: 24px;
      justify-content: center;
      align-items: center;
    }

    > p {
      color: var(---, #fff);

      /* t3 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 21px */
    }
  }
}
