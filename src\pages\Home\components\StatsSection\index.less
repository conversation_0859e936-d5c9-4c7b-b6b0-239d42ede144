@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.stats-section {
  width: 100%;
  min-height: 120px;
  position: relative;
  overflow: hidden;
  background: #3377ff;

  .stats-background {
    position: absolute;
    top: -762px;
    left: 0;
    width: 100%;
    height: 1132px;
    z-index: 1;

    .background-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.4;
    }

    .background-overlay {
      display: none;
    }
  }

  .stats-container {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 0;
    width: 1240px;
    margin: 0 auto;
    gap: 28px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    opacity: 0;
    transform: translateY(30px);

    &.animate {
      animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  }

  .stat-number {
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 600;
    font-size: 44px;
    line-height: 1.5;
    text-align: center;
    color: #ffffff;
    margin: 0;
  }

  .stat-label {
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .stats-section {
    .stats-container {
      padding: 30px 60px;
      gap: 20px;
    }

    .stat-number {
      font-size: 36px;
    }

    .stat-label {
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .stats-section {
    .stats-container {
      flex-direction: column;
      padding: 20px 20px;
      gap: 24px;
    }

    .stat-number {
      font-size: 32px;
    }

    .stat-label {
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  .stats-section {
    .stat-number {
      font-size: 28px;
    }

    .stat-label {
      font-size: 12px;
    }
  }
}
