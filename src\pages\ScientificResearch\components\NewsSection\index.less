.newsSection {
  position: relative;
  height: 780px;
  background: #ffffff;

  .sectionContainer {
    max-width: 1923px;
    margin: 0 auto;
    height: 100%;
  }

  .newsBackground {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }

  .bgMain {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #eef5ff;
  }

  .bgWorld {
    position: absolute;
    right: 0px;
    top: 17px;
    width: 1031px;
    height: 1031px;
    opacity: 0.1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .newsContent {
    position: relative;
    z-index: 2;
    width: 1288px;
    margin: 0 auto;
    padding: 52px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .sectionTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    margin-bottom: 32px;
  }

  .newsGrid {
    display: flex;
    gap: 42px;
    padding: 24px;
    background: #fafcff;
    border-radius: 12px;
    backdrop-filter: blur(4px);
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
  }

  .featuredNews {
    width: 550px;
    height: 528px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    overflow: hidden;
    display: flex;
    padding: 24px;
    flex-direction: column;
  }

  .newsImage {
    flex: 1;
    background: #d9d9d9;
    display: flex;
    align-items: flex-end;
    margin-bottom: 10px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .newsInfo {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .newsTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
    margin: 0;
  }

  .newsContentText {
    color: var(---45, rgba(0, 0, 0, 0.45));
    font-family: 'Alibaba PuHuiTi';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 24px */
    margin: 0;
  }

  .newsDate {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    margin: 0;
  }

  .newsList {
    width: 648px;
    height: 528px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .newsItem {
    background: #ffffff;
    border-radius: 12px;
    padding: 10px 16px 0px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .newsItemTitle {
    height: 60px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
    margin: 0;
  }

  .newsItemDate {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    margin: 0;
  }

  .newsDivider {
    width: 100%;
    height: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.25);
    margin-top: auto;
    margin: 0;
  }
}
