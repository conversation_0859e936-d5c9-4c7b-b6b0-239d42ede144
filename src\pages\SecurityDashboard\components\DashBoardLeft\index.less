// @import '../../../styles/variables.less';

.dashboard-left {
    display: flex;
    flex-direction: column;

    // gap: 48px;

    .panel-card {
        padding: 20px;
        margin: 10px;

        .card-header {
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: -20px -20px 16px;
            padding: 12px 16px;
            background: linear-gradient(90deg, transparent 0%, rgba(0, 70, 120, 0.65) 50%, transparent 100%);
            border-radius: 12px;


            .card-icon {
                font-size: 16px;
                color: #00d4ff;
                text-shadow: 0 0 8px rgba(0, 212, 255, 0.8);

                >img {
                    width: 44px;
                }
            }

            .card-title-background {
                display: flex;
                height: 48px;
                padding: 8px 0;
                flex-direction: column;
                align-items: center;
                gap: 10px;
                flex: 1 0 0;
                background: linear-gradient(90deg, rgba(0, 114, 244, 0.00) 0%, rgba(0, 114, 244, 0.30) 51.54%, rgba(0, 114, 244, 0.00) 100%);
            }

            .card-title {
                color: #FFF;
                text-align: center;
                font-feature-settings: 'liga' off, 'clig' off;
                text-shadow: 0 -3px 6px rgba(0, 102, 255, 0.60);
                font-family: PangMenZhengDao-3;
                font-size: 24px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                /* 100% */
                letter-spacing: 2.4px;
                // background: linear-gradient(180deg, #fff 0%,#D3F0FF 20%, #6FCFFF 100%);
            }

            .control-btn {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.7);
                cursor: pointer;
            }

            //   .control-btn:hover { color: #00d4ff; }
        }

        &.training-analysis {
            .metric-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;


                .metric-item {
                    padding: 15px 0px;
                    border-radius: 10px;
                    display: flex;

                    .metric-img {
                        width: 52px;
                        height: 52px;
                        margin-right: 9px;
                    }

                    .metric-label {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.7);
                        margin-bottom: 8px;
                    }

                    .metric-value {
                        font-size: 24px;
                        font-weight: 700;
                        color: #fff;
                        font-family: 'Courier New', monospace;
                        display: inline-block;
                    }

                    .metric-value.highlight {
                        color: #00d4ff;
                        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
                    }

                    .metric-unit {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.6);
                        margin-left: 4px;
                        display: inline-block;
                    }
                }
            }
        }

        &.resource-analysis {
            .resource-meters {
                display: flex;
                justify-content: space-around;
                gap: 15px;

                .meter-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;

                    .meter-label {
                        font-size: 12px;
                        color: rgba(255, 255, 255, 0.8);
                        font-weight: 500;
                    }

                    .circular-meter {
                        position: relative;
                        width: 110px;
                        height: 90px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .meter-value {
                            position: absolute;
                            bottom: 8px;
                            font-size: 22px;
                            font-weight: 700;
                            color: #fff;
                            font-family: 'Courier New', monospace;
                            z-index: 2;
                        }

                        .meter-unit {
                            font-size: 12px;
                            color: rgba(255, 255, 255, 0.6);
                            margin-left: 2px;
                        }

                        .meter-circle {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            transform: rotate(-90deg);
                            clip-path: inset(0 0 50% 0);
                        }
                    }
                }
            }
        }

        &.model-ranking {
            .model-ranking-table {
                .ant-table {
                    background: transparent;
                    color: #fff;
                }

                .table-content {
                    font-family: "Alibaba PuHuiTi";
                    font-style: normal;
                    line-height: normal;

                }

                .table-name {
                    color: #E5F0FF;
                    font-size: 14px;
                    font-weight: 400;
                    letter-spacing: 0.84px;
                }

                .table-num {
                    color: #FFF;
                    font-size: 18px;
                    font-weight: 700;
                    letter-spacing: 1.08px;
                }

                .table-unit {
                    color: #E5F0FF;
                    font-size: 18px;
                    font-weight: 400;
                    letter-spacing: 1.08px;
                    margin-left: 4px;
                }


                .ant-table-thead>tr>th {
                    background: transparent;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
                    color: rgba(255, 255, 255, 0.6);
                    font-size: 12px;
                    font-weight: normal;
                    padding: 8px 0;
                }

                .ant-table-tbody>tr>td {
                    background: transparent;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                    padding: 12px 0;
                }

                .ant-table-tbody>tr:hover>td {
                    background: rgba(0, 212, 255, 0.1);
                }

                .ant-table-tbody>tr:last-child>td {
                    border-bottom: none;
                }
            }
        }
    }
}