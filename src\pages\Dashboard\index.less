.dashboard-page {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  // 左侧导航栏
  .sidebar {
    width: 240px;
    background-color: #001529;
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

    .logo {
      padding: 20px;
      font-size: 18px;
      font-weight: 600;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: white;
    }

    .nav-menu {
      padding: 20px 0;

      .nav-item {
        padding: 12px 24px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.85);
        font-size: 14px;
        border-left: 3px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: white;
        }

        &.active {
          background-color: #1890ff;
          color: white;
          border-left-color: #40a9ff;
        }
      }
    }
  }

  // 主内容区域
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 顶部栏
    .top-bar {
      height: 64px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .greeting {
        h2 {
          margin: 0;
          font-size: 18px;
          color: #333;
          font-weight: 500;
        }
      }

      .user-info {
        color: #666;
        font-size: 14px;
      }
    }

    // 数据统计卡片
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      padding: 24px;
      max-width: 100%;

      .stats-card {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        .stats-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .stats-label {
            font-size: 14px;
            color: #666;
          }

          .stats-trend {
            .trend-chart {
              width: 80px;
              height: 24px;
            }
          }
        }

        .stats-number {
          font-size: 32px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .stats-change {
          font-size: 12px;
          color: #52c41a;

          &:contains('↓') {
            color: #f5222d;
          }
        }
      }

      .info-card {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        .info-header {
          margin-bottom: 16px;

          .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-right: 12px;
          }

          .info-subtitle {
            font-size: 14px;
            color: #666;
          }
        }

        .info-content {
          .risk-item {
            font-size: 12px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    // 图表区域
    .charts-section {
      flex: 1;
      padding: 0 24px 24px;
      display: flex;
      flex-direction: column;

      .chart-container {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        h3 {
          margin: 0 0 20px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          .chart-controls {
            display: flex;
            gap: 8px;

            .control-btn {
              padding: 4px 12px;
              border: 1px solid #d9d9d9;
              background-color: white;
              border-radius: 4px;
              font-size: 12px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #40a9ff;
                color: #1890ff;
              }

              &.active {
                background-color: #1890ff;
                border-color: #1890ff;
                color: white;
              }
            }
          }
        }
      }

      // 主趋势图
      .main-chart {
        margin-bottom: 20px;
        min-height: 300px;

        .chart-content {
          .chart-value {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
          }

          .trend-graph {
            height: 240px;

            .main-trend-svg {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      // 底部图表区域
      .bottom-charts {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;

        // 算力资源利用率
        .utilization-chart {
          .progress-circles {
            display: flex;
            justify-content: space-around;
            align-items: center;

            .progress-circle {
              text-align: center;

              .circle-wrapper {
                position: relative;
                width: 100px;
                height: 100px;
                margin-bottom: 12px;

                .progress-svg {
                  width: 100%;
                  height: 100%;
                  transform: rotate(-90deg);
                }

                .progress-text {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);

                  .progress-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #333;
                  }
                }
              }

              .progress-label {
                font-size: 14px;
                color: #666;

                div:first-child {
                  font-weight: 600;
                  margin-bottom: 4px;
                }
              }
            }
          }
        }

        // 地图图表
        .map-chart {
          .map-content {
            height: 200px;

            .map-placeholder {
              width: 100%;
              height: 100%;

              .china-map {
                width: 100%;
                height: 100%;
              }
            }
          }
        }

        // 资源调度来源
        .source-chart {
          .pie-chart-container {
            display: flex;
            align-items: center;
            gap: 20px;

            .pie-chart {
              width: 120px;
              height: 120px;
            }

            .pie-legend {
              flex: 1;

              .legend-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;
                color: #666;

                .legend-color {
                  width: 12px;
                  height: 12px;
                  border-radius: 2px;
                  margin-right: 8px;
                }
              }
            }
          }
        }

        // 任务成本TOP8
        .top-tasks {
          .task-list {
            .task-item {
              display: flex;
              align-items: center;
              padding: 8px 0;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .task-name {
                width: 120px;
                font-size: 12px;
                color: #333;
                margin-right: 12px;
              }

              .task-progress {
                flex: 1;
                height: 6px;
                background-color: #f0f0f0;
                border-radius: 3px;
                margin-right: 12px;
                overflow: hidden;

                .progress-bar {
                  height: 100%;
                  background-color: #1890ff;
                  border-radius: 3px;
                  transition: width 0.3s ease;
                }
              }

              .task-value {
                font-size: 12px;
                color: #666;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .main-content {
      .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }

      .charts-section {
        .bottom-charts {
          grid-template-columns: 1fr;
        }
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;

    .sidebar {
      width: 100%;
      height: auto;

      .nav-menu {
        display: flex;
        padding: 0;

        .nav-item {
          flex: 1;
          text-align: center;
          border-left: none;
          border-bottom: 3px solid transparent;

          &.active {
            border-bottom-color: #40a9ff;
            border-left-color: transparent;
          }
        }
      }
    }

    .main-content {
      .stats-cards {
        grid-template-columns: 1fr;
        padding: 16px;
        gap: 16px;
      }

      .charts-section {
        padding: 0 16px 16px;

        .chart-container {
          padding: 16px;
        }

        .bottom-charts {
          gap: 16px;

          .utilization-chart {
            .progress-circles {
              flex-direction: column;
              gap: 20px;
            }
          }

          .source-chart {
            .pie-chart-container {
              flex-direction: column;
              text-align: center;
            }
          }
        }
      }
    }
  }

  // 作业管理页面样式
  .jobs-content {
    padding: 24px;

    .jobs-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 24px;

      .job-stat-card {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .job-number {
          font-size: 32px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .job-trend {
          font-size: 12px;
          color: #52c41a;
        }
      }
    }

    .jobs-table-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #333;
        }

        .new-job-btn {
          background-color: #1890ff;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #40a9ff;
          }
        }
      }

      .jobs-table {
        .table-row {
          display: grid;
          grid-template-columns: 2fr 1fr 1.5fr 1fr 1.5fr;
          padding: 16px 24px;
          border-bottom: 1px solid #f0f0f0;
          align-items: center;

          &.header {
            background-color: #fafafa;
            font-weight: 600;
            color: #333;
          }

          .table-cell {
            font-size: 14px;
            color: #333;

            &.status-running {
              color: #1890ff;
              font-weight: 500;
            }

            &.status-completed {
              color: #52c41a;
              font-weight: 500;
            }

            &.status-failed {
              color: #f5222d;
              font-weight: 500;
            }

            .action-btn {
              background-color: transparent;
              border: 1px solid #d9d9d9;
              padding: 4px 8px;
              border-radius: 4px;
              margin-right: 8px;
              cursor: pointer;
              font-size: 12px;
              transition: all 0.3s;

              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  // 费用管理页面样式
  .costs-content {
    padding: 24px;

    .cost-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 24px;

      .cost-card {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .cost-amount {
          font-size: 32px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .cost-trend {
          font-size: 12px;
          color: #52c41a;
        }
      }
    }

    .cost-breakdown {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 24px;

      .breakdown-chart,
      .cost-trend-chart {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        h3 {
          margin: 0 0 20px 0;
          font-size: 16px;
          color: #333;
        }

        .trend-graph {
          height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .cost-details {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      h3 {
        margin: 0;
        padding: 20px 24px;
        font-size: 16px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
      }

      .cost-table {
        .table-row {
          display: grid;
          grid-template-columns: 1fr 1.5fr 1fr 1fr 1fr;
          padding: 16px 24px;
          border-bottom: 1px solid #f0f0f0;
          align-items: center;

          &.header {
            background-color: #fafafa;
            font-weight: 600;
            color: #333;
          }

          .table-cell {
            font-size: 14px;
            color: #333;

            &.status-paid {
              color: #52c41a;
              font-weight: 500;
            }

            &.status-pending {
              color: #faad14;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // 用户管理页面样式
  .users-content {
    padding: 24px;

    .users-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 24px;

      .user-stat-card {
        background-color: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        h3 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .user-number {
          font-size: 32px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .user-trend {
          font-size: 12px;
          color: #52c41a;
        }
      }
    }

    .users-table-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 24px;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #333;
        }

        .table-actions {
          display: flex;
          gap: 12px;
          align-items: center;

          .search-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;

            &:focus {
              outline: none;
              border-color: #1890ff;
            }
          }

          .add-user-btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;

            &:hover {
              background-color: #40a9ff;
            }
          }
        }
      }

      .users-table {
        .table-row {
          display: grid;
          grid-template-columns: 1.5fr 2fr 1fr 1fr 1.5fr 1.5fr;
          padding: 16px 24px;
          border-bottom: 1px solid #f0f0f0;
          align-items: center;

          &.header {
            background-color: #fafafa;
            font-weight: 600;
            color: #333;
          }

          .table-cell {
            font-size: 14px;
            color: #333;

            &.status-active {
              color: #52c41a;
              font-weight: 500;
            }

            &.status-inactive {
              color: #999;
              font-weight: 500;
            }

            .action-btn {
              background-color: transparent;
              border: 1px solid #d9d9d9;
              padding: 4px 8px;
              border-radius: 4px;
              margin-right: 8px;
              cursor: pointer;
              font-size: 12px;
              transition: all 0.3s;

              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }
            }
          }
        }
      }
    }

    .user-activity {
      background-color: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        color: #333;
      }

      .activity-chart {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
