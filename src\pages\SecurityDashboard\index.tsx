import React, { useState,  useEffect } from 'react';
import './index.less';
import Header from './components/Header';
import DashboardLeft from './components/DashBoardLeft';
import DashboardRight from './components/DashBoardRight';


const SecurityDashboard: React.FC = () => {
  const [showMainTopType, setShowMainTopType] = useState<'week' | 'month' | 'year'>('week');

  const [currentTime, setCurrentTime] = useState(new Date());

  // 核心指标数据
  const [metrics, setMetrics] = useState({
    // 顶部核心指标
    totalAlerts: 202,
    processedEvents: 926,
    resolvedIssues: 328,

    // 模型训练分析
    currentTasks: 849,
    queueWaiting: 87.5,

    // 算力资源分析
    cpuUsage: 73,
    gpuUsage: 52,
    memoryUsage: 82,

    // 模型调用排行
    modelRanking: [
      { name: '威胁检测-v2', calls: 153, percentage: 0.96, trend: 8.976 },
      { name: 'OCR文字识别-v1', calls: 129, percentage: 0.92, trend: 7.516 },
      { name: '自然语言-v1', calls: 233, percentage: 0.16, trend: 6.455 },
      { name: '分析-v5', calls: 89, percentage: 0.26, trend: 5.532 },
      { name: '分析-v1', calls: 278, percentage: 0.12, trend: 3.038 },
    ],

    // 安全运营合规
    complianceScore: 96,
    securityLevel: 19,
    riskLevel: 2.9,
    responseTime: 92,

    // 应用订阅趋势
    trendData: [
      { time: '9.15', value: 45 },
      { time: '9.16', value: 52 },
      { time: '9.17', value: 48 },
      { time: '9.18', value: 65 },
      { time: '9.19', value: 58 },
      { time: '9.20', value: 72 },
      { time: '9.21', value: 68 },
      { time: '9.22', value: 75 },
      { time: '9.23', value: 82 },
      { time: '9.24', value: 78 },
    ],

    // 热门应用TOP
    topApps: [
      { rank: 1, name: '全景监控分析', usage: 9.976 },
      { rank: 2, name: '威胁情报检测', usage: 3.678 },
      { rank: 3, name: '高级威胁分析', usage: 2.436 },
      { rank: 4, name: '安全态势感知', usage: 1.892 },
      { rank: 5, name: '情报数据分析', usage: 852 },
    ],
  });

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 模拟数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        totalAlerts: prev.totalAlerts + Math.floor(Math.random() * 2),
        processedEvents: prev.processedEvents + Math.floor(Math.random() * 3),
        cpuUsage: Math.round(Math.max(50, Math.min(90, prev.cpuUsage + (Math.random() - 0.5) * 4))),
        gpuUsage: Math.round(Math.max(40, Math.min(80, prev.gpuUsage + (Math.random() - 0.5) * 3))),
        memoryUsage: Math.round(
          Math.max(60, Math.min(95, prev.memoryUsage + (Math.random() - 0.5) * 2))
        ),
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);
  return (
    <div className='security-dashboard'>
      {/* 顶部标题区域 */}
      <Header />

      {/* 中间区域 */}
      <div className='security-dashboard-content'>
        <div className='security-dashboard-date-info'>
          <div className='security-dashboard-date-info-left'>
            <img
              src='/platform/images/security-dashboard/security-dashboard-date-info-left.svg'
              alt=''
            />
          </div>
          <div className='security-dashboard-date-info-center'>
            <div className='security-dashboard-date-info-center-date-picker'>
              <p
                onClick={() => setShowMainTopType('week')}
                className={showMainTopType === 'week' ? 'active' : ''}
              >
                周
              </p>
              <p
                onClick={() => setShowMainTopType('month')}
                className={showMainTopType === 'month' ? 'active' : ''}
              >
                月
              </p>
              <p
                onClick={() => setShowMainTopType('year')}
                className={showMainTopType === 'year' ? 'active' : ''}
              >
                年
              </p>
            </div>

            <div className='security-dashboard-date-info-center-content'>
              <div className='security-dashboard-date-info-center-content-item'>
                <div className='security-dashboard-date-info-center-content-item-left'>
                  <img
                    src='/platform/images/security-dashboard/date-info-center-content-item.svg'
                    alt=''
                  />
                </div>
                <div className='security-dashboard-date-info-center-content-item-right'>
                  <p className='content-item-right-number'>
                    <span>202</span>个
                  </p>
                  <p className='content-item-right-title'>模型总数</p>
                </div>
              </div>
              <div className='security-dashboard-date-info-center-content-item'>
                <div className='security-dashboard-date-info-center-content-item-left'>
                  <img
                    src='/platform/images/security-dashboard/date-info-center-content-item.svg'
                    alt=''
                  />
                </div>
                <div className='security-dashboard-date-info-center-content-item-right'>
                  <p className='content-item-right-number'>
                    <span>926</span>个
                  </p>
                  <p className='content-item-right-title'>作业任务总数</p>
                </div>
              </div>
              <div className='security-dashboard-date-info-center-content-item'>
                <div className='security-dashboard-date-info-center-content-item-left'>
                  <img
                    src='/platform/images/security-dashboard/date-info-center-content-item.svg'
                    alt=''
                  />
                </div>
                <div className='security-dashboard-date-info-center-content-item-right'>
                  <p className='content-item-right-number'>
                    <span>328</span>个
                  </p>
                  <p className='content-item-right-title'>应用总数</p>
                </div>
              </div>
            </div>
          </div>
          <div className='security-dashboard-date-info-right'>
            <img
              src='/platform/images/security-dashboard/security-dashboard-date-info-right.svg'
              alt=''
            />
          </div>
        </div>

        <div className='security-dashboard-content-main'>
          <img src='/platform/images/security-dashboard/main-center-bg.png' alt='' />
        </div>
        {/* 左侧数据栏：覆盖在中间区域左侧 */}
        <div className='DashBoardSideLeft'>
          <DashboardLeft metrics={metrics} />
        </div>
        <div className='DashBoardSideRight'>
          <DashboardRight metrics={metrics} />
        </div>
      </div>

      
    </div>
  );
};

export default SecurityDashboard;
