@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.application-section {
  width: 100%;
  padding: 52px 340px 80px;
  background: #ffffff;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    z-index: 1;
  }

  .application-container {
    position: relative;
    z-index: 2;
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
  }

  .application-header {
    text-align: center;
    padding: 2px 525px;

    .application-title {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 500;
      font-size: 32px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.85);
      margin: 0;
    }
  }

  .application-content {
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;

    .application-main-card {
      width: 780px;
      height: 400px;
      border-radius: 8px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;

      .main-card-image {
        width: 100%;
        height: 100%;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .main-card-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(180deg, rgba(22, 33, 56, 0) 53%, rgba(22, 33, 56, 0.55) 100%);
        }
      }

      .main-card-content {
        position: absolute;
        bottom: 32px;
        left: 32px;
        right: 32px;
        z-index: 2;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .main-card-tag {
          span {
            display: inline-block;
            padding: 2px 6px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 500;
            font-size: 20px;
            line-height: 1.5;
            color: #ffffff;
          }
        }

        .main-card-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 28px;
          line-height: 1.5;
          color: #ffffff;
          margin: 0;
        }
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      }
    }

    .application-side-cards {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .side-card {
        width: 624px;
        height: 320px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;

        .side-card-image {
          width: 100%;
          height: 100%;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .side-card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(22, 33, 56, 0) 53%, rgba(22, 33, 56, 0.55) 100%);
          }
        }

        .side-card-content {
          position: absolute;
          bottom: 25.6px;
          left: 25.6px;
          right: 25.6px;
          z-index: 2;
          display: flex;
          flex-direction: column;
          gap: 9.6px;

          .side-card-tag {
            span {
              display: inline-block;
              padding: 1.6px 4.8px;
              background: rgba(255, 255, 255, 0.8);
              border-radius: 1.6px;
              font-family: 'Alibaba PuHuiTi', @font-family;
              font-weight: 500;
              font-size: 16px;
              line-height: 1.5;
              color: #ffffff;
            }
          }

          .side-card-title {
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 500;
            font-size: 22.4px;
            line-height: 1.5;
            color: #ffffff;
            margin: 0;
          }
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .application-section {
    padding: 40px 20px 60px;

    .application-header {
      padding: 2px 20px;

      .application-title {
        font-size: 28px;
      }
    }

    .application-content {
      flex-direction: column;
      gap: 30px;

      .application-main-card {
        width: 100%;
        max-width: 780px;
        height: auto;
        aspect-ratio: 780/400;
      }

      .application-side-cards {
        width: 100%;
        flex-direction: row;
        justify-content: center;
        gap: 20px;

        .side-card {
          width: 100%;
          max-width: 300px;
          height: auto;
          aspect-ratio: 624/320;
        }
      }
    }
  }
}

@media (max-width: @screen-md) {
  .application-section {
    padding: 30px 16px 40px;

    .application-header {
      .application-title {
        font-size: 24px;
      }
    }

    .application-content {
      .application-side-cards {
        flex-direction: column;
        align-items: center;

        .side-card {
          max-width: 400px;
        }
      }
    }
  }
}
