// 存储工具类
export class StorageManager {
  private static instance: StorageManager;
  private readonly prefix: string;

  private constructor(prefix: string = 'ai_platform_') {
    this.prefix = prefix;
  }

  static getInstance(prefix?: string): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager(prefix);
    }
    return StorageManager.instance;
  }

  // 生成带前缀的key
  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  // localStorage 操作
  localStorage = {
    set: <T>(key: string, value: T): boolean => {
      try {
        const serialized = JSON.stringify(value);
        window.localStorage.setItem(this.getKey(key), serialized);
        return true;
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error);
        return false;
      }
    },

    get: <T>(key: string, defaultValue?: T): T | null => {
      try {
        const item = window.localStorage.getItem(this.getKey(key));
        return item ? JSON.parse(item) : defaultValue || null;
      } catch (error) {
        console.error(`Error getting localStorage key "${key}":`, error);
        return defaultValue || null;
      }
    },

    remove: (key: string): boolean => {
      try {
        window.localStorage.removeItem(this.getKey(key));
        return true;
      } catch (error) {
        console.error(`Error removing localStorage key "${key}":`, error);
        return false;
      }
    },

    clear: (): boolean => {
      try {
        const keys = Object.keys(window.localStorage);
        keys.forEach(key => {
          if (key.startsWith(this.prefix)) {
            window.localStorage.removeItem(key);
          }
        });
        return true;
      } catch (error) {
        console.error('Error clearing localStorage:', error);
        return false;
      }
    },

    exists: (key: string): boolean => {
      return window.localStorage.getItem(this.getKey(key)) !== null;
    },
  };

  // sessionStorage 操作
  sessionStorage = {
    set: <T>(key: string, value: T): boolean => {
      try {
        const serialized = JSON.stringify(value);
        window.sessionStorage.setItem(this.getKey(key), serialized);
        return true;
      } catch (error) {
        console.error(`Error setting sessionStorage key "${key}":`, error);
        return false;
      }
    },

    get: <T>(key: string, defaultValue?: T): T | null => {
      try {
        const item = window.sessionStorage.getItem(this.getKey(key));
        return item ? JSON.parse(item) : defaultValue || null;
      } catch (error) {
        console.error(`Error getting sessionStorage key "${key}":`, error);
        return defaultValue || null;
      }
    },

    remove: (key: string): boolean => {
      try {
        window.sessionStorage.removeItem(this.getKey(key));
        return true;
      } catch (error) {
        console.error(`Error removing sessionStorage key "${key}":`, error);
        return false;
      }
    },

    clear: (): boolean => {
      try {
        const keys = Object.keys(window.sessionStorage);
        keys.forEach(key => {
          if (key.startsWith(this.prefix)) {
            window.sessionStorage.removeItem(key);
          }
        });
        return true;
      } catch (error) {
        console.error('Error clearing sessionStorage:', error);
        return false;
      }
    },

    exists: (key: string): boolean => {
      return window.sessionStorage.getItem(this.getKey(key)) !== null;
    },
  };

  // 获取存储使用情况
  getStorageInfo() {
    const getStorageSize = (storage: Storage, prefix: string) => {
      let total = 0;
      let count = 0;
      const keys = Object.keys(storage);

      keys.forEach(key => {
        if (key.startsWith(prefix)) {
          total += key.length + (storage.getItem(key)?.length || 0);
          count++;
        }
      });

      return { total, count };
    };

    const localStorage = getStorageSize(window.localStorage, this.prefix);
    const sessionStorage = getStorageSize(window.sessionStorage, this.prefix);

    return {
      localStorage: {
        size: localStorage.total,
        count: localStorage.count,
        sizeInKB: Math.round((localStorage.total / 1024) * 100) / 100,
      },
      sessionStorage: {
        size: sessionStorage.total,
        count: sessionStorage.count,
        sizeInKB: Math.round((sessionStorage.total / 1024) * 100) / 100,
      },
    };
  }

  // 数据导出
  export(): Record<string, any> {
    const data: Record<string, any> = {};

    // 导出 localStorage 数据
    const localKeys = Object.keys(window.localStorage);
    localKeys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        const cleanKey = key.replace(this.prefix, '');
        try {
          data[cleanKey] = JSON.parse(window.localStorage.getItem(key) || '');
        } catch {
          data[cleanKey] = window.localStorage.getItem(key);
        }
      }
    });

    return data;
  }

  // 数据导入
  import(data: Record<string, any>): boolean {
    try {
      Object.entries(data).forEach(([key, value]) => {
        this.localStorage.set(key, value);
      });
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
}

// 创建默认实例
export const storage = StorageManager.getInstance();

// 常用的存储key
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  USER_SETTINGS: 'user_settings',
  THEME: 'theme',
  LANGUAGE: 'language',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
  RECENT_ROUTES: 'recent_routes',
  FORM_DRAFTS: 'form_drafts',
} as const;
