@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.news-section {
  width: 100%;
  padding: 52px 340px 80px;
  background: #ffffff;

  .news-container {
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
  }

  .news-header {
    text-align: center;
    padding: 2px 525px;

    .section-title {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 500;
      font-size: 32px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.85);
      margin: 0;
    }
  }

  .news-content {
    display: flex;
    align-self: stretch;
    height: 400px;
    background: linear-gradient(89deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.8) 100%);
    backdrop-filter: blur(40px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .news-main-image {
    flex-shrink: 0;
    width: 418.66px;
    height: 100%;
    border-radius: 20px 0 0 20px;
    overflow: hidden;

    .main-news-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .news-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px 12px 24px 24px;

    .news-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 24px 32px 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.02);
      }

      &.featured {
        padding: 24px 32px 12px 12px;
        border-right: 1px solid rgba(0, 0, 0, 0.08);

        .news-item-content {
          .news-title {
            font-size: 20px;
          }

          .news-description {
            display: block;
          }
        }
      }

      .news-item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .news-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 16px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          margin: 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .news-description {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.6;
          color: rgba(0, 0, 0, 0.65);
          margin: 0;
          display: none;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }

      .news-item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
        margin-top: 12px;

        .news-date {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 12px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .news-section {
    padding: 40px 20px 60px;

    .news-header {
      padding: 2px 20px;

      .section-title {
        font-size: 28px;
      }
    }

    .news-content {
      flex-direction: column;
      height: auto;

      .news-main-image {
        width: 100%;
        height: 200px;
        border-radius: 20px 20px 0 0;
      }

      .news-list {
        padding: 20px;

        .news-item {
          &.featured {
            padding: 20px;
            border-right: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
          }
        }
      }
    }
  }
}

@media (max-width: @screen-md) {
  .news-section {
    padding: 30px 16px 40px;

    .news-header {
      .section-title {
        font-size: 24px;
      }
    }

    .news-content {
      .news-list {
        padding: 16px;

        .news-item {
          padding: 16px;

          &.featured {
            padding: 16px;

            .news-item-content {
              .news-title {
                font-size: 18px;
              }
            }
          }

          .news-item-content {
            .news-title {
              font-size: 14px;
            }

            .news-description {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
