import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import DynamicHeader from '@/components/DynamicHeader';
import { managementMenu } from '@/config/menuConfig';
import './index.less';

const ManagementPage: React.FC = () => {
  const location = useLocation();
  const isManagementRoot = location.pathname === '/platform/management';

  return (
    <div className='management-page'>
      <DynamicHeader
        menuConfig={managementMenu}
        showBackground={true}
        className='dark-theme'
        showSearch={false}
        showActions={false}
      />

      {isManagementRoot ? (
        <div className='management-content'>
          <div className='management-hero'>
            <h1>管理中心</h1>
            <p>这里展示了管理后台专用的菜单配置</p>
          </div>

          <div className='management-features'>
            <div className='feature-card'>
              <h3>仪表盘</h3>
              <p>查看系统整体运行状况和关键指标</p>
            </div>

            <div className='feature-card'>
              <h3>用户管理</h3>
              <p>管理平台用户、权限和角色分配</p>
            </div>

            <div className='feature-card'>
              <h3>资源管理</h3>
              <p>监控和管理计算资源、存储等</p>
            </div>

            <div className='feature-card'>
              <h3>数据分析</h3>
              <p>查看使用统计、性能分析等数据</p>
            </div>

            <div className='feature-card'>
              <h3>系统设置</h3>
              <p>配置系统参数、安全设置等</p>
            </div>
          </div>
        </div>
      ) : (
        <div className='management-content'>
          <Outlet />
        </div>
      )}
    </div>
  );
};

export default ManagementPage;
