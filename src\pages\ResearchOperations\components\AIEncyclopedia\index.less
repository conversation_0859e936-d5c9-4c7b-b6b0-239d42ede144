.ai-encyclopedia-page {
  width: 100%;
  padding: 52px 0;
  background: #eef5ff;
  .ai-encyclopedia-container {
    width: 1240px;
    margin: 0 auto;
    .ai-encyclopedia-title {
      width: 100%;
      h2 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 35.495px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 53.242px */
      }
    }
    .ai-encyclopedia-content {
      width: 100%;
      margin-top: 35px;

      .ai-encyclopedia-keys {
        width: 100%;
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .keys-container {
          display: flex;
          gap: 24px;
          .key-item {
            color: var(---65, rgba(0, 0, 0, 0.65));
            text-align: center;
            font-family: 'Ali<PERSON><PERSON> PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
          }

          .active {
            color: var(---Brand1-5, #37f);
          }
        }
        .find-more {
          display: flex;
          align-items: center;
          height: 24px;
          gap: 4px;
          color: var(---Brand1-5, #37f);
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
          > img {
            width: 20px;
            height: 20px;
            aspect-ratio: 1/1;
          }
        }
      }

      .ai-encyclopedia-info {
        width: 100%;
        margin-top: 24px;
        display: flex;
        gap: 20px;
        height: 349px;

        .ai-encyclopedia-info-item {
          flex: 1;
          border-radius: 12px;
          border: 1px solid var(---08, rgba(0, 0, 0, 0.08));
          background: #fff;
          overflow: hidden;

          /* 卡片投影 */
          box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
          display: flex;
          flex-direction: column;
          .ai-encyclopedia-info-item-img {
            width: 100%;
            height: 50%;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .ai-encyclopedia-info-item-content {
            width: 100%;
            height: 50%;
            padding: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .info-title {
              > h4 {
                height: 30px;
                align-self: stretch;
                color: var(---85, rgba(0, 0, 0, 0.85));
                font-family: 'Alibaba PuHuiTi';
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: 150%; /* 30px */
              }
              > p {
                margin-top: 6px;
                display: flex;
                justify-content: space-between;
                color: var(---45, rgba(0, 0, 0, 0.45));
                font-family: 'Alibaba PuHuiTi';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 150%; /* 21px */
              }
            }
            .info-desc {
              height: 62px;
              color: var(---65, rgba(0, 0, 0, 0.65));
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
            }
          }
        }
      }
    }
  }
}
