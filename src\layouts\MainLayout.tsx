import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import './MainLayout.less';

interface MainLayoutProps {
  // 可以接收一些全局配置props
}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const location = useLocation();

  // 根据路由判断Header是否需要背景
  const shouldShowHeaderBackground = () => {
    // 首页不显示背景，其他页面显示
    return location.pathname !== '/';
  };

  // 需要隐藏Header的页面（如登录页）
  const shouldHideHeader = () => {
    const hideHeaderPaths = ['/login', '/404', '/403'];
    return hideHeaderPaths.includes(location.pathname);
  };

  // 需要隐藏Footer的页面
  const shouldHideFooter = () => {
    const hideFooterPaths = ['/login', '/404', '/403', '/platform/management/dashboard'];
    return hideFooterPaths.includes(location.pathname);
  };

  return (
    <div className='main-layout'>
      {!shouldHideHeader() && <Header showBackground={shouldShowHeaderBackground()} />}

      <main className='main-content'>
        <Outlet />
      </main>

      {!shouldHideFooter() && <Footer />}
    </div>
  );
};

export default MainLayout;
