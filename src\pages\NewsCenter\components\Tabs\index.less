.news-center-tabs {
  width: 100%;
  .news-center-tabs-container {
    width: 1240px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .news-center-tabs-left {
      position: relative;
      display: flex;
      align-items: center;
      height: 80px;
      gap: 40px;
      > p {
        cursor: pointer;
        height: 80px;
        display: flex;
        padding: 0 52px;
        justify-content: center;
        align-items: center;
        color: var(---45, rgba(0, 0, 0, 0.45));
        transition: color 0.3s ease;
        border-radius: 4px;
        overflow: hidden;

        /* h1 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 36px */
      }
      .active {
        color: var(---85, rgba(0, 0, 0, 0.85));
      }
      
      .animated-underline {
        position: absolute;
        bottom: 0px;
        height: 4px;
        background: var(---Brand1-5, #37f);
        border-radius: 0px 0px 4px 4px;
        transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                    width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1;
      }
    }
    .news-center-tabs-right {
      width: 400px;
      height: 36px;

      .ant-input-affix-wrapper {
        width: 100%;
        height: 100%;
        padding: 8px 12px;
        border-radius: 2px;
        border: 1px solid var(---12, rgba(0, 0, 0, 0.12));
        background: #fff;
      }
    }
  }
}
