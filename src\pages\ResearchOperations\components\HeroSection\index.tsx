import React from 'react';
import './index.less';
import { Button, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const heroInfo = [
  {
    id: 1,
    title: '专家教授',
    value: '50万+',
  },
  {
    id: 2,
    title: '细分领域研究方向',
    value: '1000+',
  },
  {
    id: 3,
    title: '技术成果',
    value: '200万+',
  },
  {
    id: 4,
    title: '科研院所前沿项目',
    value: '10万+',
  },
];

const HeroSection: React.FC = () => {
  return (
    <div className='research-operations-hero-section'>
      <div className='hero-section-content'>
        <h1>
          让<span>科技成果</span>转化更高效
        </h1>
        <div className='hero-section-search'>
          <div className='hot-search'>
            <p className='active'>按需求</p>
            <p>找成果</p>
            <p>找资讯</p>
            <p>找专家</p>
          </div>
          <div className='search-input-container'>
            <Input className='search-input' type='text' placeholder='请输入关键词' />
            <Button className='search-btn' icon={<SearchOutlined />}>
              搜索
            </Button>
          </div>
          <div className='release-btns'>
            <Button className='release-btn high-btn'>发布需求</Button>
            <Button className='release-btn'>发布成果</Button>
          </div>
        </div>
      </div>
      <div className='hero-info'>
        {heroInfo.map(item => (
          <div className='hero-info-item' key={item.id}>
            <p className='hero-info-item-value'>{item.value}</p>
            <p className='hero-info-item-title'>{item.title}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HeroSection;
