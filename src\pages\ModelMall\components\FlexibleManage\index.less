@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.flexible-manage-section {
  width: 100%;
  padding: 52px 0;
  background: #ffffff;

  .news-container {
    max-width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  .section-title {
    width: 1240px;
    height: 52px;
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 600;
    font-size: 32px;
    line-height: 1.5;
    text-align: center;
    color: #000000;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .news-grid {
    display: flex;
    width: 100%;
    gap: 32px;
    height: auto;
  }

  .news-item {
    overflow: hidden;
    position: relative;
    height: 410px;
    cursor: pointer;
    width: 120px;
    min-width: 120px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    &.featured {
      width: 920px;
      background: #d9d9d9;
    }

    .news-item-bg {
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;
      background-size: auto;
      background-position: left center;
      background-repeat: no-repeat;
      filter: blur(0px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 30%;
        height: 100%;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0.3) 0%,
          rgba(255, 255, 255, 0.1) 50%,
          transparent 100%
        );
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
      }
    }
    .news-background {
      position: relative;
      z-index: 2;
      width: 100%;
      height: 100%;
      background-size: auto;
      background-position: right center;
      background-repeat: no-repeat;
    }

    .news-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 357px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      padding: 32px;
      transition: all 0.3s ease;
    }

    &.expanded .news-overlay {
      background: linear-gradient(
        to bottom,
        transparent 20%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(0, 0, 0, 0.8) 100%
      );
    }

    .news-tag {
      padding: 4px 12px;
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(4px);
      border-radius: 2px;
      width: fit-content;
      margin-bottom: 16px;

      .tag-text {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 600;
        font-size: 20px;
        line-height: 1.5;
        color: #ffffff;
      }
    }
    .position-icon {
      position: absolute;
      top: 36px;
      left: 36px;
      width: 36px;
      height: 36px;
    }
    .news-expanded-content {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.4s ease 0.2s forwards;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .news-subtitle {
        color: var(---85, rgba(0, 0, 0, 0.85));

        /* h2 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 30px */
      }

      .news-content {
        color: var(---45, rgba(0, 0, 0, 0.45));

        /* t2 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
      }

      .news-actions {
        color: var(---Brand1-5, #37f);

        /* t2 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
      }
    }

    .news-coll-content {
      display: flex;
      align-items: end;
      > div {
        text-align: center;
        > img {
          width: 36px;
          height: 36px;
        }
        > p {
          width: 66px;
          color: var(---85, rgba(0, 0, 0, 0.85));
          text-align: center;

          /* t1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 150%; /* 24px */
        }
      }
    }
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
