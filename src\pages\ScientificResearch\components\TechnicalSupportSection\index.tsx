import React from 'react';
import { useNavigate } from 'react-router-dom';
import './index.less';

const TechnicalSupportSection: React.FC = () => {
  const navigate = useNavigate();
  const supportItems = [
    {
      id: 1,
      title: '使用手册',
      description:
        '本平台是专为医疗机构、科研团队及药企研发设计的AI驱动型科研分析系统，集成自然语言处理、医学影像识别与多组学分析能力，助力高效产出可信赖的医学研究成果。',
      icon: '/platform/images/scientific-research/help-left-icon.svg',
    },
    {
      id: 2,
      title: '技术指南',
      description:
        '本平台是专为医疗机构、科研团队及药企研发设计的AI驱动型科研分析系统，集成自然语言处理、医学影像识别与多组学分析能力，助力高效产出可信赖的医学研究成果。',
      icon: '/platform/images/scientific-research/help-right-icon.svg',
    },
  ];

  // 处理了解更多按钮点击
  const handleLearnMore = (itemId: number) => {
    if (itemId === 1) {
      // 使用手册
      navigate('/platform/scientific-research/user-manual');
    } else if (itemId === 2) {
      // 技术指南
      navigate('/platform/scientific-research/technical-guide');
    }
  };

  return (
    <section className='technical-support-section'>
      <div className='section-container'>
        <h2 className='section-title'>技术支持与帮助</h2>

        <div className='support-content'>
          <div className='support-background'>
            <div className='bg-left'></div>
            <div className='bg-right'></div>
          </div>

          <div className='support-items'>
            {supportItems.map(item => (
              <div key={item.id} className='support-item'>
                <div className='support-header'>
                  <div className='support-icon'>
                    <img src={item.icon} alt={item.title} />
                  </div>
                  <h3 className='support-title'>{item.title}</h3>
                </div>
                <p className='support-description'>{item.description}</p>
                <button className='learn-more-btn' onClick={() => handleLearnMore(item.id)}>
                  <span>了解更多</span>
                  <img src='/platform/images/arrow-right.svg' alt='箭头' />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechnicalSupportSection;
