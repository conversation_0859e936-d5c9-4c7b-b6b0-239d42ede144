import React, { useState } from 'react';
import { Card, Row, Col, Button, Switch, Tag, Divider, Tabs, List, Typography } from 'antd';
import { CheckOutlined, CrownOutlined } from '@ant-design/icons';
import './index.less';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

const PricingPage: React.FC = () => {
  const [isYearly, setIsYearly] = useState(false);

  // 算力商城价格
  const computingPlans = [
    {
      name: 'GPU基础版',
      description: '适合个人开发者和小型项目',
      price: { monthly: 0.8, yearly: 0.8 },
      unit: '元/GPU小时',
      features: [
        'Tesla T4 GPU',
        '8GB显存',
        '4核CPU',
        '16GB内存',
        '100GB存储空间',
        '基础技术支持'
      ],
      popular: false,
      color: '#1890ff'
    },
    {
      name: 'GPU专业版',
      description: '适合企业和研发团队',
      price: { monthly: 2.5, yearly: 2.5 },
      unit: '元/GPU小时',
      features: [
        'Tesla V100 GPU',
        '32GB显存',
        '8核CPU',
        '64GB内存',
        '500GB存储空间',
        '优先技术支持',
        '集群管理',
        '任务调度'
      ],
      popular: true,
      color: '#52c41a'
    },
    {
      name: 'GPU旗舰版',
      description: '适合大规模AI训练',
      price: { monthly: 8.0, yearly: 8.0 },
      unit: '元/GPU小时',
      features: [
        'Tesla A100 GPU',
        '80GB显存',
        '16核CPU',
        '128GB内存',
        '1TB存储空间',
        '24/7专属支持',
        '多GPU集群',
        '分布式训练',
        '定制化服务'
      ],
      popular: false,
      color: '#722ed1'
    }
  ];

  // 模型商城价格
  const modelPlans = [
    {
      name: '基础调用',
      description: '适合轻量级应用',
      price: { monthly: 0.01, yearly: 0.008 },
      unit: '元/千次调用',
      features: [
        '基础NLP模型',
        '图像识别模型',
        '语音识别模型',
        'API调用限制: 10万次/月',
        '标准响应时间',
        '邮件技术支持'
      ],
      popular: false,
      color: '#1890ff'
    },
    {
      name: '专业调用',
      description: '适合商业应用',
      price: { monthly: 0.005, yearly: 0.004 },
      unit: '元/千次调用',
      features: [
        '高级NLP模型',
        '计算机视觉模型',
        '语音合成模型',
        'API调用限制: 100万次/月',
        '优化响应时间',
        '在线技术支持',
        '模型微调服务'
      ],
      popular: true,
      color: '#52c41a'
    },
    {
      name: '企业定制',
      description: '适合大型企业',
      price: { monthly: '定制', yearly: '定制' },
      unit: '面议',
      features: [
        '定制化模型训练',
        '私有化部署',
        '专属模型版本',
        '无限API调用',
        '毫秒级响应',
        '专属客户经理',
        '定制化开发',
        'SLA保障'
      ],
      popular: false,
      color: '#722ed1'
    }
  ];

  // 应用商城价格
  const appPlans = [
    {
      name: '个人版',
      description: '适合个人用户',
      price: { monthly: 99, yearly: 999 },
      unit: '元/月',
      features: [
        '3个应用使用权',
        '基础功能',
        '个人数据存储',
        '邮件支持',
        '使用教程'
      ],
      popular: false,
      color: '#1890ff'
    },
    {
      name: '专业版',
      description: '适合中小企业',
      price: { monthly: 299, yearly: 2999 },
      unit: '元/月',
      features: [
        '10个应用使用权',
        '高级功能',
        '团队协作',
        '企业级存储',
        '在线客服支持',
        '定制化配置',
        '数据分析报告'
      ],
      popular: true,
      color: '#52c41a'
    },
    {
      name: '企业版',
      description: '适合大型企业',
      price: { monthly: '定制', yearly: '定制' },
      unit: '面议',
      features: [
        '无限应用使用权',
        '全部功能',
        '私有化部署',
        '专属服务器',
        '24/7专属支持',
        '定制化开发',
        'API接口',
        'SLA保障'
      ],
      popular: false,
      color: '#722ed1'
    }
  ];

  const renderPricingCard = (plan: any, index: number) => (
    <Col xs={24} lg={8} key={index}>
      <Card
        className={`pricing-card ${plan.popular ? 'popular' : ''}`}
        bodyStyle={{ padding: '32px 24px' }}
      >
        {plan.popular && (
          <div className="popular-badge">
            <CrownOutlined /> 推荐
          </div>
        )}
        
        <div className="plan-header">
          <h3 style={{ color: plan.color }}>{plan.name}</h3>
          <p className="plan-description">{plan.description}</p>
        </div>

        <div className="plan-price">
          <div className="price-amount">
            {typeof plan.price[isYearly ? 'yearly' : 'monthly'] === 'number' ? (
              <>
                <span className="currency">¥</span>
                <span className="amount">
                  {plan.price[isYearly ? 'yearly' : 'monthly']}
                </span>
              </>
            ) : (
              <span className="amount">{plan.price[isYearly ? 'yearly' : 'monthly']}</span>
            )}
          </div>
          <div className="price-unit">{plan.unit}</div>
        </div>

        <Divider />

        <div className="plan-features">
          {plan.features.map((feature: string, idx: number) => (
            <div key={idx} className="feature-item">
              <CheckOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
              <span>{feature}</span>
            </div>
          ))}
        </div>

        <div className="plan-action">
          <Button 
            type={plan.popular ? 'primary' : 'default'}
            size="large"
            style={{ 
              width: '100%',
              backgroundColor: plan.popular ? plan.color : undefined,
              borderColor: plan.color
            }}
          >
            {typeof plan.price[isYearly ? 'yearly' : 'monthly'] === 'number' ? '立即购买' : '联系销售'}
          </Button>
        </div>
      </Card>
    </Col>
  );

  return (
    <div className="pricing-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1>灵活的价格方案</h1>
          <p>选择最适合您需求的价格方案，按需付费，随时升级</p>
          
          <div className="billing-toggle">
            <span className={!isYearly ? 'active' : ''}>按月付费</span>
            <Switch
              checked={isYearly}
              onChange={setIsYearly}
            />
            <span className={isYearly ? 'active' : ''}>
              按年付费 <Tag color="red">节省15%</Tag>
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="pricing-section">
        <div className="container">
          <Tabs defaultActiveKey="computing" size="large" centered>
            <TabPane tab="算力商城" key="computing">
              <div className="pricing-grid">
                <Row gutter={[32, 32]}>
                  {computingPlans.map(renderPricingCard)}
                </Row>
              </div>
            </TabPane>

            <TabPane tab="模型商城" key="model">
              <div className="pricing-grid">
                <Row gutter={[32, 32]}>
                  {modelPlans.map(renderPricingCard)}
                </Row>
              </div>
            </TabPane>

            <TabPane tab="应用商城" key="app">
              <div className="pricing-grid">
                <Row gutter={[32, 32]}>
                  {appPlans.map(renderPricingCard)}
                </Row>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="faq-section">
        <div className="container">
          <div className="section-header">
            <h2>常见问题</h2>
            <p>关于价格和服务的常见疑问解答</p>
          </div>

          <Row gutter={[48, 48]}>
            <Col xs={24} lg={12}>
              <List
                itemLayout="vertical"
                dataSource={[
                  {
                    title: '如何计费？',
                    content: '我们采用按需付费的模式，算力按小时计费，模型按调用次数计费，应用按月订阅。'
                  },
                  {
                    title: '支持哪些付费方式？',
                    content: '支持支付宝、微信支付、银行转账等多种付费方式，企业用户支持月结。'
                  },
                  {
                    title: '可以随时升级套餐吗？',
                    content: '可以的，您可以随时升级套餐，差价会按比例计算并补齐。'
                  }
                ]}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      title={<Title level={4}>{item.title}</Title>}
                      description={<Text>{item.content}</Text>}
                    />
                  </List.Item>
                )}
              />
            </Col>
            <Col xs={24} lg={12}>
              <List
                itemLayout="vertical"
                dataSource={[
                  {
                    title: '有免费试用吗？',
                    content: '新用户注册即可获得100元体验金，可用于体验所有付费服务。'
                  },
                  {
                    title: '企业用户有优惠吗？',
                    content: '企业用户享受批量折扣，年付用户额外享受15%优惠，具体可联系销售。'
                  },
                  {
                    title: '如何申请发票？',
                    content: '在用户中心的账单管理中可以申请开具发票，支持增值税普通发票和专用发票。'
                  }
                ]}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      title={<Title level={4}>{item.title}</Title>}
                      description={<Text>{item.content}</Text>}
                    />
                  </List.Item>
                )}
              />
            </Col>
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>还有疑问？</h2>
            <p>联系我们的销售团队，获取专属价格方案</p>
            <div className="cta-buttons">
              <Button type="primary" size="large">
                联系销售
              </Button>
              <Button size="large" style={{ marginLeft: '16px' }}>
                在线咨询
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingPage;
