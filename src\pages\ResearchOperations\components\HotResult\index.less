.hot-result {
  width: 100%;
  padding: 52px 0;
  margin-top: 110px;
  .hot-result-container {
    width: 1240px;
    margin: 0 auto;
    .hot-result-title {
      width: 100%;
      position: relative;
      > h2 {
        width: 100%;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 35.495px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 53.242px */
        text-align: center;
        margin: 0;
      }
      > p {
        display: flex;
        height: 20px;
        align-items: center;
        gap: 2px;
        margin: 0;
        position: absolute;
        right: 0;
        bottom: 0;
        color: var(---Brand1-5, #37f);
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
      }
    }
    .hot-result-content {
      width: 100%;
      height: 445px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 24px;
      margin-top: 32px;
      .hot-result-item {
        height: 100%;
        height: 420.56px;
        aspect-ratio: 83/90;
        border-radius: 12px;
        border-radius: 12px;
        border: 1px solid var(---08, rgba(0, 0, 0, 0.08));
        background: #fff;
        overflow: hidden;
        /* 卡片投影 */
        box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 20px 40px 0 rgba(134, 156, 199, 0.2);
          border-color: var(---Brand1-5, #37f);
        }
        .hot-result-item-img {
          width: 100%;
          height: 50%;
          > img {
            display: block;
            width: calc(100% + 2px); /* 覆盖左侧可能出现的1-2px黑边 */
            height: 100%;
            object-fit: cover;
            margin-left: -2px;
          }
        }
        .hot-result-item-info {
          width: 100%;
          height: 50%;
          padding: 16px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .hot-result-item-info-container {
            > h3 {
              color: var(---85, rgba(0, 0, 0, 0.85));
              font-family: 'Alibaba PuHuiTi';
              font-size: 20px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 30px */
              margin: 0;
            }
            .hot-result-item-info-company {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-top: 6px;
              color: var(---45, rgba(0, 0, 0, 0.45));
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
            }
            .hot-result-item-info-content {
              width: 100%;
              color: var(---65, rgba(0, 0, 0, 0.65));
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
              margin-top: 10px;
            }
          }
          .hot-result-item-footer {
            width: 100%;
            height: 38px;
            padding-top: 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            display: flex;
            justify-content: space-between;
            align-items: center;
            .hot-result-item-footer-left {
              height: 38px;
              display: flex;
              align-items: center;
              gap: 6px;
              > img {
                width: 28px;
                height: 28px;
                aspect-ratio: 1/1;
                border-radius: 28px;
                border: 1px solid #d8e6ff;
              }
            }
            .hot-result-item-footer-right {
              height: 38px;
              display: flex;
              align-items: center;
              gap: 6px;
              > div {
                height: 38px;
                display: flex;
                align-items: center;
                gap: 10px;
                > img {
                  width: 20px;
                  height: 20px;
                  aspect-ratio: 1/1;
                }
              }
            }
          }
        }
      }
      .active {
        width: 420px;
        height: 445px;
      }
    }
  }
}
