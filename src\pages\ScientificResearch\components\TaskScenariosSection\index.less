.taskScenariosSection {
  padding: 52px 0;
  background: #f3f6f9;

  .sectionContainer {
    max-width: 1240px;
    width: 1240px;
    margin: 0 auto;
  }

  .sectionTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: #000000;
    text-align: center;
    margin-bottom: 32px;
  }

  .scenariosGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    height: 581px;
  }

  .scenarioCard {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    display: flex;
    flex-direction: column;
  }

  .scenarioImage {
    flex: 1;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .scenarioContent {
    display: flex;
    padding: 24px 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;
  }

  .scenarioTitle {
    font-family: '<PERSON><PERSON><PERSON> PuHuiTi', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    text-align: center;
    line-height: 1.5;
    margin: 0;
  }
}
