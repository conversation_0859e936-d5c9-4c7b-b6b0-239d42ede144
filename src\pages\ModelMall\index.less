.model-mall {
  min-height: 100vh;
  background: #f8f9fa;

  .page-header {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    padding: 60px 0;
    text-align: center;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      h1 {
        color: white;
        font-size: 36px;
        margin-bottom: 16px;
        font-weight: 600;

        .anticon {
          margin-right: 12px;
          font-size: 32px;
        }
      }

      p {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
    }
  }

  .categories-section {
    padding: 60px 0;
    background: white;

    .categories-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      h2 {
        font-size: 28px;
        color: #333;
        margin-bottom: 32px;
        text-align: center;
      }

      .category-card {
        text-align: center;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .category-icon {
          font-size: 32px;
          color: #6f42c1;
          margin-bottom: 12px;
        }

        .category-info {
          h3 {
            font-size: 16px;
            color: #333;
            margin: 0 0 4px;
          }

          .category-count {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }

  .models-section {
    padding: 60px 0;

    .models-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;

        h2 {
          font-size: 28px;
          color: #333;
          margin: 0;
        }

        .filter-buttons {
          .ant-btn {
            margin-left: 8px;
          }
        }
      }

      .model-card {
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .model-cover {
          height: 100px;
          background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .category-badge {
            display: flex;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: 600;

            .anticon {
              font-size: 24px;
              margin-right: 8px;
            }
          }
        }

        .model-content {
          .provider {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
          }

          .description {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .rating {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .rating-text {
              font-size: 12px;
              color: #666;
              margin-left: 8px;
              font-weight: 600;
            }
          }

          .stats {
            font-size: 12px;
            color: #999;
            margin-bottom: 12px;

            .downloads {
              margin-right: 16px;
            }
          }

          .price {
            display: flex;
            align-items: baseline;
            margin-bottom: 12px;

            .price-value {
              font-size: 20px;
              font-weight: 600;
              color: #e83e8c;
            }

            .price-unit {
              font-size: 12px;
              color: #999;
              margin-left: 4px;
            }
          }

          .tags {
            .ant-tag {
              margin-bottom: 4px;
              font-size: 11px;
            }
          }
        }

        .model-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .advantages-section {
    background: white;
    padding: 60px 0;

    .advantages-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      h2 {
        font-size: 28px;
        color: #333;
        margin-bottom: 48px;
        text-align: center;
      }

      .advantage-item {
        text-align: center;
        padding: 24px;

        .advantage-icon {
          font-size: 48px;
          color: #6f42c1;
          margin-bottom: 16px;
        }

        h3 {
          font-size: 20px;
          color: #333;
          margin-bottom: 12px;
        }

        p {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

.ant-badge-ribbon-wrapper .ant-badge-ribbon {
  top: 12px;
  right: -4px;
}
