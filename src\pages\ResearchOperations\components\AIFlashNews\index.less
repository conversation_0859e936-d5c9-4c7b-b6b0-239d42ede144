.ai-flash-news-page {
  width: 100%;
  padding: 52px 0;

  .ai-flash-news-container {
    width: 1240px;
    margin: 0 auto;
    .ai-flash-news-title {
      width: 100%;
      position: relative;
      > h2 {
        width: 100%;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 35.495px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 53.242px */
        text-align: center;
        margin: 0;
      }
      > p {
        display: flex;
        height: 20px;
        align-items: center;
        gap: 2px;
        margin: 0;
        position: absolute;
        right: 0;
        bottom: 0;
        color: var(---Brand1-5, #37f);
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
      }
    }
    .ai-flash-news-content {
      margin-top: 32px;
      display: flex;
      height: 558px;
      gap: 24px;
      .news-info-left {
        display: flex;
        width: 257px;
        height: 558px;
        padding: 24px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20px;
        border-radius: 12px;
        background: #eef5ff;
        box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
        .news-info-left-item {
          display: flex;
          padding: 10px 24px;
          align-items: center;
          gap: 10px;
          align-self: stretch;
          border-radius: 6px;
          background: #fff;
          > img {
            width: 34px;
            height: 32px;
          }
          > p {
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-align: center;
            font-family: 'Alibaba PuHuiTi';
            font-size: 22px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 33px */
          }
        }
        .active {
          border-radius: 6px;
          background: var(---Brand1-5, #37f);
          > p {
            color: #fff;
          }
        }
      }
      .news-info-right {
        flex: 1 0 0;
        height: 558px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        .news-info-right-item {
          width: 100%;
          padding: 20px;
          display: flex;
          gap: 16px;
          border-radius: 12px;
          background: var(---02, rgba(0, 0, 0, 0.02));

          /* 卡片投影 */
          box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
          .news-info-right-item-date-img {
            border-radius: 4px;
            border: 1px solid var(---12, rgba(0, 0, 0, 0.12));
            display: flex;
            width: 60px;
            height: 60px;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            .date-day {
              display: flex;
              flex-direction: column;
              justify-content: center;
              flex: 1 0 0;
              align-self: stretch;
              color: var(---Brand1-5, #37f);
              text-align: center;
              font-family: 'Days One';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
            }
            .date-other {
              display: flex;
              justify-content: center;
              align-items: center;
              flex: 1 0 0;
              align-self: stretch;
              color: #000;
              text-align: center;
              font-family: 'Alibaba PuHuiTi';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 18px */
              background: var(---Brand1-2, rgba(0, 107, 255, 0.3));
            }
          }
          .news-info-right-item-info {
            display: flex;
            flex-direction: column;
            gap: 6px;
            > h4 {
              color: #000;
              font-family: 'Alibaba PuHuiTi';
              font-size: 20px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 30px */
            }
            .item-desc {
              color: var(---65, rgba(0, 0, 0, 0.65));
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            .item.date {
              color: var(---45, rgba(0, 0, 0, 0.45));
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
            }
          }
        }
        .news-info-right-item-large {
          height: 212px;
          padding: 0;
          display: flex;
          .news-info-right-item-img {
            width: 414px;
            height: 212px;
            > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .news-info-right-item-info {
            flex: 1;
            padding: 16px;
            > h3 {
              width: 440px;
              color: var(---85, rgba(0, 0, 0, 0.85));
              font-family: 'Alibaba PuHuiTi';
              font-size: 20px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 30px */
            }
            > .item-desc {
              width: 440px;
              color: var(---65, rgba(0, 0, 0, 0.65));
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
              margin: 10px 0;
              display: -webkit-box;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            .item-date {
              color: var(---45, rgba(0, 0, 0, 0.45));
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
            }
          }
        }
      }
    }
  }
}
