.policy-files {
  width: 100%;
  padding: 52px 0;
  position: relative;
  background: url(/platform/images/news-center/policy-file-bg2.png) lightgray 50% / cover no-repeat;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
    background: linear-gradient(0deg, rgba(0, 85, 255, 0.13) 0%, rgba(0, 85, 255, 0.13) 100%);

    z-index: 0;
  }
  .policy-files-container {
    width: 1240px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    .policy-files-header {
      width: 100%;
      > h1 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
    }
    .policy-files-content {
      margin-top: 32px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      .policy-files-item {
        display: flex;
        padding: 28px 32px;
        align-items: flex-start;
        gap: 28px;
        flex: 1 0 0;
        align-self: stretch;
        border-radius: 2px;
        background: var(---, #fff);
      }
      .policy-files-item-date {
        width: 56px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        > h3 {
          width: 56px;
          overflow: hidden;
          color: var(---85, rgba(0, 0, 0, 0.85));
          text-overflow: ellipsis;
          font-family: Poppins;
          font-size: 40px;
          font-style: normal;
          font-weight: 600;
          line-height: 110%; /* 44px */
        }
        > p {
          width: 56px;
          overflow: hidden;
          color: var(---85, rgba(0, 0, 0, 0.85));
          text-overflow: ellipsis;

          /* t3 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 21px */
        }
      }
      .policy-files-item-info {
        > h3 {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          align-self: stretch;
          overflow: hidden;
          color: var(---85, rgba(0, 0, 0, 0.85));
          text-overflow: ellipsis;

          /* t1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 24px */
        }
        > p {
          margin-top: 12px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          color: var(---45, rgba(0, 0, 0, 0.45));
          text-overflow: ellipsis;

          /* t2-段落 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 180%; /* 28.8px */
        }
      }
    }
  }
}
