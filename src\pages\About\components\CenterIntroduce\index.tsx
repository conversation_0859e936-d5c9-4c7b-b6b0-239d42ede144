import React from 'react';
import './index.less';
import { Col, Row, Statistic, type StatisticProps } from 'antd';
import CountUp from 'react-countup';

const formatter: StatisticProps['formatter'] = value => (
  <CountUp end={value as number} separator=',' />
);

const CenterIntroduce: React.FC = () => {
  return (
    <section className='about-center-introduce'>
      <div className='about-center-introduce-container'>
        <div className='about-center-introduce-content'>
          <div className='about-center-introduce-title'>
            <h3>河南省人工智能行业赋能中心（医疗）</h3>
          </div>
          <div className='about-center-introduce-content-description'>
            <p>
              立足中原医学科学城，由河南省医学科学院牵头，联合郑州大学第一附属医院、河南空港数字城市开发建设有限公司等10家单位共同建设，是河南省发展和改革委员会审批通过的首批河南省人工智能行业赋能中心之一。
              中心整合省内顶级医疗资源、算力基础设施和AI优秀研发团队，首创“1+1+2+N+3”医疗可信数据空间架构，集成不低于2000P的分布式算力资源，打造中部地区规模最大的医疗AI算力池。
              中心以人才培养、产业孵化为目标，构建综合服务运营平台，打通“算力供给-数据赋能-模型进化-应用推广”的创新链条，面向医疗机构、科研机构、高校、企业、政策机构等多元主体，提供覆盖技术支撑、科研协同、产业赋能的全流程服务，推动河南医疗AI技术自主可控与产业能级跃升，为“健康中国2030”提供核心支撑。
            </p>
          </div>
        </div>
        <div className='about-center-introduce-image'>
          <img src='/platform/images/about/about-center-introduce.png' alt='' />
        </div>
      </div>
      <div className='about-center-introduce-bg'>
        <div className='about-center-introduce-bg-container'>
          <Row
            gutter={16}
            className='about-center-introduce-bg-row'
            align='middle'
            justify='center'
          >
            <Col span={8}>
              <Statistic
                className='about-center-introduce-bg-row-statistic'
                suffix='+'
                value={10}
                formatter={formatter}
              />
              <p className='about-center-introduce-bg-row-title'>建设单位</p>
            </Col>
            <Col span={8}>
              <Statistic
                className='about-center-introduce-bg-row-statistic'
                value={'1+1+2+N+3'}
                precision={2}
              />
              <p className='about-center-introduce-bg-row-title'>首创医疗可信数据空间架构</p>
            </Col>
            <Col span={8}>
              <Statistic
                className='about-center-introduce-bg-row-statistic'
                suffix='P'
                value={20000}
                precision={2}
                formatter={formatter}
              />
              <p className='about-center-introduce-bg-row-title'>分布式算力资源</p>
            </Col>
          </Row>
        </div>
      </div>
    </section>
  );
};

export default CenterIntroduce;
