import { useEffect, useState } from 'react';
import './index.less';
import dayjs from 'dayjs';

export default function Header() {
  const getChineseWeekday = () => {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    const dayOfWeek = dayjs().day(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    return `星期${weekdays[dayOfWeek]}`;
  };

  const [dateInfo, setDateInfo] = useState({
    date: dayjs().format('YYYY.MM.DD'),
    time: dayjs().format('HH:mm:ss'),
    week: getChineseWeekday(),
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setDateInfo({
        date: dayjs().format('YYYY.MM.DD'),
        time: dayjs().format('HH:mm:ss'),
        week: getChineseWeekday(),
      });
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className='security-dashboard-header'>
      <h1 className='security-dashboard-header-title'>安全赋能一体化运营平台</h1>
      <div className='security-dashboard-header-info'>
        <div className='security-dashboard-header-info-left'>
          <img src='/platform/images/security-dashboard/security-dashboard-notice.svg' alt='' />
          <p>系统通知：【节点告警】07-28 郑州C 节点延迟异常...</p>
        </div>
        <div className='security-dashboard-header-info-right'>
          <p>{dateInfo.date}</p>
          <p>{dateInfo.week}</p>
          <p className='security-dashboard-header-info-right-time'>{dateInfo.time}</p>
        </div>
      </div>
    </div>
  );
}
