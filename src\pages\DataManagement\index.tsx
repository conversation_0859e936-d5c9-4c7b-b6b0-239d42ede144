import React from 'react';
import { Card, Row, Col, Statistic, Button, Table, Tag } from 'antd';
import { DatabaseOutlined, CloudOutlined, SafetyOutlined, SyncOutlined } from '@ant-design/icons';
import './index.less';

const DataManagement: React.FC = () => {
  // 模拟数据
  const dataStats = [
    { title: '总数据量', value: 2.5, suffix: 'TB' },
    { title: '数据源数量', value: 156, suffix: '个' },
    { title: '活跃连接', value: 89, suffix: '个' },
    { title: '数据同步率', value: 99.8, suffix: '%' },
  ];

  const dataSourceColumns = [
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'MySQL' ? 'blue' : type === 'MongoDB' ? 'green' : 'orange'}>
          {type}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === '在线' ? 'success' : 'error'}>{status}</Tag>
      ),
    },
    {
      title: '数据量',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: '最后同步',
      dataIndex: 'lastSync',
      key: 'lastSync',
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <div>
          <Button type='link' size='small'>
            查看
          </Button>
          <Button type='link' size='small'>
            同步
          </Button>
          <Button type='link' size='small'>
            配置
          </Button>
        </div>
      ),
    },
  ];

  const dataSourceData = [
    {
      key: '1',
      name: '用户数据库',
      type: 'MySQL',
      status: '在线',
      size: '1.2GB',
      lastSync: '2分钟前',
    },
    {
      key: '2',
      name: '日志数据',
      type: 'MongoDB',
      status: '在线',
      size: '856MB',
      lastSync: '5分钟前',
    },
    {
      key: '3',
      name: '文件存储',
      type: 'MinIO',
      status: '在线',
      size: '3.4GB',
      lastSync: '1小时前',
    },
  ];

  return (
    <div className='data-management'>
      {/* 页面头部 */}
      <div className='page-header'>
        <div className='header-content'>
          <div className='header-info'>
            <h1>
              <DatabaseOutlined /> 数据空间
            </h1>
            <p>统一的数据存储、管理和共享平台，支持多种数据源的集成和分析</p>
          </div>
        </div>
      </div>

      {/* 数据统计 */}
      <div className='data-stats'>
        <Row gutter={[24, 24]}>
          {dataStats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 功能模块 */}
      <div className='data-features'>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <CloudOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                </div>
              }
            >
              <Card.Meta title='分布式存储' description='高可用、高性能的分布式数据存储解决方案' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                管理存储
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <SyncOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                </div>
              }
            >
              <Card.Meta title='数据同步' description='实时数据同步和ETL处理，确保数据一致性' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                配置同步
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <SafetyOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
                </div>
              }
            >
              <Card.Meta title='数据安全' description='多层次数据加密和权限控制，保障数据安全' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                安全设置
              </Button>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card
              hoverable
              className='feature-card'
              cover={
                <div className='feature-icon'>
                  <DatabaseOutlined style={{ fontSize: '48px', color: '#722ed1' }} />
                </div>
              }
            >
              <Card.Meta title='数据共享' description='跨组织数据共享和协作平台' />
              <Button type='primary' style={{ marginTop: '16px' }}>
                共享管理
              </Button>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 数据源列表 */}
      <div className='data-sources'>
        <Card title='数据源管理' extra={<Button type='primary'>添加数据源</Button>}>
          <Table columns={dataSourceColumns} dataSource={dataSourceData} pagination={false} />
        </Card>
      </div>
    </div>
  );
};

export default DataManagement;
