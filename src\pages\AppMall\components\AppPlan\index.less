.app-plan-section {
  width: 100%;
  padding: 52px 0;
  background: var(---, #fff);
  .app-plan-container {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 32px;

    .app-plan-title h2 {
      color: var(---85, rgba(0, 0, 0, 0.85));
      text-align: center;
      font-family: 'Alibaba PuHuiTi';
      font-size: 32px;
      font-style: normal;
      font-weight: 600;
      line-height: 150%; /* 48px */
      margin: 0;
    }

    .app-plans {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
      .app-plan-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80px;
        gap: 10px;
        background: var(---, #fff);
        border-radius: 10px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        > img {
          display: flex;
          width: 24px;
          height: 24px;
          justify-content: center;
          align-items: center;
        }
        > p {
          color: var(---85, rgba(0, 0, 0, 0.85));

          /* t1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 150%; /* 24px */
          margin: 0;
        }
      }
      .active {
        /* 颜色叠加 + 背景图：上层半透明品牌色渐变，下层为图片 */
        background-image: url('/platform/images/app-mall/app-plan-active.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center, center;
        /* 回退纯色（图片或渐变不可用时生效） */
        background-color: var(---Brand1-5, #37f);
        background-blend-mode: multiply;
        border-radius: 4px;
        position: relative;

        > p {
          color: var(---, #fff);
        }
        &::after {
          content: '';
          display: block;
          width: 100px;
          height: 4px;
          background: var(---Brand1-4, #66b3ff);
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }
    }

    .app-plan-desc {
      width: 100%;
      height: 440px;
      display: flex;
      justify-content: space-between;

      .app-plan-desc-left {
        width: 450px;
        height: 100%;
        padding: 24px 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        gap: 40px;
        .app-plan-desc-left-title {
          h2 {
            width: 100%;
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: 'Alibaba PuHuiTi';
            font-size: 40px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 60px */
            margin: 0;
          }
          p {
            width: 100%;
            color: var(---85, rgba(0, 0, 0, 0.85));

            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            margin: 0;
          }
        }
        .stroke-line {
          width: 100%;
          height: 1px;
          background: var(---08, rgba(0, 0, 0, 0.08));
          position: relative;
          > img {
            position: absolute;
            top: -3px;
            left: 0;
          }
        }
        .app-plan-desc-left-content {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          gap: 12px;
          > div {
            display: flex;
            gap: 10px;
            > p {
              margin: 0;
              overflow: hidden;
              color: var(---65, rgba(0, 0, 0, 0.65));
              text-overflow: ellipsis;

              /* t2 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
              opacity: 0.89;
            }
          }
        }
        .app-plan-desc-left-btn {
          display: flex;
          width: 160px;
          height: 52px;
          padding: 24px 52px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 4px;
          background: var(---Brand1-5, #37f);
          color: var(---, #fff);

          /* t1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 150%; /* 24px */
        }
      }
      .app-plan-desc-right {
        width: 739px;
        height: 440px;
        border-radius: 20px;
        background: var(---fill-7, #f5f7fa);
        > img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
