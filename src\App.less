// App 样式
@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.app {
  .text-center();
}

.app-logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;

  &:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
  }
}

.app-logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .app-logo {
    animation: logo-spin infinite 20s linear;
  }
}

.app-header {
  background-color: #282c34;
  padding: @padding-xl;
  color: white;
}

.app-link {
  color: #61dafb;
}

.card {
  padding: @padding-md;
  .border-radius();
  .box-shadow();
  background: #fff;
  margin: @padding-md 0;
}

.read-the-docs {
  color: #888;
}
