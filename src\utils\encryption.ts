// RSA加密解密工具
// 注意：需要先安装 jsencrypt 依赖
// npm install jsencrypt @types/jsencrypt

// 如果没有类型声明，可以使用以下声明
declare module 'jsencrypt' {
  export default class JSEncrypt {
    constructor();
    setPublicKey(key: string): void;
    setPrivateKey(key: string): void;
    encrypt(text: string): string | false;
    decrypt(text: string): string | false;
  }
  export { JSEncrypt };
}

// 公钥 - 用于加密
const PUBLIC_KEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLX5ZZbSJ2xPozC0HXPYJrovGS
DHzIOHvqfzttK6kLgUHLnguMeHz5NdObvkOBR93p5BawBbgnzhUqDCDh2L3P1HNK
BzAh7gRoncHCyeSk47w2xL1cqeRPymhryMu9O4TVAwPYy0k5JOsoFyMpwnpJxOMf
GGTgNBSxmoPT4pO2+wIDAQAB`;

// 私钥 - 用于解密
const PRIVATE_KEY = `MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMtfllltInbE+jML
Qdc9gmui8ZIMfMg4e+p/O20rqQuBQcueC4x4fPk105u+Q4FH3enkFrAFuCfOFSoM
IOHYvc/Uc0oHMCHuBGidwcLJ5KTjvDbEvVyp5E/KaGvIy707hNUDA9jLSTkk6ygX
IynCeknE4x8YZOA0FLGag9Pik7b7AgMBAAECgYEApiXIafSb9FYGZc8rkhHBS/hJ
zNyA0P48Vh5tyUmjCDAoNZY/rxn8V4ifARzWf3bcGCnsUw/JHFPjvH8+GeR9ZCkf
yLZXOADW5hu93ybjce42R2DGUu6BorEKdLK/BjiYGIvvfqk0xOre8GANnoXReLgo
KWyNQVy9QcrecjNtm+kCQQDqnfSVjzcoplqGGqFLarFt9OnBZITwQv6TvoPpwyOv
B5LDzAt4rVrmDo9vHSAx4ycCU7F53XUkhm0EmMGHoV8NAkEA3eipMG4TMoC/vxKz
8HsVxxb3JNs8tqh/eF0s5PCfDwsd792vOa5z6gMQ7ps7nT3vv8wVAKKJ4ui0iUtU
hUgsJwJBAJVvvIzSZrC2nKwUJC5UK+U8fPNYYjBii6JxU7Y+O+lNKpcZzSi1SlOE
bKm6ZHpCE+OwiTd07hswoBmwbnxENbUCQBNNuvIIkUjyZDu138tKmcFg4QzmuWhW
TghlapNbzypa2DbWfPiykUjJDX8EJ/Jswd9YXHdarE392j6bO/YAKj8CQQCBEZXS
XPC0JP8qe417W4GybWYcWnFqpTLwPEwsV0AfUBqzSbD2LPbaiZdk/+Oa3nYaLdOf
aCspsj2AUl7N8UZO`;

// 使用动态import避免编译时错误
let JSEncrypt: any = null;

async function getJSEncrypt() {
  if (!JSEncrypt) {
    try {
      // 尝试导入jsencrypt
      const module = await import('jsencrypt');
      JSEncrypt = module.default || module.JSEncrypt;
    } catch (error) {
      console.error('JSEncrypt not installed. Please install it with: npm install jsencrypt');
      throw new Error('JSEncrypt dependency not found');
    }
  }
  return JSEncrypt;
}

/**
 * RSA加密
 * @param text 待加密的文本
 * @returns 加密后的字符串
 */
export async function encrypt(text: string): Promise<string> {
  try {
    const JSEncryptClass = await getJSEncrypt();
    const encryptor = new JSEncryptClass();
    encryptor.setPublicKey(PUBLIC_KEY);
    const encrypted = encryptor.encrypt(text);
    if (!encrypted) {
      throw new Error('Encryption failed');
    }
    return encrypted;
  } catch (error) {
    console.error('RSA encryption error:', error);
    throw error;
  }
}

/**
 * RSA解密
 * @param encryptedText 加密的文本
 * @returns 解密后的字符串
 */
export async function decrypt(encryptedText: string): Promise<string> {
  try {
    const JSEncryptClass = await getJSEncrypt();
    const encryptor = new JSEncryptClass();
    encryptor.setPrivateKey(PRIVATE_KEY);
    const decrypted = encryptor.decrypt(encryptedText);
    if (!decrypted) {
      throw new Error('Decryption failed');
    }
    return decrypted;
  } catch (error) {
    console.error('RSA decryption error:', error);
    throw error;
  }
}

// 同步版本（如果JSEncrypt已经加载）
export function encryptSync(text: string): string {
  if (!JSEncrypt) {
    throw new Error('JSEncrypt not initialized. Use async version first.');
  }
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(PUBLIC_KEY);
  const encrypted = encryptor.encrypt(text);
  if (!encrypted) {
    throw new Error('Encryption failed');
  }
  return encrypted;
}

export function decryptSync(encryptedText: string): string {
  if (!JSEncrypt) {
    throw new Error('JSEncrypt not initialized. Use async version first.');
  }
  const encryptor = new JSEncrypt();
  encryptor.setPrivateKey(PRIVATE_KEY);
  const decrypted = encryptor.decrypt(encryptedText);
  if (!decrypted) {
    throw new Error('Decryption failed');
  }
  return decrypted;
}
