import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Input,
  Select,
  Modal,
  Form,
  Upload,
  Statistic,
  Avatar,
  Rate,
  Badge,
  Descriptions,
} from 'antd';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  CopyrightOutlined,
  PlusOutlined,
  DownloadOutlined,
  EyeOutlined,
  HeartOutlined,
  CalendarOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import './ResourcesSharingPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const ResourcesSharingPage: React.FC = () => {
  const [isEquipmentModalVisible, setIsEquipmentModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 资源统计
  const resourceStats = [
    { title: '科研设备', value: 2845, prefix: <ExperimentOutlined />, color: '#1890ff' },
    { title: '数据集', value: 15673, prefix: <DatabaseOutlined />, color: '#52c41a' },
    { title: '技术文档', value: 8932, prefix: <FileTextOutlined />, color: '#fa8c16' },
    { title: '知识产权', value: 1256, prefix: <CopyrightOutlined />, color: '#722ed1' },
  ];

  // 设备数据
  const equipmentData = [
    {
      key: '1',
      name: '高分辨率电子显微镜',
      model: 'TEM-3000',
      category: '材料分析',
      location: '清华大学材料学院',
      status: 'available',
      price: '500元/小时',
      rating: 4.8,
      bookings: 156,
      contact: '张教授',
      phone: '010-12345678',
      description: '适用于纳米材料结构分析，分辨率达0.1nm',
      image: '/platform/images/research-operations/equipment-1.jpg',
    },
    {
      key: '2',
      name: 'X射线衍射仪',
      model: 'XRD-6000',
      category: '晶体分析',
      location: '北京大学化学院',
      status: 'busy',
      price: '300元/小时',
      rating: 4.6,
      bookings: 89,
      contact: '李研究员',
      phone: '010-87654321',
      description: '用于晶体结构分析和相态识别',
      image: '/platform/images/research-operations/equipment-2.jpg',
    },
    {
      key: '3',
      name: '超高速离心机',
      model: 'UC-8000',
      category: '生物分离',
      location: '中科院生物所',
      status: 'maintenance',
      price: '200元/小时',
      rating: 4.5,
      bookings: 234,
      contact: '王博士',
      phone: '010-11223344',
      description: '最高转速80000rpm，适用于蛋白质分离纯化',
      image: '/platform/images/research-operations/equipment-3.jpg',
    },
  ];

  // 数据集数据
  const datasetData = [
    {
      key: '1',
      name: '医学影像数据集',
      type: '图像数据',
      size: '128GB',
      format: 'DICOM',
      domain: '医疗健康',
      provider: '北京协和医院',
      downloads: 2340,
      rating: 4.9,
      openness: 'public',
      license: 'CC BY 4.0',
      description: '包含10万例CT、MRI医学影像数据，用于AI诊断研究',
      tags: ['医学影像', 'CT', 'MRI', '深度学习'],
    },
    {
      key: '2',
      name: '工业缺陷检测数据',
      type: '图像数据',
      size: '45GB',
      format: 'PNG/JPG',
      domain: '智能制造',
      provider: '华为技术有限公司',
      downloads: 1876,
      rating: 4.7,
      openness: 'restricted',
      license: '商业许可',
      description: '包含各类工业产品缺陷图像，标注准确率99%以上',
      tags: ['缺陷检测', '质量控制', '机器视觉'],
    },
    {
      key: '3',
      name: '金融时序数据',
      type: '时序数据',
      size: '12GB',
      format: 'CSV',
      domain: '金融科技',
      provider: '中国银行总行',
      downloads: 956,
      rating: 4.4,
      openness: 'internal',
      license: '内部使用',
      description: '5年股票、期货、外汇交易数据，适用于量化研究',
      tags: ['时序分析', '量化交易', '风险建模'],
    },
  ];

  // 技术文档数据
  const documentData = [
    {
      key: '1',
      title: '深度学习在医疗诊断中的应用指南',
      type: '技术报告',
      author: '张教授团队',
      organization: '清华大学',
      publishDate: '2023-08-15',
      downloadCount: 3456,
      rating: 4.8,
      tags: ['深度学习', '医疗AI', '诊断'],
      abstract: '本报告详细介绍了深度学习技术在医疗影像诊断、疾病预测等领域的最新进展...',
      pages: 68,
      language: '中文',
      size: '5.2MB',
    },
    {
      key: '2',
      title: '5G网络优化白皮书',
      type: '白皮书',
      author: '华为5G团队',
      organization: '华为技术有限公司',
      publishDate: '2023-07-20',
      downloadCount: 2890,
      rating: 4.9,
      tags: ['5G', '网络优化', '通信'],
      abstract: '全面阐述5G网络部署和优化的关键技术，包括信号覆盖、容量规划等...',
      pages: 124,
      language: '中英双语',
      size: '8.9MB',
    },
    {
      key: '3',
      title: '新材料研发标准化流程',
      type: '标准文档',
      author: '材料科学委员会',
      organization: '中科院材料所',
      publishDate: '2023-06-10',
      downloadCount: 1567,
      rating: 4.6,
      tags: ['新材料', '标准化', '研发流程'],
      abstract: '建立新材料从设计、合成、测试到应用的标准化研发流程体系...',
      pages: 89,
      language: '中文',
      size: '3.7MB',
    },
  ];

  // 知识产权数据
  const ipData = [
    {
      key: '1',
      name: '智能图像识别算法专利',
      type: '发明专利',
      number: 'CN202310123456.7',
      owner: '清华大学',
      inventor: '张教授, 李博士',
      applicationDate: '2023-03-15',
      status: '已授权',
      field: '人工智能',
      licensing: 'available',
      price: '面议',
      description: '一种基于深度学习的高精度图像识别方法，适用于工业检测领域',
      citations: 23,
    },
    {
      key: '2',
      name: '新型锂电池材料',
      type: '发明专利',
      number: 'CN202310234567.8',
      owner: '比亚迪股份有限公司',
      inventor: '王工程师团队',
      applicationDate: '2023-02-20',
      status: '实质审查',
      field: '新能源',
      licensing: 'exclusive',
      price: '1000万元',
      description: '高能量密度、长循环寿命的锂电池正极材料及其制备方法',
      citations: 8,
    },
  ];

  const equipmentColumns = [
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.image} size={40} style={{ marginRight: 12 }} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{record.model}</div>
          </div>
        </div>
      ),
    },
    {
      title: '设备类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => <Tag color='blue'>{category}</Tag>,
    },
    {
      title: '所在位置',
      dataIndex: 'location',
      key: 'location',
      width: 120,
      ellipsis: true,
    },
    {
      title: '使用状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          available: { color: 'success', text: '可预约' },
          busy: { color: 'warning', text: '使用中' },
          maintenance: { color: 'error', text: '维护中' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Badge status={statusInfo.color as any} text={statusInfo.text} />;
      },
    },
    {
      title: '费用',
      dataIndex: 'price',
      key: 'price',
      width: 100,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_: any, record: any) => (
        <div>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            查看详情
          </Button>
          <Button
            type='link'
            size='small'
            icon={<CalendarOutlined />}
            disabled={record.status !== 'available'}
          >
            预约
          </Button>
        </div>
      ),
    },
  ];

  const datasetColumns = [
    {
      title: '数据集名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.type} | {record.size} | {record.format}
          </div>
        </div>
      ),
    },
    {
      title: '领域',
      dataIndex: 'domain',
      key: 'domain',
      width: 100,
      render: (domain: string) => <Tag color='green'>{domain}</Tag>,
    },
    {
      title: '提供方',
      dataIndex: 'provider',
      key: 'provider',
      width: 120,
      ellipsis: true,
    },
    {
      title: '开放程度',
      dataIndex: 'openness',
      key: 'openness',
      width: 100,
      render: (openness: string) => {
        const opennessMap = {
          public: { color: 'success', text: '公开' },
          restricted: { color: 'warning', text: '受限' },
          internal: { color: 'error', text: '内部' },
        };
        const info = opennessMap[openness as keyof typeof opennessMap];
        return <Tag color={info.color}>{info.text}</Tag>;
      },
    },
    {
      title: '下载量',
      dataIndex: 'downloads',
      key: 'downloads',
      width: 80,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: () => (
        <div>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            详情
          </Button>
          <Button type='link' size='small' icon={<DownloadOutlined />}>
            下载
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className='resources-sharing-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>资源目录与共享</h1>
            <p>整合优质科研资源，构建开放共享的资源生态体系</p>
            <div className='header-actions'>
              <Button type='primary' size='large' icon={<PlusOutlined />}>
                发布资源
              </Button>
              <Button size='large' icon={<ShareAltOutlined />}>
                资源协作
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            {resourceStats.map((stat, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className='stat-card'>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    prefix={stat.prefix}
                    valueStyle={{ color: stat.color }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 资源分类标签页 */}
      <section className='resources-section'>
        <div className='container'>
          <Card>
            <Tabs defaultActiveKey='equipment' size='large'>
              {/* 科研设备 */}
              <TabPane
                tab={
                  <span>
                    <ExperimentOutlined />
                    科研设备
                  </span>
                }
                key='equipment'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索设备名称或型号'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索设备:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类别</Option>
                      <Option value='analysis'>材料分析</Option>
                      <Option value='biological'>生物实验</Option>
                      <Option value='optical'>光学设备</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部状态</Option>
                      <Option value='available'>可预约</Option>
                      <Option value='busy'>使用中</Option>
                    </Select>
                  </div>
                  <Button
                    type='primary'
                    icon={<PlusOutlined />}
                    onClick={() => setIsEquipmentModalVisible(true)}
                  >
                    申请设备共享
                  </Button>
                </div>
                <Table
                  dataSource={equipmentData}
                  columns={equipmentColumns}
                  pagination={{ pageSize: 6 }}
                  scroll={{ x: 1000 }}
                />
              </TabPane>

              {/* 数据资源 */}
              <TabPane
                tab={
                  <span>
                    <DatabaseOutlined />
                    数据资源
                  </span>
                }
                key='data'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索数据集'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索数据:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部领域</Option>
                      <Option value='ai'>人工智能</Option>
                      <Option value='medical'>医疗健康</Option>
                      <Option value='finance'>金融科技</Option>
                    </Select>
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类型</Option>
                      <Option value='image'>图像数据</Option>
                      <Option value='text'>文本数据</Option>
                      <Option value='time'>时序数据</Option>
                    </Select>
                  </div>
                  <Button
                    type='primary'
                    icon={<PlusOutlined />}
                    onClick={() => console.log('发布数据集')}
                  >
                    发布数据集
                  </Button>
                </div>
                <Table
                  dataSource={datasetData}
                  columns={datasetColumns}
                  pagination={{ pageSize: 6 }}
                  scroll={{ x: 1000 }}
                />
              </TabPane>

              {/* 技术文档 */}
              <TabPane
                tab={
                  <span>
                    <FileTextOutlined />
                    技术文档
                  </span>
                }
                key='documents'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索文档标题或作者'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索文档:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类型</Option>
                      <Option value='report'>技术报告</Option>
                      <Option value='whitepaper'>白皮书</Option>
                      <Option value='standard'>标准文档</Option>
                    </Select>
                  </div>
                  <Button
                    type='primary'
                    icon={<PlusOutlined />}
                    onClick={() => console.log('上传文档')}
                  >
                    上传文档
                  </Button>
                </div>
                <Row gutter={[16, 16]}>
                  {documentData.map(doc => (
                    <Col xs={24} lg={8} key={doc.key}>
                      <Card
                        title={doc.title}
                        extra={<Rate disabled defaultValue={doc.rating} />}
                        actions={[
                          <EyeOutlined key='view' />,
                          <DownloadOutlined key='download' />,
                          <HeartOutlined key='favorite' />,
                        ]}
                      >
                        <div className='document-info'>
                          <p>
                            <strong>作者:</strong> {doc.author}
                          </p>
                          <p>
                            <strong>机构:</strong> {doc.organization}
                          </p>
                          <p>
                            <strong>发布时间:</strong> {doc.publishDate}
                          </p>
                          <p>
                            <strong>下载量:</strong> {doc.downloadCount}
                          </p>
                          <div className='document-tags'>
                            {doc.tags.map(tag => (
                              <Tag key={tag} color='blue'>
                                {tag}
                              </Tag>
                            ))}
                          </div>
                          <p className='document-abstract'>{doc.abstract}</p>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>

              {/* 知识产权 */}
              <TabPane
                tab={
                  <span>
                    <CopyrightOutlined />
                    知识产权
                  </span>
                }
                key='ip'
              >
                <div className='tab-header'>
                  <div className='search-filters'>
                    <Search
                      placeholder='搜索专利名称或号码'
                      style={{ width: 300 }}
                      onSearch={value => console.log('搜索专利:', value)}
                    />
                    <Select defaultValue='all' style={{ width: 120 }}>
                      <Option value='all'>全部类型</Option>
                      <Option value='invention'>发明专利</Option>
                      <Option value='utility'>实用新型</Option>
                      <Option value='design'>外观设计</Option>
                    </Select>
                  </div>
                </div>
                <Row gutter={[16, 16]}>
                  {ipData.map(ip => (
                    <Col xs={24} lg={12} key={ip.key}>
                      <Card>
                        <Descriptions title={ip.name} column={1} size='small'>
                          <Descriptions.Item label='专利类型'>
                            <Tag color='purple'>{ip.type}</Tag>
                          </Descriptions.Item>
                          <Descriptions.Item label='专利号'>{ip.number}</Descriptions.Item>
                          <Descriptions.Item label='专利权人'>{ip.owner}</Descriptions.Item>
                          <Descriptions.Item label='发明人'>{ip.inventor}</Descriptions.Item>
                          <Descriptions.Item label='申请时间'>
                            {ip.applicationDate}
                          </Descriptions.Item>
                          <Descriptions.Item label='当前状态'>
                            <Tag color={ip.status === '已授权' ? 'success' : 'processing'}>
                              {ip.status}
                            </Tag>
                          </Descriptions.Item>
                          <Descriptions.Item label='技术领域'>
                            <Tag color='green'>{ip.field}</Tag>
                          </Descriptions.Item>
                          <Descriptions.Item label='许可方式'>
                            <Tag color={ip.licensing === 'available' ? 'blue' : 'orange'}>
                              {ip.licensing === 'available' ? '开放许可' : '独占许可'}
                            </Tag>
                          </Descriptions.Item>
                          <Descriptions.Item label='许可费用'>{ip.price}</Descriptions.Item>
                        </Descriptions>
                        <p style={{ marginTop: 16, color: '#666' }}>{ip.description}</p>
                        <div style={{ marginTop: 16 }}>
                          <Button type='primary' size='small'>
                            申请许可
                          </Button>
                          <Button type='link' size='small'>
                            查看详情
                          </Button>
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>
            </Tabs>
          </Card>
        </div>
      </section>

      {/* 设备共享申请模态框 */}
      <Modal
        title='申请设备共享'
        visible={isEquipmentModalVisible}
        onOk={() => setIsEquipmentModalVisible(false)}
        onCancel={() => setIsEquipmentModalVisible(false)}
        width={600}
      >
        <Form form={form} layout='vertical'>
          <Form.Item label='设备名称' name='equipmentName' rules={[{ required: true }]}>
            <Input placeholder='请输入设备名称' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='设备型号' name='model' rules={[{ required: true }]}>
                <Input placeholder='请输入设备型号' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='设备类别' name='category' rules={[{ required: true }]}>
                <Select placeholder='请选择设备类别'>
                  <Option value='analysis'>材料分析</Option>
                  <Option value='biological'>生物实验</Option>
                  <Option value='optical'>光学设备</Option>
                  <Option value='mechanical'>机械设备</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='设备描述' name='description' rules={[{ required: true }]}>
            <TextArea rows={4} placeholder='请详细描述设备功能、技术参数等' />
          </Form.Item>
          <Form.Item label='设备图片' name='images'>
            <Upload listType='picture-card'>
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ResourcesSharingPage;
