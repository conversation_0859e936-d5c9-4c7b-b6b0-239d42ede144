import React from 'react';
import ReactECharts from 'echarts-for-react';
import './index.less';

type TrendPoint = { time: string; value: number };
type TopApp = { rank: number; name: string; usage: number };

export interface DashboardRightProps {
    metrics: {
        // 安全运营总览
        complianceScore: number; // 安全指数 %
        securityLevel: number;   // 新增告警数
        riskLevel: number;       // 平均响应时长（h）
        responseTime: number;    // 防护覆盖率 %（设计命名沿用左侧示例）

        // 应用订阅趋势
        trendData: TrendPoint[];

        // 热门应用 TOP
        topApps: TopApp[];
    };
}

const DashboardRight: React.FC<DashboardRightProps> = ({ metrics }) => {
    const normalizeUsage = (val: number) => (val < 1000 ? Math.round(val * 1000) : Math.round(val));
    const formatNumber = (val: number) => normalizeUsage(val).toLocaleString('zh-CN');

    const maxUsage = Math.max(...metrics.topApps.map(a => normalizeUsage(a.usage)), 1);

    const trendOption = {
        tooltip: { trigger: 'axis' },
        grid: { left: 30, right: 10, top: 30, bottom: 30 },
        xAxis: {
            type: 'category',
            data: metrics.trendData.map(p => p.time),
            boundaryGap: false,
            axisLine: { lineStyle: { color: 'rgba(255,255,255,0.2)' } },
            axisLabel: { color: 'rgba(229,240,255,0.8)' }
        },
        yAxis: {
            type: 'value',
            axisLine: { show: false },
            splitLine: { lineStyle: { color: 'rgba(255,255,255,0.08)' } },
            axisLabel: { color: 'rgba(229,240,255,0.8)' }
        },
        legend: {
            data: ['类别一', '类别二'],
            textStyle: { color: '#E5F0FF' },
            right: 10,
            top: 0
        },
        series: [
            {
                name: '类别一',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                lineStyle: { color: '#3AA0FF' },
                itemStyle: { color: '#3AA0FF' },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(58,160,255,0.35)' },
                            { offset: 1, color: 'rgba(58,160,255,0.05)' }
                        ]
                    }
                },
                data: metrics.trendData.map(p => p.value)
            },
            {
                name: '类别二',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                lineStyle: { color: '#FFCF5C' },
                itemStyle: { color: '#FFCF5C' },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: 'rgba(255,207,92,0.35)' },
                            { offset: 1, color: 'rgba(255,207,92,0.05)' }
                        ]
                    }
                },
                data: metrics.trendData.map(p => Math.max(1, Math.round(p.value * 0.7 + 6)))
            }
        ]
    };

    return (
        <div className='dashboard-right'>
            {/* 安全运营总览 */}
            <div className='panel-card security-overview'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>安全运营总览</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content'>
                    <div className='overview-grid'>
                        <div className='overview-col'>
                            <div className='section-container'>
                                <div className='security-item'>
                                    <img src='/platform/images/security-dashboard/security-item-bg-1.png' alt='' className='security-item-bg' />
                                    <div className='state-item'>
                                        <div className='label'>新增告警数</div>
                                        <div className='value-row'>
                                            <span className='value'>{metrics.securityLevel}</span>
                                            <span className='unit'>个</span>
                                        </div>
                                    </div>
                                </div>
                                <div className='section-container'>
                                    <div className='security-item'>
                                        <img src='/platform/images/security-dashboard/security-item-bg-2.png' alt='' className='security-item-bg' />
                                        <div className='state-item'>
                                            <div className='label'>均响应时长</div>
                                            <div className='value-row'>
                                                <span className='value'>{metrics.riskLevel}</span>
                                                <span className='unit'>h</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div className='overview-center'>
                            <img src='/platform/images/security-dashboard/security-item-bg-center.png' alt='' className='security-item-center-bg' />
                            <div className='inner'>
                                <div className='inner-percent'>
                                    <span className='percent-number'>{metrics.complianceScore}</span>
                                    <span className='percent-unit'>%</span>
                                </div>
                                <div className='inner-label'>安全指数</div>
                            </div>
                        </div>
                        <div className='overview-col'>
                            <div className='section-container'>
                                <div className='security-item'>
                                    <img src='/platform/images/security-dashboard/security-item-bg-3.png' alt='' className='security-item-bg' />
                                    <div className='state-item'>
                                        <div className='label'>待处理告警</div>
                                        <div className='value-row'>
                                            <span className='value'>2</span>
                                            <span className='unit'>个</span>
                                        </div>
                                    </div>
                                </div>
                                <div className='section-container'>
                                    <div className='security-item'>
                                        <img src='/platform/images/security-dashboard/security-item-bg-4.png' alt='' className='security-item-bg' />
                                        <div className='state-item'>
                                            <div className='label'>防护覆盖率</div>
                                            <div className='value-row'>
                                                <span className='value'>{metrics.responseTime}</span>
                                                <span className='unit'>%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* 应用订阅趋势 */}
            <div className='panel-card subscription-trend'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>应用订阅趋势</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content trend-content'>
                    <div className='trend-left-label'>订阅次数</div>
                    <ReactECharts option={trendOption} style={{ height: 220 }} />
                </div>
            </div>

            {/* 热门应用TOP */}
            <div className='panel-card hot-apps'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>热门应用TOP</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content apps-list'>
                    {metrics.topApps.map(app => {
                        const value = normalizeUsage(app.usage);
                        const percent = Math.max(0.05, value / maxUsage);
                        const isUp = app.rank === 1 || app.rank === 4;
                        const isDown = app.rank === 2;
                        return (
                            <div className='app-row' key={app.rank}>
                                <div className='app-left'>
                                    <span className='rank'>NO.{app.rank}</span>
                                    <span className='name'>
                                        {app.name}
                                        {isUp && <span className='trend up' />}
                                        {isDown && <span className='trend down' />}
                                    </span>
                                </div>
                                <div className='app-right'>
                                    <div className='progress'>
                                        <div className='bar' style={{ width: `${percent * 100}%` }} />
                                    </div>
                                    <div className='usage'>
                                        <span className='table-num'>{formatNumber(app.usage)}</span>
                                        <span className='table-unit'>次</span>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default DashboardRight;


