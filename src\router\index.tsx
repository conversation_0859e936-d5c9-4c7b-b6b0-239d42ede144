import React, { Suspense, useEffect } from 'react';
import { createBrowserRouter, RouterProvider, useLocation } from 'react-router-dom';
import { routes } from './config';
import { RouteGuard } from './guards';
import type { RouteConfig } from './types';

import { getPageTitle } from './config';
import { Spin } from 'antd';

// 加载组件
const LoadingComponent: React.FC = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <Spin size='large' tip='页面加载中...' />
  </div>
);

// 页面标题更新Hook
const usePageTitleUpdater = () => {
  const location = useLocation();

  useEffect(() => {
    const pageTitle = getPageTitle(location.pathname);
    const fullTitle = `${pageTitle} - 智能门户`;
    document.title = fullTitle;

    // 开发环境下的调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('🏷️ 页面标题更新:', {
        路径: location.pathname,
        页面标题: pageTitle,
        完整标题: fullTitle,
      });
    }
  }, [location.pathname]);
};

// 路由包装组件
const RouteWrapper: React.FC<{
  component: React.ComponentType;
  meta?: any;
}> = ({ component: Component, meta }) => {
  usePageTitleUpdater();

  return (
    <Suspense fallback={<LoadingComponent />}>
      <RouteGuard requiresAuth={meta?.requiresAuth} roles={meta?.roles} guestOnly={meta?.guestOnly}>
        <Component />
      </RouteGuard>
    </Suspense>
  );
};

// 根据配置创建路由
const createRoutes = (): any[] => {
  const processRoute = (route: RouteConfig): any => ({
    path: route.path,
    element: <RouteWrapper component={route.component} meta={route.meta} />,
    children: route.children?.map((child: RouteConfig) => processRoute(child)),
  });

  return routes.map(processRoute);
};

// 创建路由器
const router = createBrowserRouter([
  ...createRoutes(),
  // 根路径重定向 (暂时注释，使用默认首页)
  // {
  //   path: "/",
  //   element: <Navigate to="/chat" replace />,
  // },
]);

// 路由提供者组件
export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

// 导出路由相关工具
export { routes, getNavigationItems } from './config';
export * from './guards';
export * from './types';
