.policy-file {
  width: 100%;
  background: var(---fill-7, #f5f7fa);

  .policy-file-container {
    width: 1240px;
    padding: 52px 0;
    margin: 0 auto;

    .policy-file-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      align-self: stretch;
      .policy-file-item {
        width: 100%;
        height: 120px;
        display: flex;
        padding: 16px;
        justify-content: center;
        align-items: center;
        background: var(---, #fff);
        cursor: pointer;

        .policy-file-item-date {
          display: flex;
          width: 88px;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          align-self: stretch;
          border-radius: 2px;
          background: var(---fill-7, #f5f7fa);

          > h3 {
            overflow: hidden;
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-overflow: ellipsis;
            font-family: Poppins;
            font-size: 40px;
            font-style: normal;
            font-weight: 600;
            line-height: 110%; /* 44px */
          }
          > p {
            overflow: hidden;
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-overflow: ellipsis;

            /* t3 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 21px */
          }
        }

        .policy-file-item-content {
          display: flex;
          padding: 16px 32px;
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
          flex: 1 0 0;
          align-self: stretch;
          background: var(---, #fff);

          > h4 {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            align-self: stretch;
            overflow: hidden;
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-overflow: ellipsis;

            /* t1 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 24px */
          }

          > p {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            flex: 1 0 0;
            overflow: hidden;
            color: var(---45, rgba(0, 0, 0, 0.45));
            text-overflow: ellipsis;

            /* t2-段落 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 180%; /* 28.8px */
          }
        }

        .policy-file-item-all-arrow {
          display: flex;
          width: 48px;
          height: 48px;
          padding: 5px 3px;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 10px;
          flex-shrink: 0;
          aspect-ratio: 1/1;
          border-radius: 100px;
          background: var(---fill-7, #f5f7fa);

          background-image: url(/platform/images/news-center/industry-informations-right.svg);

          background-repeat: no-repeat;
          background-position: center;
        }

        &:hover {
          .policy-file-item-date {
            border-radius: 2px;
            background: var(---Brand1-5, #37f);
            > h3,
            > p {
              color: var(---, #fff);
            }
          }
          .policy-file-item-content > h4 {
            color: var(---Brand1-5, #37f);
          }
          .policy-file-item-all-arrow {
            background-image: url(/platform/images/news-center/industry-informations-right-hover.svg);
          }
        }
      }
    }
  }
}
