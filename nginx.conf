user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 声明环境变量
env ADMIN_SERVER;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    # 开启 Gzip
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 8000;
        server_name localhost;

            # 根路径重定向到 /platform/
    location = / {
        return 301 /platform/;
    }

    # /platform/ 路径下的JS/CSS文件 - 如果不存在返回404
    location ~* ^/platform/.+\.(js|css|map)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # /platform/ 路径下的其他静态资源文件
    location ~* ^/platform/.+\.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # /platform/ SPA 路由配置 - 只有非静态文件才回退到index.html
    location /platform/ {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /platform/index.html;
    }

        # API 代理
        location /api/ {
            proxy_pass $ADMIN_SERVER;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            return 200 'healthy\n';
        }
    }
}
