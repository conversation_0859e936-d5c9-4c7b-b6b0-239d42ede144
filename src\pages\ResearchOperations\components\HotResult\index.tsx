import React from 'react';
import './index.less';

const hotResult = [
  {
    id: 1,
    title: '深度问数-数据治理平台',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '上海汇智灵曦数字科技有限公司',
    direction: 'AI医疗',
    username: '<PERSON>',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 100,
    collectionCount: 100,
    img: '/platform/images/research-operations/hot-result-1.jpg',
    link: 'http://www.aethermind.cn/frontend/',
  },
  {
    id: 2,
    title: '训练推理一体化平台',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '上海汇智灵曦数字科技有限公司',
    direction: 'AI医疗',
    username: '<PERSON>',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 100,
    collectionCount: 100,
    img: '/platform/images/research-operations/hot-result-2.jpg',
    link: 'http://wenshu.aethermind.cn/welcome',
  },
  {
    id: 3,
    title: '灵犀助手-医学研究助手',
    content:
      '在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等...',
    company: '上海汇智灵曦数字科技有限公司',
    direction: 'AI医疗',
    username: 'Arthur',
    userAvatar: '/platform/images/research-operations/hot-result-user.jpg',
    viewCount: 100,
    collectionCount: 100,
    img: '/platform/images/research-operations/hot-result-3.jpg',
    link: 'http://kyan.aethermind.cn/#/login',
  },
];

const HotResult: React.FC = () => {
  const handleItemClick = (link: string) => {
    window.open(link, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className='hot-result'>
      <div className='hot-result-container'>
        <div className='hot-result-title'>
          <h2>热门研究成果</h2>
          <p>
            查看更多
            <img src='/platform/images/research-operations/find-more.svg' alt='find-more' />
          </p>
        </div>
        <div className='hot-result-content'>
          {hotResult.map(item => (
            <div
              className={`hot-result-item ${item.id === 2 ? 'active' : ''}`}
              key={item.id}
              onClick={() => handleItemClick(item.link)}
            >
              <div className='hot-result-item-img'>
                <img src={item.img} alt='' />
              </div>
              <div className='hot-result-item-info'>
                <div className='hot-result-item-info-container'>
                  <h3>{item.title}</h3>
                  <p className='hot-result-item-info-company'>
                    <span>{item.company}</span>
                    <span>{item.direction}</span>
                  </p>
                  <p className='hot-result-item-info-content'>{item.content}</p>
                </div>
                <div className='hot-result-item-footer'>
                  <div className='hot-result-item-footer-left'>
                    <img src={item.userAvatar} alt='user-avatar' />
                    <span>{item.username}</span>
                  </div>
                  <div className='hot-result-item-footer-right'>
                    <div>
                      <img
                        src='/platform/images/research-operations/hot-result-view.svg'
                        alt='view'
                      />
                      <span>{item.viewCount}</span>
                    </div>
                    <div>
                      <img
                        src='/platform/images/research-operations/hot-result-collection.svg'
                        alt='collection'
                      />
                      <span>{item.collectionCount}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HotResult;
