.research-operations-hero-section {
  width: 100%;
  height: 641px;
  margin-top: 64px;
  background: url('/platform/images/research-operations/hero-banner.png') lightgray 50% / cover
    no-repeat;
  background-size: cover;
  position: relative;
  .hero-section-content {
    width: 960px;
    margin: 0 auto;
    padding-top: 117px;
    display: flex;
    flex-direction: column;
    align-items: center;
    > h1 {
      width: 100%;
      color: var(---85, rgba(0, 0, 0, 0.85));
      text-align: center;
      font-family: 'Alibaba PuHuiTi';
      font-size: 48px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 72px */
      margin: 0;
      > span {
        background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: 'Alibaba PuHuiTi';
        font-size: 48px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
      }
    }
    .hero-section-search {
      width: 100%;
      margin-top: 72px;
      .hot-search {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 40px;
        > p {
          color: var(---45, rgba(0, 0, 0, 0.45));
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 36px */
          margin: 0;
        }
        .active {
          color: var(---Brand1-5, #37f);
          position: relative;
          &::after {
            display: block;
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(---Brand1-5, #37f);
          }
        }
      }
      .search-input-container {
        margin-top: 24px;
        width: 100%;
        display: flex;
        height: 72px;
        padding: 12px 16px 12px 24px;
        justify-content: space-between;
        align-items: center;
        border-radius: 52px;
        border: 1px solid var(---08, rgba(0, 0, 0, 0.08));
        background: #fff;
        .search-input {
          width: 80%;
          height: 100%;
          border: none;
          outline: none;
          background: transparent;
          color: var(---45, rgba(0, 0, 0, 1));
          font-family: 'Alibaba PuHuiTi';
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 30px */
          &::placeholder {
            color: var(---45, rgba(0, 0, 0, 0.45));
            font-family: 'Alibaba PuHuiTi';
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 30px */
          }
          &:focus,
          &::focus-within {
            border: none;
            outline: none;
            background: transparent;
            box-shadow: none;
          }
        }
        .search-btn {
          width: 86px;
          display: flex;
          height: 48px;
          padding: 0 10px;
          align-items: center;
          gap: 6px;
          border-radius: 45px;
          background: var(---08, rgba(0, 0, 0, 0.08));
          color: var(---65, rgba(0, 0, 0, 0.65));
          font-family: 'Alibaba PuHuiTi';
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 30px */
        }
      }
      .release-btns {
        margin-top: 40px;
        display: flex;
        justify-content: center;
        gap: 24px;
        .release-btn {
          width: 182px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 41px;
          border: 1px solid var(---12, rgba(0, 0, 0, 0.12));
          background: linear-gradient(180deg, #fff 0%, #ebf5fb 100%);
          box-shadow: 0 4px 4px 0 rgba(17, 62, 91, 0.18);
          color: var(---85, rgba(0, 0, 0, 0.85));
          text-align: center;
          font-family: 'Alibaba PuHuiTi';
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 27px */
          &.high-btn {
            border-radius: 41px;
            border: 1px solid var(---Brand1-5, #37f);
            background: var(---Brand1-5, #37f);
            box-shadow: 0 4px 4px 0 rgba(17, 62, 91, 0.18);
            color: #fff;
          }
        }
      }
    }
  }
  .hero-info {
    width: 1240px;
    height: 164px;
    position: absolute;
    bottom: -82px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    border-radius: 12px;
    background: #fff;

    /* 卡片投影 */
    box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
    .hero-info-item {
      width: 25%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      .hero-info-item-title {
        color: var(---45, rgba(0, 0, 0, 0.45));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 36px */
        margin: 0;
      }
      .hero-info-item-value {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 35px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 52.5px */
        margin: 0;
      }
    }
  }
}
