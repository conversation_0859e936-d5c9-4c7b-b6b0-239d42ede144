// Token 管理工具
// 支持localStorage和Cookie两种存储方式

// Token键名
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_INFO_KEY = 'user_info';

// Cookie相关配置
const COOKIE_TOKEN_KEY = 'Admin-Token';

/**
 * 获取访问token
 * @returns string | null
 */
export function getToken(): string | null {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
}

/**
 * 设置访问token
 * @param token 访问token
 */
export function setToken(token: string): void {
  localStorage.setItem(ACCESS_TOKEN_KEY, token);
  // 同时设置cookie以兼容旧系统
  setCookie(COOKIE_TOKEN_KEY, token);
}

/**
 * 移除访问token
 */
export function removeToken(): void {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  removeCookie(COOKIE_TOKEN_KEY);
}

/**
 * 获取刷新token
 * @returns string | null
 */
export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
}

/**
 * 设置刷新token
 * @param token 刷新token
 */
export function setRefreshToken(token: string): void {
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
}

/**
 * 移除刷新token
 */
export function removeRefreshToken(): void {
  localStorage.removeItem(REFRESH_TOKEN_KEY);
}

/**
 * 获取用户信息
 * @returns any | null
 */
export function getUserInfo(): any | null {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
}

/**
 * 设置用户信息
 * @param userInfo 用户信息
 */
export function setUserInfo(userInfo: any): void {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
}

/**
 * 移除用户信息
 */
export function removeUserInfo(): void {
  localStorage.removeItem(USER_INFO_KEY);
}

/**
 * 清除所有认证信息
 */
export function clearAuth(): void {
  removeToken();
  removeRefreshToken();
  removeUserInfo();
}

/**
 * 检查是否已登录
 * @returns boolean
 */
export function isLoggedIn(): boolean {
  return !!getToken();
}

// Cookie工具函数
function setCookie(name: string, value: string, days: number = 7): void {
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
}

function getCookie(name: string): string | null {
  const nameEQ = name + '=';
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function removeCookie(name: string): void {
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/`;
}

// 兼容旧系统的Cookie方法
export const cookieUtils = {
  get: getCookie,
  set: setCookie,
  remove: removeCookie,
};
