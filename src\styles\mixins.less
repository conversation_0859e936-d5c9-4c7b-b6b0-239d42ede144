// Less Mixins 混入
@import './variables.less';

// 居中对齐
.center-flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 文本省略
.text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略
.text-ellipsis-multi(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 边框圆角
.border-radius(@radius: @border-radius-base) {
  border-radius: @radius;
}

// 阴影
.box-shadow(@shadow: @box-shadow-base) {
  box-shadow: @shadow;
}

// 渐变背景
.gradient-bg(@start-color, @end-color, @direction: to bottom) {
  background: linear-gradient(@direction, @start-color, @end-color);
}

// 响应式断点
.respond-to(@breakpoint) when (@breakpoint = xs) {
  @media (max-width: @screen-xs) {
    & {
      .respond-content();
    }
  }
}
.respond-to(@breakpoint) when (@breakpoint = sm) {
  @media (max-width: @screen-sm) {
    & {
      .respond-content();
    }
  }
}
.respond-to(@breakpoint) when (@breakpoint = md) {
  @media (max-width: @screen-md) {
    & {
      .respond-content();
    }
  }
}
.respond-to(@breakpoint) when (@breakpoint = lg) {
  @media (max-width: @screen-lg) {
    & {
      .respond-content();
    }
  }
}
.respond-to(@breakpoint) when (@breakpoint = xl) {
  @media (max-width: @screen-xl) {
    & {
      .respond-content();
    }
  }
}

// 按钮样式
.button-variant(@color, @background, @border) {
  color: @color;
  background-color: @background;
  border-color: @border;

  &:hover {
    color: @color;
    background-color: lighten(@background, 10%);
    border-color: lighten(@border, 10%);
  }

  &:active {
    color: @color;
    background-color: darken(@background, 5%);
    border-color: darken(@border, 5%);
  }
}
