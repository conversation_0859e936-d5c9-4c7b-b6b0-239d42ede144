.about-solve-plan {
  display: flex;
  height: 100vh;
  padding: 181px 0 28px 0;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  align-self: stretch;
  background-image: url('/platform/images/about/solve-plan-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .about-solve-plan-container {
    width: 1240px;
    margin: 0 auto;

    .about-solve-plan-content {
      display: flex;
      flex-direction: column;
      gap: 52px;

      > h2 {
        display: flex;
        width: 1240px;
        flex-direction: column;
        justify-content: center;
        flex-shrink: 0;
        align-self: stretch;
        color: var(---, #fff);
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }

      .about-solve-plan-content-info {
        width: 814px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        > h3 {
          overflow: hidden;
          color: var(---, #fff);
          text-overflow: ellipsis;

          /* h1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 36px */
        }

        > p {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 8;
          align-self: stretch;
          overflow: hidden;
          color: var(---, #fff);
          text-overflow: ellipsis;

          /* t2-段落 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 180%; /* 28.8px */
        }
      }
    }

    .about-solve-plan-list {
      margin-top: 130px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .about-solve-plan-list-item {
        display: flex;
        flex-direction: column;
        height: 200px;
        padding: 24px 32px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-color: #fff;
        border-radius: 16px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 2px;
        border: 4px solid var(---50, rgba(255, 255, 255, 0.5));
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.62) 0%, rgba(0, 0, 0, 0.62) 100%);

        > h3 {
          width: 100%;
          overflow: hidden;
          color: var(---, #fff);
          text-overflow: ellipsis;

          /* h1 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 36px */
        }

        > p {
          width: 100%;
          overflow: hidden;
          color: var(---, #fff);
          text-overflow: ellipsis;
          font-family: 'Alibaba PuHuiTi';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 21px */
          text-transform: uppercase;
        }
      }
    }
  }
}
