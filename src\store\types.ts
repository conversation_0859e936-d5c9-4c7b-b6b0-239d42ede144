// Store 类型定义
import type { User, LoginResponse } from '../api/types';

// 用户状态
export interface UserState {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface UserActions {
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (username: string, password: string) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  clearError: () => void;
  initUserFromStorage: () => void;
  updateUserInfo: () => Promise<User>;
}

// 应用状态（可选，根据需要使用）
export interface AppState {
  loading: boolean;
}

export interface AppActions {
  setLoading: (loading: boolean) => void;
}
