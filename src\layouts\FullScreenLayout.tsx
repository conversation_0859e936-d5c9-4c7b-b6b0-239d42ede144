import React from 'react';
import { Outlet } from 'react-router-dom';
import './FullScreenLayout.less';

interface FullScreenLayoutProps {
  // 可以接收一些全局配置props
}

const FullScreenLayout: React.FC<FullScreenLayoutProps> = () => {
  return (
    <div className='fullscreen-layout'>
      <main className='fullscreen-content'>
        <Outlet />
      </main>
    </div>
  );
};

export default FullScreenLayout;
