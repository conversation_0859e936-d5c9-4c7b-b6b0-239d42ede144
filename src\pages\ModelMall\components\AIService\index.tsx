import React from 'react';
import './index.less';
import { Button } from 'antd';

const serviceContent = [
  {
    title: 'resnet18',
    img: '/platform/images/app-mall/app-2.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'resnet3d_18',
    img: '/platform/images/app-mall/app-2.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'yolov3-tinyu',
    img: '/platform/images/app-mall/app-5.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'yolo12m.pt',
    img: '/platform/images/app-mall/app-5.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'denseunet',
    img: '/platform/images/app-mall/app-1.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'tv_vit_l_32',
    img: '/platform/images/app-mall/app-2.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'unet_2plus',
    img: '/platform/images/app-mall/app-1.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'yolo12m.pt',
    img: '/platform/images/app-mall/app-5.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
  {
    title: 'denseunet',
    img: '/platform/images/app-mall/app-1.jpg',
    tags: ['分类', 'CT', 'MR', 'PET', '病理'],
    play: '12.8k',
    star: '699',
    date: '2025-1-10',
  },
];

const AIService: React.FC = () => {
  const handleDetailClick = () => {
    window.open('http://www.aethermind.cn/frontend/', '_blank', 'noopener,noreferrer');
  };

  const handleUseClick = () => {
    window.open('http://www.aethermind.cn/frontend/', '_blank', 'noopener,noreferrer');
  };

  return (
    <section className='ai-service-section'>
      <div className='service-container'>
        <div className='service-title'>
          <h2>模型API服务</h2>
          <div className='service-title-desc'>
            <p>标签灵活管理，模型与机构权限清晰可控</p>
            <p className='service-title-desc-btn'>
              查看全部应用
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
            </p>
          </div>
        </div>
        <div className='service-content'>
          {serviceContent.map(item => (
            <div className='service-content-item'>
              <div className='service-content-item-top'>
                <div className='item-img'>
                  <img src={item.img} alt={item.title} />
                </div>
                <div className='item-info'>
                  <p>{item.title}</p>
                  <div className='item-tags'>
                    {item.tags.map(tag => (
                      <p>{tag}</p>
                    ))}
                  </div>
                </div>
              </div>
              <div className='service-content-item-bottom'>
                <div className='items-nums-wrap'>
                  <div className='item-nums'>
                    <p>
                      <img src='/platform/images/model-mall/ai-service-play.svg' alt='' />
                      <span>{item.play}</span>
                    </p>
                    <p>
                      <img src='/platform/images/model-mall/ai-service-star.svg' alt='' />
                      <span>{item.star}</span>
                    </p>
                  </div>
                  <p className='item-date'>更新于{item.date}</p>
                </div>
                <div className='service-content-btns'>
                  <Button className='info-btn' onClick={handleDetailClick}>
                    详情
                  </Button>
                  <Button className='use-btn' onClick={handleUseClick}>
                    使用
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AIService;
