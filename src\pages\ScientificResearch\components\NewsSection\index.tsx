import React from 'react';
import './index.less';

const NewsSection: React.FC = () => {
  // 处理新闻点击事件
  const handleNewsClick = (newsId: number) => {
    const newsLinks: { [key: number]: string } = {
      1: 'https://mp.weixin.qq.com/s/LFEzpmF135lwymXKR-yjIg', // WAIC2025世界人工智能大会
      2: 'https://mp.weixin.qq.com/s/oMV434Vzpfpz7_hX3xTWUA', // 深度问数：用AI解锁医疗数据价值，让决策"一问即达"
      3: 'https://mp.weixin.qq.com/s/JKr9J_qwKmVZaNO-ez4bKQ', // AI智能查房助手，开启智慧查房新"声"代！
      4: 'https://mp.weixin.qq.com/s/VsauF3x_-X5eju4PPZvL7A', // 训练推理一体化平台，破解大模型医疗落地难题
      5: 'https://mp.weixin.qq.com/s/4LPYR9UVmfMa8c7TTN52IA', // 邀请函|汇智灵曦诚邀您莅临WAIC2025世界人工智能大会
    };

    const link = newsLinks[newsId];
    if (link) {
      window.open(link, '_blank');
    }
  };
  const newsItems = [
    {
      id: 1,
      title: 'WAIC2025世界人工智能大会',
      content:
        '在即将到来的世界人工智能大会 （WAIC2025） 上，汇智灵曦数字科技有限公司 （AETHER MIND） 将携其最新的医疗智能化解决方案亮相大会。',
      date: '2025-7-26',
      image: '/platform/images/scientific-research/news-left-banner.png',
    },
    {
      id: 2,
      title: '2025世界人工智能大会（WAIC），河南豫健数智科技有限公司携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 3,
      title: '2025世界人工智能大会（WAIC），河南豫健数智科技有限公司携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 4,
      title: '2025世界人工智能大会（WAIC），河南豫健数智科技有限公司携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 5,
      title: '2025世界人工智能大会（WAIC），河南豫健数智科技有限公司携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
  ];

  return (
    <section className='newsSection'>
      <div className='sectionContainer'>
        <div className='newsBackground'>
          <div className='bgMain'></div>
          <div className='bgWorld'>
            <img src='/platform/images/scientific-research/news-banner.png' alt='世界地图' />
          </div>
        </div>

        <div className='newsContent'>
          <h2 className='sectionTitle'>最新资讯</h2>

          <div className='newsGrid'>
            <div
              className='featuredNews'
              onClick={() => handleNewsClick(newsItems[0].id)}
              style={{ cursor: 'pointer' }}
            >
              <div className='newsImage'>
                <img src={newsItems[0].image} alt={newsItems[0].title} />
              </div>
              <div className='newsInfo'>
                <h3 className='newsTitle'>{newsItems[0].title}</h3>
                <p className='newsContentText'>{newsItems[0].content}</p>
                <span className='newsDate'>{newsItems[0].date}</span>
              </div>
            </div>

            <div className='newsList'>
              {newsItems.slice(1).map(item => (
                <div
                  key={item.id}
                  className='newsItem'
                  onClick={() => handleNewsClick(item.id)}
                  style={{ cursor: 'pointer' }}
                >
                  <h4 className='newsItemTitle'>{item.title}</h4>
                  <span className='newsItemDate'>{item.date}</span>
                  <div className='newsDivider'></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
