.technical-guide-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 104px 20px;

  .guide-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .guide-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;

    h1 {
      color: white;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .ant-typography {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.9);
      max-width: 800px;
      margin: 0 auto;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  .guide-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .guide-card {
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: none;

    .ant-card-head {
      border-bottom: 2px solid #f0f0f0;
      background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
        color: #495057;

        .anticon {
          margin-right: 8px;
          color: #6c63ff;
        }
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .architecture-section {
    ul {
      list-style: none;
      padding: 0;

      li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ant-typography {
          color: #6c63ff;
        }
      }
    }
  }

  .security-section {
    ul {
      list-style: none;
      padding: 0;

      li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ant-typography {
          color: #28a745;
        }
      }
    }
  }

  .performance-section {
    ol {
      padding-left: 20px;

      li {
        padding: 8px 0;
        line-height: 1.6;

        .ant-typography {
          color: #6c63ff;
        }
      }
    }
  }

  .ant-collapse {
    border: none;
    background: #f8f9fa;

    .ant-collapse-item {
      border-bottom: 1px solid #dee2e6;

      &:last-child {
        border-bottom: none;
      }
    }

    .ant-collapse-header {
      background: white;
      font-weight: 600;
      color: #495057;
    }

    .ant-collapse-content {
      background: white;

      .ant-collapse-content-box {
        padding: 16px;
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #6c63ff;
      color: white;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f8f9ff;
    }
  }

  .ant-tag {
    border-radius: 12px;
    font-weight: 600;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .technical-guide-page {
    padding: 20px 10px;

    .guide-container {
      max-width: 100%;
    }

    .guide-header {
      h1 {
        font-size: 24px;
      }

      .ant-typography {
        font-size: 14px;
      }
    }

    .guide-card {
      margin: 0 -10px;
      border-radius: 8px;

      .ant-card-body {
        padding: 16px;
      }
    }

    .ant-table {
      font-size: 12px;
    }
  }
}
