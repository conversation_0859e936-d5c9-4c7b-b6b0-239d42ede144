import React from 'react';
import './index.less';

const CoreStrengths: React.FC = () => {
  return (
    <div className='core-strengths'>
      <div className='core-strengths-container'>
        <div className='core-strengths-title'>
          <h2>核心优势</h2>
          <p>直击医疗AI落地痛点</p>
        </div>
        <div className='core-strengths-content'>
          <div className='core-strengths-content-left'>
            <div className='content-left-title'>
              <h3>
                <span>01</span>
                ML全流程打通
              </h3>
              <p>
                自由组合算法节点，快速构建任务流程。支持单节点与整条Pipeline运行模式，满足不同阶段需求。
              </p>
            </div>
            <div className='content-left-desc'>
              <div className='content-left-desc-item'>
                <img src='/platform/images/model-mall/core-strengths-right.svg' alt='' />
                <p> DAG图形化流程构建</p>
              </div>
              <div className='content-left-desc-item'>
                <img src='/platform/images/model-mall/core-strengths-right.svg' alt='' />
                <p> 支持多种执行方式</p>
              </div>
              <div className='content-left-desc-item'>
                <img src='/platform/images/model-mall/core-strengths-right.svg' alt='' />
                <p> 所见即所得体验</p>
              </div>
            </div>
            <div className='content-left-progress'>
              <div className='active'></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
          <div className='core-strengths-content-right'>
            <div className='top-mask'></div>
            <div className='bottom-mask'></div>

            <img src='/platform/images/model-mall/core-strengths-content-1.jpg' alt='' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoreStrengths;
