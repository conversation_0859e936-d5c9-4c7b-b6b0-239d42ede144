import React from 'react';
import './index.less';

const TrainingCourse: React.FC = () => {
  const courses = [
    {
      image: "/platform/images/scientific-research/trainingCourse-list.png",
      title: '这里是关于医疗方面的培训课程名称',
      professor: 'XXX教授',
      date: '2025-03-25 12:00:00'
    },
    {
      image: "/platform/images/scientific-research/trainingCourse-list.png",
      title: '这里是关于医疗方面的培训课程名称',
      professor: 'XXX教授',
      date: '2025-03-25 12:00:00'
    },
    {
      image: "/platform/images/scientific-research/trainingCourse-list.png",
      title: '这里是关于医疗方面的培训课程名称',
      professor: 'XXX教授',
      date: '2025-03-25 12:00:00'
    }
  ];
  const CourseItem = (props: any) => {
    const { title, professor, date, image } = props
    return (
      <div className="course-item">
        <img src={image} alt="Icon" className="course-icon" />
        <div className="course-details" >
          <div className='courses-first-title'>
            <img src='/platform/images/scientific-research/TrainingCourse-item-detail-icon.png' alt='图标'width={25}height={25} />
            <h3>{title}</h3>
          </div>

          <p>{professor}<span style={{margin:"0 15px"}}>|</span>  {date}</p>
        </div>
      </div>
    )
  }
  return (
    <section className='trainingCourse'>
      <div className="newsContent">
        <h2 className="sectionTitle">课程培训</h2>
        <div className="newsGrid">

          <div className="featuredNews">
            <div className='assistantBg'>
              <img src='/platform/images/scientific-research/trainingCourse-bg.png' alt='背景' />
            </div>
            <div className='assistantInfo'>
              <div className='trainingNewClass'>最新课程</div>
              <div className='course-title'>
                <h1 className='course-title-h1'>流式细胞分析技术的应用</h1>
                <p className='course-title-p'>主讲导师：XXX教授</p>
              </div>
              <div className='bottomBar'>
                <span>开课时间：2025-08-30 18:00:00</span> <span>报名人数：200人</span>
              </div>
            </div>

            {/* <div className="newsInfo">
              </div> */}
          </div>
          <div>
            <div className='popular-courses'>
              <div className='hot-courses'>

                <div className='hot-courses-title'>
                  <img src="/platform/images/scientific-research/trainingCourse-icon.png" width={35} />

                  <h2>热门课程</h2>
                </div>
                <a href="#">查看更多 &gt;&gt;</a>
              </div>

              <div className="courseDetails">
                {courses.map((course, index) => (
                  <CourseItem
                    key={index}
                    image={course.image}
                    title={course.title}
                    professor={course.professor}
                    date={course.date}
                  />
                ))}
              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  )
}
export default TrainingCourse