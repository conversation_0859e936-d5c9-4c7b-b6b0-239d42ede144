import React from 'react';
import { useNavigate } from 'react-router-dom';
import './index.less';

const apps = [
  {
    id: 1,
    name: '灵曦助手',
    img: '/platform/images/app-mall/app-1.jpg',
    description: '专为科研人员、医生及医学领域工作者打造的智能辅助平台。',
    money: '19.00',
  },
  {
    id: 2,
    name: '深度文数',
    img: '/platform/images/app-mall/app-2.jpg',
    description: '自然语言 + 医疗数据库查询系统。',
    money: '19.00',
  },
  {
    id: 3,
    name: '名医引擎',
    img: '/platform/images/app-mall/app-3.jpg',
    description: '基于知识图谱的临床辅助决策系统。',
    money: '19.00',
  },
  {
    id: 4,
    name: '数见未来',
    img: '/platform/images/app-mall/app-4.jpg',
    description: '科研报告自动生成与AI辅助分析平台。',
    money: '19.00',
  },
  {
    id: 5,
    name: 'AI智能查房助手',
    img: '/platform/images/app-mall/app-5.jpg',
    description: '辅助医生高效整理病历、生成查房记录，提升查房效率。',
    money: '19.00',
  },
  {
    id: 6,
    name: '研数智库',
    img: '/platform/images/app-mall/app-2.jpg',
    description: '支持科研数据挖掘、趋势预测、统计分析。',
    money: '19.00',
  },
  {
    id: 7,
    name: '病历质控助手',
    img: '/platform/images/app-mall/app-3.jpg',
    description: '系统实时提示规范问题、提高病历合规率，降低医疗风险。',
    money: '19.00',
  },
  {
    id: 8,
    name: '医学术语识别训练',
    img: '/platform/images/app-mall/app-4.jpg',
    description: '趣味学习、结合语音识别和自动标注。',
    money: '19.00',
  },
  {
    id: 9,
    name: '智能影像诊断',
    img: '/platform/images/app-mall/app-1.jpg',
    description: '放射科医生上传CT/MRI，系统自动识别结节、分型，并提供诊断建议。',
    money: '19.00',
  },
];

const FeaturedApps: React.FC = () => {
  const navigate = useNavigate();

  const handleAppClick = (appId: number) => {
    navigate(`/platform/app-mall/${appId}`);
  };

  return (
    <div className='featured-apps-section'>
      <div className='featured-apps-container'>
        <div className='featured-apps-title'>
          <h2>精选应用</h2>
          <div className='featured-apps-title-content'>
            <p>汇聚行业优选成果，推动AI技术落地转化</p>
            <p className='featured-apps-title-content-link'>
              查看全部应用
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
            </p>
          </div>
        </div>
        <div className='featured-apps-main'>
          {apps.map(app => (
            <div 
              key={app.id}
              className='featured-apps-main-item'
              onClick={() => handleAppClick(app.id)}
              style={{ cursor: 'pointer' }}
            >
              <div className='featured-apps-main-item-img'>
                <img src={app.img} alt='应用' />
              </div>
              <div className='featured-apps-main-item-content'>
                <div className='featured-apps-main-item-content-title'>
                  <h3>{app.name}</h3>
                  <p>{app.description}</p>
                </div>
                <div className='featured-apps-main-item-content-money'>
                  <span>￥{app.money}</span>
                  <p>元/月起</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturedApps;
