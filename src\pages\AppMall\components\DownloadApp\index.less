.download-app-section {
  width: 100%;
  height: 270px;
  padding: 52px 0;
  background: url(/platform/images/featured-apps/download-app-banner.png) no-repeat center center;
  background-size: cover;
  .download-app-container {
    width: 1240px;
    margin: 0 auto;
    .download-app-title {
      h2 {
        margin: 0;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%; /* 48px */
      }
    }
    .download-app-desc {
      color: var(---65, rgba(0, 0, 0, 0.65));
      font-family: 'Alibaba PuHuiTi';
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 30px */
      margin: 0;
      margin-top: 12px;
      text-align: center;
    }
    .download-app-btn {
      display: flex;
      width: 240px;
      height: 52px;
      padding: 8px 20px;
      margin: 0 auto;
      margin-top: 20px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: linear-gradient(270deg, #d987ff 0%, #3852ff 34.56%, #38f 100%);
      overflow: hidden;
      color: var(---, #fff);
      text-overflow: ellipsis;

      /* t1 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 24px */
      &:hover {
        color: var(---, #fff);
        background: linear-gradient(270deg, #d987ff 0%, #3852ff 34.56%, #38f 100%);
      }
    }
  }
}
