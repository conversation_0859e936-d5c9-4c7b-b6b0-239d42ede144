.coupons {
  padding-top: 60px;
  width: 100%;
  background-color: #fff;

  .coupons-bg {
    width: 100%;

    height: 270px;
    background-image: url(/platform/images/computing-mall/coupons-bg.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-blend-mode: multiply;
    .coupons-content {
      width: 1240px;
      padding: 52px 0;
      margin: 0 auto;
      > p {
        color: var(---85, rgba(0, 0, 0, 0.85));
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
      .coupons-content-btn {
        margin-top: 32px;
        display: flex;
        width: 240px;
        height: 52px;
        padding: 8px 20px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        flex-shrink: 0;
        border-radius: 4px;
        background: linear-gradient(270deg, #d987ff 0%, #3852ff 34.56%, #38f 100%);
        overflow: hidden;
        color: var(---, #fff);
        text-overflow: ellipsis;

        /* t1 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 24px */
      }
    }
  }
}
