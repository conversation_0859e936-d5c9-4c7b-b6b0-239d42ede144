import React from 'react';
import { Card, Typography, Divider, Steps, Alert } from 'antd';
import './index.less';

const { Title, Paragraph, Text } = Typography;

const UserManual: React.FC = () => {
  const steps = [
    {
      title: '注册登录',
      description: '创建账户并完成身份验证',
    },
    {
      title: '选择功能',
      description: '根据需求选择相应的AI服务',
    },
    {
      title: '上传数据',
      description: '上传医疗数据或文献资料',
    },
    {
      title: '开始分析',
      description: '启动AI分析并获取结果',
    },
  ];

  return (
    <div className="user-manual-page">
      <div className="manual-container">
        <div className="manual-header">
          <Title level={1}>使用手册</Title>
          <Paragraph>
            本平台是专为医疗机构、科研团队及药企研发设计的AI驱动型科研分析系统，
            集成自然语言处理、医学影像识别与多组学分析能力，助力高效产出可信赖的医学研究成果。
          </Paragraph>
        </div>

        <div className="manual-content">
          <Card title="快速开始" className="manual-card">
            <Steps
              direction="vertical"
              current={-1}
              items={steps}
            />
          </Card>

          <Card title="主要功能" className="manual-card">
            <div className="feature-list">
              <div className="feature-item">
                <Title level={4}>智能文献检索与解析</Title>
                <Paragraph>
                  利用先进的自然语言处理技术，快速检索和解析医学文献，
                  提取关键信息，为研究提供有力支撑。
                </Paragraph>
              </div>
              
              <Divider />
              
              <div className="feature-item">
                <Title level={4}>医学实验辅助设计</Title>
                <Paragraph>
                  基于AI算法，协助设计科学合理的医学实验方案，
                  优化实验流程，提高研究效率。
                </Paragraph>
              </div>
              
              <Divider />
              
              <div className="feature-item">
                <Title level={4}>智能医疗数据分析</Title>
                <Paragraph>
                  运用机器学习技术，对医疗数据进行深度分析，
                  发现数据中的潜在规律和关联性。
                </Paragraph>
              </div>
            </div>
          </Card>

          <Card title="注意事项" className="manual-card">
            <Alert
              message="数据安全提醒"
              description="请确保上传的数据已经过脱敏处理，符合相关法律法规要求。平台采用严格的数据加密和访问控制机制，保障数据安全。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Alert
              message="使用建议"
              description="建议在使用前仔细阅读技术指南，了解各功能模块的具体操作方法和最佳实践。"
              type="info"
              showIcon
            />
          </Card>

          <Card title="常见问题" className="manual-card">
            <div className="faq-list">
              <div className="faq-item">
                <Title level={5}>Q: 如何上传大型数据文件？</Title>
                <Paragraph>
                  A: 平台支持多种格式的文件上传，对于大型文件建议使用分片上传功能，
                  确保上传过程的稳定性。
                </Paragraph>
              </div>
              
              <div className="faq-item">
                <Title level={5}>Q: 分析结果如何导出？</Title>
                <Paragraph>
                  A: 分析完成后，可以通过导出功能将结果保存为PDF、Excel等格式，
                  便于后续的研究和报告撰写。
                </Paragraph>
              </div>
              
              <div className="faq-item">
                <Title level={5}>Q: 如何获得技术支持？</Title>
                <Paragraph>
                  A: 如遇到技术问题，可通过平台内的帮助中心提交工单，
                  或联系客服获得及时的技术支持。
                </Paragraph>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UserManual;
