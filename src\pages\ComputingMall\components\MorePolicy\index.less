.more-policy {
  width: 100%;
  background: var(---, #fff);
  padding: 52px 0;

  .more-policy-container {
    width: 1240px;
    margin: 0 auto;
    .more-policy-title {
      > h2 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
    }

    .more-policy-content {
      margin-top: 32px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .more-policy-content-items {
        display: flex;
        gap: 20px;
        .more-policy-content-item {
          border-radius: 29px;
          border: 2px solid #fff;
          background: #e4eaf4;
          backdrop-filter: blur(50px);
          overflow: hidden;
          height: 280px;
          > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .more-policy-content-item-1 {
          width: 610px;
        }
        .more-policy-content-item-2,
        .more-policy-content-item-3 {
          width: 295px;
        }
        .more-policy-content-item-4 {
          width: 295px;
        }
        .more-policy-content-item-5,
        .more-policy-content-item-6 {
          width: 452.5px;
        }
      }
    }
  }
}
