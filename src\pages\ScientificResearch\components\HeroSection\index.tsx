import React from 'react';
import { Button } from 'antd';
import './index.less';

const HeroSection: React.FC = () => {
  // 处理在线体验按钮点击
  const handleOnlineExperience = () => {
    window.open('http://kyan.aethermind.cn/#/login', '_blank');
  };
  return (
    <section className='heroSectionScientificResearch'>
      <div className='heroSectionMain'>
        {/* <div className="heroSectionMask"></div> */}
        <div className='heroBackground'>
          <div className='heroOverlay'></div>
        </div>

        <div className='heroContent'>
          <div className='heroText'>
            <h1 className='heroTitle'>
              加速<span className='heightText'>科研</span>每一步，
              <span className='heightText'>赋能</span>创新每一刻
            </h1>
            <p className='heroSubtitle'>全程助力科研加速创新,赋能探索，加速发现</p>
          </div>

          <div className='heroCards'>
            <div className='heroCard medicalSearch'>
              <h3>医学文献检索</h3>
              <div className='cardImage'>
                <img
                  src='/platform/images/scientific-research/hero-medicine.png'
                  alt='医学文献检索'
                />
              </div>
            </div>

            <div className='heroCard documentReading'>
              <div className='dataAnalysisTitle'>
                <h3>智能文献阅读</h3>
                <p>文档问答、解析和翻译一体化</p>
              </div>
              <div className='uploadArea'>
                <div className='uploadBox'>
                  <img src='/platform/images/scientific-research/hero-upload.svg' alt='上传' />
                </div>
              </div>
            </div>

            <div className='heroCard dataAnalysis'>
              <div className='dataAnalysisTitle'>
                <h3>智能数据分析</h3>
                <p>科研数据多维问答式分析</p>
              </div>
              <div className='chartPreview'>
                <img src='/platform/images/scientific-research/hero-data.png' alt='数据分析图表' />
              </div>
            </div>
          </div>

          {/* <div className='heroActions'>
            <Button className='actionBtn'>立即购买</Button>
            <Button className='actionBtn' onClick={handleOnlineExperience}>
              在线体验
            </Button>
            <Button className='actionBtn'>企业咨询</Button>
          </div> */}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
