.computing-mall-hero-section {
  width: 100%;
  height: 524px;
  padding-top: 64px;
  background: rgb(234, 236, 242);
  background-size: cover;

  .computing-mall-content {
    position: relative;
    padding-top: 105px;
    width: 1240px;
    margin: 0 auto;
    .computing-mall-bg {
      background: linear-gradient(
        270deg,
        rgba(115, 115, 115, 0) 4.77%,
        rgba(126, 126, 126, 0.3) 11.61%,
        #a4a4a4 18.46%,
        rgba(170, 170, 170, 0.9) 43.58%,
        rgba(191, 191, 191, 0.48) 59.86%,
        rgba(217, 217, 217, 0) 81.52%
      );
      background: url('/platform/images/computing-mall/hero-bg.png') lightgray 50% / cover no-repeat;
      width: 1201px;
      height: 515px;
      flex-shrink: 0;
      aspect-ratio: 597/256;
      position: absolute;
      top: -53px;
      right: -56px;

      /* 使用mask实现左侧40%半透明虚化和右侧20%半透明虚化效果 */
      mask: linear-gradient(
        to right,
        rgba(234, 236, 242, 0) 0%,
        rgba(234, 236, 242, 0.6) 40%,
        rgba(234, 236, 242, 0.8) 40%,
        rgba(234, 236, 242, 0.8) 85%,
        rgba(234, 236, 242, 0.6) 85%,
        rgba(234, 236, 242, 0.6) 90%,
        rgba(234, 236, 242, 0) 100%
      );
      -webkit-mask: linear-gradient(
        to right,
        rgba(234, 236, 242, 0) 0%,
        rgba(234, 236, 242, 0.6) 40%,
        rgba(234, 236, 242, 0.8) 40%,
        rgba(234, 236, 242, 0.8) 85%,
        rgba(234, 236, 242, 0.6) 85%,
        rgba(234, 236, 242, 0.6) 90%,
        rgba(234, 236, 242, 0) 100%
      );
    }
    .computing-mall-title {
      position: relative;
      z-index: 2;
      width: 600px;
      > h1 {
        margin: 0;
        color: var(---85, rgba(0, 0, 0, 0.85));
        font-family: 'Alibaba PuHuiTi';
        font-size: 53.665px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 80.498px */
      }
      > p {
        margin: 0;
        color: var(---65, rgba(0, 0, 0, 0.65));
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 30px */
        margin-top: 10px;
      }
      .computing-mall-button {
        width: 117px;
        height: 48px;
        margin-top: 52px;
        border-radius: 4px;
        background: var(---Brand1-5, #37f);
        color: var(---, #fff);

        /* t1 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 24px */
      }
    }
  }
}
