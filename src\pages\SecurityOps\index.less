.security-ops {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;

  .page-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 104px 0;
    margin-bottom: 32px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .header-info {
        h1 {
          font-size: 36px;
          font-weight: 600;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 16px;

          .anticon {
            font-size: 40px;
          }
        }

        p {
          font-size: 18px;
          margin: 0;
          opacity: 0.9;
          line-height: 1.6;
        }
      }
    }

    @media (max-width: 768px) {
      padding: 40px 0;

      .header-info {
        h1 {
          font-size: 28px;
        }

        p {
          font-size: 16px;
        }
      }
    }
  }

  .security-stats {
    max-width: 1200px;
    margin: 0 auto 32px;
    padding: 0 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: none;
      background: white;
    }

    .ant-statistic-title {
      font-weight: 600;
      color: #595959;
    }

    .ant-statistic-content {
      font-weight: 700;
    }
  }

  .security-features {
    max-width: 1200px;
    margin: 0 auto 32px;
    padding: 0 24px;

    .feature-card {
      text-align: center;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: none;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .feature-icon {
        padding: 32px 0 16px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }

      .ant-card-meta-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #262626;
      }

      .ant-card-meta-description {
        color: #666;
        line-height: 1.6;
      }

      .ant-btn {
        width: 100%;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .security-dashboard-panel {
    max-width: 1200px;
    margin: 0 auto 32px;
    padding: 24px;

    .health-card,
    .trend-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: none;

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    .health-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .health-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-weight: 500;
        color: #595959;
      }

      .ant-progress {
        .ant-progress-text {
          font-weight: 600;
        }
      }
    }

    .trend-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .trend-info {
        display: flex;
        flex-direction: column;

        .trend-label {
          font-size: 14px;
          color: #8c8c8c;
          margin-bottom: 4px;
        }

        .trend-value {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
        }
      }

      .ant-tag {
        font-weight: 500;
      }
    }
  }

  .security-events {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px 32px;

    .ant-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: none;

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        border-bottom: 2px solid #f0f0f0;
        color: #595959;
      }

      .ant-table-tbody > tr {
        &:hover > td {
          background: #f5f5f5;
        }

        td {
          border-bottom: 1px solid #f0f0f0;
        }
      }

      .ant-tag {
        font-weight: 500;
        border: none;
        border-radius: 4px;

        .anticon {
          margin-right: 4px;
        }
      }

      .ant-btn-link {
        font-weight: 500;

        &.ant-btn-dangerous {
          color: #ff4d4f;

          &:hover {
            color: #ff7875;
          }
        }
      }
    }

    .ant-pagination {
      margin-top: 24px;
      text-align: center;

      .ant-pagination-item-active {
        border-color: #ff6b6b;

        a {
          color: #ff6b6b;
        }
      }

      .ant-pagination-item:hover {
        border-color: #ff6b6b;

        a {
          color: #ff6b6b;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .security-stats,
    .security-features,
    .security-dashboard,
    .security-events {
      padding: 0 16px 24px;
    }

    .security-features {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .security-dashboard {
      .ant-col {
        margin-bottom: 24px;
      }
    }

    .security-events {
      .ant-table {
        font-size: 12px;

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
        }
      }
    }
  }
}

// 自定义主题色彩
.security-ops {
  .ant-btn-primary {
    background: #ff6b6b;
    border-color: #ff6b6b;

    &:hover,
    &:focus {
      background: #ff5252;
      border-color: #ff5252;
    }
  }

  .ant-progress-bg {
    border-radius: 10px;
  }

  .ant-progress-inner {
    border-radius: 10px;
    background: #f5f5f5;
  }
}
