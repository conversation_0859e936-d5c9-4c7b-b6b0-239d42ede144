import React from 'react';
import './index.less';

const policyFiles = [
  {
    id: 1,
    date: '2025-09-02',
    title: '加大政策支持 推动平台经济健康发展',
    content:
      '发展平台经济事关扩内需、稳就业、惠民生，事关赋能实体经济、发展新质生产力。22日召开的国务院常务会议明确提出，要进一步加强对平台经济健康发展的统筹指导，加大政策支持力度。',
  },
  {
    id: 2,
    date: '2025-08-26',
    title:
      '工业和信息化部办公厅关于开展数字化赋能、科技成果赋智、质量标准品牌赋值中小企业全国行活动的通知',
    content:
      '各省、自治区、直辖市及计划单列市、新疆生产建设兵团工业和信息化、商务、卫生健康、医保、数据、中医药、药监主管部门：现将《医药工业数智化转型实施方案（2025—2030年）》印发给你们，请结合实际，抓好贯彻落实。',
  },
  {
    id: 3,
    date: '2025-07-09',
    title:
      '科技部等六部门关于印发《关于加快场景创新以人工智能高水平应用促进经济高质量发展的指导意见》的通知',
    content:
      '为落实《新一代人工智能发展规划》，系统指导各地方和各主体加快人工智能场景应用，推动经济高质量发展，现将《关于加快场景创新 以人工智能高水平应用促进经济高质量发展的指导意见》印发给你们，请结合实际，认真贯彻落实。',
  },
  {
    id: 4,
    date: '2025-06-02',
    title: '国家人工智能产业综合标准化体系建设指南 (2024 版)',
    content:
      '为深入贯彻落实党中央、国务院决策部署，加强人工智能标准化工作系统谋划，工业和信息化部、中央网络安全和信息化委员会办公室、国家发展和改革委员会、国家标准化管理委员会组织编制了《国家人工智能产业综合标准化体系建设指南（2024版）》。现印发给你们，请结合实际，抓好贯彻落实。',
  },
];

const PolicyFiles: React.FC = () => {
  return (
    <div className='policy-files'>
      <div className='policy-files-container'>
        <div className='policy-files-header'>
          <h1>政策文件</h1>
        </div>
        <div className='policy-files-content'>
          {policyFiles.map(item => (
            <div className='policy-files-item' key={item.id}>
              <div className='policy-files-item-date'>
                <h3>{item.date.substring(8)}</h3>
                <p>{item.date.substring(0, 7)}</p>
              </div>
              <div className='policy-files-item-info'>
                <h3>{item.title}</h3>
                <p>{item.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PolicyFiles;
