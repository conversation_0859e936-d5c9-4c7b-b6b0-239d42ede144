import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';
import './index.less';

const Forbidden: React.FC = () => {
  const navigate = useNavigate();

  const handleBackHome = () => {
    navigate('/');
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="error-page error-403">
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有访问此页面的权限"
        extra={
          <div className="error-actions">
            <Button type="primary" size="large" onClick={handleLogin}>
              去登录
            </Button>
            <Button size="large" onClick={handleBackHome} style={{ marginLeft: 16 }}>
              返回首页
            </Button>
            <Button size="large" onClick={handleGoBack} style={{ marginLeft: 16 }}>
              返回上页
            </Button>
          </div>
        }
      >
        <div className="error-description">
          <h3>可能的原因：</h3>
          <ul>
            <li>您尚未登录系统</li>
            <li>您的账户权限不足</li>
            <li>页面需要特殊权限访问</li>
          </ul>
          <div className="error-suggestions">
            <h4>建议：</h4>
            <p>请先登录您的账户，或联系管理员获取相应权限</p>
          </div>
        </div>
      </Result>
    </div>
  );
};

export default Forbidden;
