.notice-announcement {
  width: 100%;
  padding: 52px 0;
  background: var(---fill-7, #f5f7fa);
  .notice-announcement-container {
    width: 1240px;
    margin: 0 auto;
    .notice-announcement-header {
      width: 100%;
      > h1 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
      .notice-announcement-header-content {
        margin-top: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        > h3 {
          color: var(---85, rgba(0, 0, 0, 0.85));

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
          opacity: 0.8;
        }
        .notice-announcement-header-content-button {
          display: flex;
          align-items: center;
          > p {
            color: var(---Brand1-5, #37f);

            /* t2 */
            font-family: 'Aliba<PERSON> PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
            margin-right: 6px;
          }
          > img {
          }
          > img:nth-child(1) {
            opacity: 0.3;
          }
          > img:nth-child(2) {
            opacity: 0.6;
          }
        }
      }
    }

    .notice-announcement-content {
      margin-top: 32px;
      height: 600px;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 2fr 1fr;
      gap: 16px;

      // 左上大区域 (2x2)
      .grid-item-large {
        grid-column: 1 / 3;
        grid-row: 1 / 2;

        .main-announcement {
          height: 100%;
          display: flex;
          border-radius: 2px;
          overflow: hidden;

          > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      // 右上合并区域 (1x2)
      .grid-item-tall {
        grid-column: 3 / 4;
        grid-row: 1 / 2;
        display: flex;
        flex-direction: column;
        gap: 16px;

        .main-announcement-content {
          flex: 1;
          padding: 28px 32px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          border-radius: 2px;
          background: var(---, #fff);

          > h2 {
            overflow: hidden;
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-overflow: ellipsis;

            /* t1 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 24px */
          }

          > p {
            color: var(---45, rgba(0, 0, 0, 0.45));
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-weight: 400;
            line-height: 170%;
            flex: 1;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 10;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .main-announcement-button {
            display: flex;
            align-items: center;
            justify-content: space-between;

            > p {
              overflow: hidden;
              color: var(---25, rgba(0, 0, 0, 0.25));
              text-overflow: ellipsis;

              /* t3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
            }

            .btn {
              overflow: hidden;
              color: var(---65, rgba(0, 0, 0, 0.65));
              text-overflow: ellipsis;

              /* t3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
            }
          }
        }

        .announcement-item {
          flex: 1;
          border-radius: 2px;
          background: var(---, #fff);
          padding: 20px;
          display: flex;
          flex-direction: column;

          > h3 {
            color: var(---85, rgba(0, 0, 0, 0.85));
            font-family: 'Alibaba PuHuiTi';
            font-size: 14px;
            font-weight: 500;
            line-height: 150%;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          > p {
            color: var(---45, rgba(0, 0, 0, 0.45));
            font-family: 'Alibaba PuHuiTi';
            font-size: 12px;
            font-weight: 400;
            line-height: 170%;
            flex: 1;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .item-footer {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            > span {
              font-family: 'Alibaba PuHuiTi';
              font-size: 11px;
              font-weight: 400;

              &:first-child {
                color: var(---25, rgba(0, 0, 0, 0.25));
              }

              &.btn {
                color: var(---Brand1-5, #37f);
                cursor: pointer;

                &:hover {
                  opacity: 0.8;
                }
              }
            }
          }
        }
      }

      // 独立区域样式
      .grid-item-normal {
        border-radius: 2px;
        background: var(---, #fff);

        .announcement-item {
          display: flex;
          padding: 28px 32px;
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          flex: 1 0 0;
          align-self: stretch;
          grid-row: 3 / span 1;
          grid-column: 1 / span 1;
          border-radius: 2px;
          background: var(---, #fff);

          > h3 {
            overflow: hidden;
            color: var(---85, rgba(0, 0, 0, 0.85));
            text-overflow: ellipsis;

            /* t1 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 24px */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            align-self: stretch;
          }

          > p {
            flex: 1;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            overflow: hidden;
            color: var(---45, rgba(0, 0, 0, 0.45));
            text-overflow: ellipsis;

            /* t1 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 24px */
          }

          .item-footer {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            > span {
              overflow: hidden;
              color: var(---25, rgba(0, 0, 0, 0.25));
              text-overflow: ellipsis;

              /* t3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */

              &.btn {
                color: var(---65, rgba(0, 0, 0, 0.65));
                cursor: pointer;
              }
            }
          }
        }
      }

      // 最下面三个独立区域横跨整个宽度
      .grid-item-bottom-row {
        grid-column: 1 / 4;
        grid-row: 2 / 3;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;
      }
    }
  }
}
