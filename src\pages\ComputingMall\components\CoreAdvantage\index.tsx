import React from 'react';
import './index.less';

const CoreAdvantage: React.FC = () => {
  return (
    <div className='computing-mall-core-advantage'>
      <div className='computing-mall-core-advantage-container'>
        <div className='computing-mall-core-advantage-title'>
          <h2>核心优势</h2>
        </div>
        <div className='computing-mall-core-advantage-content'>
          <div className='advantage-content-item'>
            <div>
              <img src='/platform/images/computing-mall/core-advantage-1.png' alt='' />
            </div>
            <h4>一键优化配置</h4>
            <p>智能匹配任务场景，推荐最优算力方案，提升资源利用率。</p>
          </div>
          <div className='advantage-content-item'>
            <div>
              <img src='/platform/images/computing-mall/core-advantage-2.png' alt='' />
            </div>
            <h4>一键优化配置</h4>
            <p>智能匹配任务场景，推荐最优算力方案，提升资源利用率。</p>
          </div>
          <div className='advantage-content-item'>
            <div>
              <img src='/platform/images/computing-mall/core-advantage-3.png' alt='' />
            </div>
            <h4>一键优化配置</h4>
            <p>智能匹配任务场景，推荐最优算力方案，提升资源利用率。</p>
          </div>
          <div className='advantage-content-item'>
            <div>
              <img src='/platform/images/computing-mall/core-advantage-4.png' alt='' />
            </div>
            <h4>一键优化配置</h4>
            <p>智能匹配任务场景，推荐最优算力方案，提升资源利用率。</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoreAdvantage;
