import React from 'react';
import './index.less';

const keys = [
  {
    id: 1,
    title: '全部',
  },
  {
    id: 2,
    title: '金融',
  },
  {
    id: 3,
    title: '科技',
  },
  {
    id: 4,
    title: '医疗',
  },
  {
    id: 5,
    title: '数字化',
  },
  {
    id: 6,
    title: '人工智能',
  },
];

const datas = [
  {
    id: 1,
    title: '云数据储存的优势',
    username: '一只小小鸟',
    date: '2025-03-06',
    desc: '这是关于此主题内容的简短简短概括的一段话',
    img: '/platform/images/research-operations/ai-encyclopedia-1.png',
  },
  {
    id: 2,
    title: 'AI的应用领域',
    username: '一只小小鸟',
    date: '2025-03-06',
    desc: '这是关于此主题内容的简短简短概括的一段话',
    img: '/platform/images/research-operations/ai-encyclopedia-2.png',
  },
  {
    id: 3,
    title: '大数据分析',
    username: '一只小小鸟',
    date: '2025-03-06',
    desc: '这是关于此主题内容的简短简短概括的一段话',
    img: '/platform/images/research-operations/ai-encyclopedia-3.png',
  },
  {
    id: 4,
    title: '金融市场现状',
    username: '一只小小鸟',
    date: '2025-03-06',
    desc: '这是关于此主题内容的简短简短概括的一段话',
    img: '/platform/images/research-operations/ai-encyclopedia-4.png',
  },
];

const AIEncyclopedia: React.FC = () => {
  return (
    <div className='ai-encyclopedia-page'>
      <div className='ai-encyclopedia-container'>
        <div className='ai-encyclopedia-title'>
          <h2>AI百科知识</h2>
        </div>
        <div className='ai-encyclopedia-content'>
          <div className='ai-encyclopedia-keys'>
            <div className='keys-container'>
              {keys.map(item => (
                <div className={`key-item ${item.id === 1 ? 'active' : ''}`} key={item.id}>
                  <span>{item.title}</span>
                </div>
              ))}
            </div>
            <div className='find-more'>
              <span>查看更多</span>
              <img src='/platform/images/research-operations/find-more.svg' alt='find-more' />
            </div>
          </div>
          <div className='ai-encyclopedia-info'>
            {datas.map(item => (
              <div className='ai-encyclopedia-info-item'>
                <div className='ai-encyclopedia-info-item-img'>
                  <img src={item.img} alt={item.title} />
                </div>
                <div className='ai-encyclopedia-info-item-content'>
                  <div className='info-title'>
                    <h4>{item.title}</h4>
                    <p>
                      <span>{item.username}</span>
                      <span>{item.date}</span>
                    </p>
                  </div>
                  <p className='info-desc'>{item.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIEncyclopedia;
