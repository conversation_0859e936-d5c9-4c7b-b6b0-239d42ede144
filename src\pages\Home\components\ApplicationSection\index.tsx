import React from 'react';
import './index.less';

interface ApplicationItem {
  id: number;
  title: string;
  description: string;
  image: string;
  tag: string;
}

const applicationItems: ApplicationItem[] = [
  {
    id: 1,
    title: '智能影像',
    description: 'AI 算力辅助医学影像分析',
    image: '/images/home/<USER>',
    tag: '智能影像'
  },
  {
    id: 2,
    title: '疾病预测',
    description: '高性能算力助力风险预测',
    image: '/images/home/<USER>',
    tag: '疾病预测'
  },
  {
    id: 3,
    title: '药物研发',
    description: 'AI 模型加速药物筛选和临床试验',
    image: '/images/home/<USER>',
    tag: '药物研发'
  }
];

const ApplicationSection: React.FC = () => {
  return (
    <section className='application-section'>
      <div className='application-container'>
        <div className='application-header'>
          <h2 className='application-title'>广泛的行业应用场景</h2>
        </div>

        <div className='application-content'>
          <div className='application-main-card'>
            <div className='main-card-image'>
              <img src='/images/home/<USER>' alt='智能影像' />
              <div className='main-card-overlay'></div>
            </div>
            <div className='main-card-content'>
              <div className='main-card-tag'>
                <span>智能影像</span>
              </div>
              <h3 className='main-card-title'>AI 算力辅助医学影像分析</h3>
            </div>
          </div>

          <div className='application-side-cards'>
            <div className='side-card'>
              <div className='side-card-image'>
                <img src='/images/home/<USER>' alt='疾病预测' />
                <div className='side-card-overlay'></div>
              </div>
              <div className='side-card-content'>
                <div className='side-card-tag'>
                  <span>疾病预测</span>
                </div>
                <h3 className='side-card-title'>高性能算力助力风险预测</h3>
              </div>
            </div>

            <div className='side-card'>
              <div className='side-card-image'>
                <img src='/images/home/<USER>' alt='药物研发' />
                <div className='side-card-overlay'></div>
              </div>
              <div className='side-card-content'>
                <div className='side-card-tag'>
                  <span>药物研发</span>
                </div>
                <h3 className='side-card-title'>AI 模型加速药物筛选和临床试验</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ApplicationSection;
