import React, { type ReactNode, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperObj } from 'swiper';
import type { SwiperOptions } from 'swiper/types';
import './index.less';

export interface SlideData {
  id: string | number;
  content: ReactNode;
}

export interface ArrowConfig {
  leftIcon?: string;
  rightIcon?: string;
  leftPosition?: string;
  rightPosition?: string;
}

export interface SlideSizeConfig {
  normalWidth?: string;
  activeWidth?: string;
  normalHeight?: string;
  activeHeight?: string;
}

export interface MaskConfig {
  enabled?: boolean;
  gradient?: string;
  webkitGradient?: string;
}

export interface CustomSwiperProps {
  slides: SlideData[];
  showArrows?: boolean;
  arrowConfig?: ArrowConfig;
  className?: string;
  swiperOptions?: SwiperOptions;
  slideSizeConfig?: SlideSizeConfig;
  maskConfig?: MaskConfig;
  onSlideChange?: (activeIndex: number) => void;
  onSwiper?: (swiper: SwiperObj) => void;
  renderSlide?: (slide: SlideData, index: number) => ReactNode;
}

const CustomSwiper: React.FC<CustomSwiperProps> = ({
  slides,
  showArrows = true,
  arrowConfig = {
    leftIcon: '/platform/images/arrow-left.svg',
    rightIcon: '/platform/images/arrow-right.svg',
    leftPosition: '192px',
    rightPosition: '192px',
  },
  className = '',
  swiperOptions = {},
  slideSizeConfig = {
    normalWidth: '480px',
    activeWidth: '620px',
    normalHeight: '336px',
    activeHeight: '100%',
  },
  maskConfig = {
    enabled: false,
  },
  onSlideChange,
  onSwiper,
  renderSlide,
}) => {
  const swiperRef = useRef<SwiperObj>(null);

  // 默认 Swiper 配置
  const defaultSwiperOptions: SwiperOptions = {
    spaceBetween: 32,
    slidesPerView: 3,
    centeredSlides: true,
    initialSlide: Math.floor(slides.length / 2),
    loop: slides.length > 3,
    ...swiperOptions,
  };

  // Swiper 初始化时保存实例
  const handleSwiper = (swiper: SwiperObj) => {
    swiperRef.current = swiper;
    if (onSwiper) {
      onSwiper(swiper);
    }
  };

  // 切换到上一张
  const goPrev = () => {
    if (swiperRef.current) {
      swiperRef.current.slidePrev();
    }
  };

  // 切换到下一张
  const goNext = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
    }
  };

  // 处理slide变化事件
  const handleSlideChange = (swiper: SwiperObj) => {
    if (onSlideChange) {
      onSlideChange(swiper.activeIndex);
    }
  };

  // 渲染slide内容
  const renderSlideContent = (slide: SlideData, index: number) => {
    if (renderSlide) {
      return renderSlide(slide, index);
    }
    return slide.content;
  };

  // 构建动态样式
  const dynamicStyle: React.CSSProperties & Record<string, string> = {
    '--slide-normal-width': slideSizeConfig.normalWidth || '480px',
    '--slide-active-width': slideSizeConfig.activeWidth || '620px',
    '--slide-normal-height': slideSizeConfig.normalHeight || '336px',
    '--slide-active-height': slideSizeConfig.activeHeight || '100%',
    '--mask-gradient': `linear-gradient(
      to right,
      transparent 0%,
      transparent 10%,
      black 30%,
      black 70%,
      transparent 90%,
      transparent 100%
    )`,
    '--webkit-mask-gradient': `linear-gradient(
      to right,
      transparent 0%,
      transparent 10%,
      black 30%,
      black 70%,
      transparent 90%,
      transparent 100%
    )`,
  };

  // 如果启用了遮罩，添加遮罩样式
  if (maskConfig.enabled) {
    if (maskConfig.gradient) {
      dynamicStyle['--mask-gradient'] = maskConfig.gradient;
    }
    if (maskConfig.webkitGradient) {
      dynamicStyle['--webkit-mask-gradient'] = maskConfig.webkitGradient;
    }
  }

  return (
    <div className={`custom-swiper-container ${className}`} style={dynamicStyle}>
      <div className='custom-swiper-content'>
        <div className={`custom-swiper-wrapper ${maskConfig.enabled ? 'with-mask' : ''}`}>
          <Swiper
            {...defaultSwiperOptions}
            onSwiper={handleSwiper}
            onSlideChange={handleSlideChange}
          >
            {slides.map((slide, index) => (
              <SwiperSlide key={slide.id} className='custom-swiper-slide'>
                {renderSlideContent(slide, index)}
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {showArrows && slides.length > 1 && (
          <>
            <button
              className='custom-swiper-arrow custom-swiper-arrow-left'
              onClick={goPrev}
              style={{ left: arrowConfig.leftPosition }}
            >
              <img src={arrowConfig.leftIcon} alt='上一张' />
            </button>
            <button
              className='custom-swiper-arrow custom-swiper-arrow-right'
              onClick={goNext}
              style={{ right: arrowConfig.rightPosition }}
            >
              <img src={arrowConfig.rightIcon} alt='下一张' />
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default CustomSwiper;
