import React, { useState } from 'react';
import {
  Card,
  Statistic,
  Row,
  Col,
  Tabs,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Progress,
  Timeline,
  Alert,
  Badge,
  List,
  Space,
} from 'antd';
import {
  SecurityScanOutlined,
  EyeOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  SafetyCertificateOutlined,
  MonitorOutlined,
  AuditOutlined,
  KeyOutlined,
  UserOutlined,
  SettingOutlined,
  BugOutlined,
} from '@ant-design/icons';
import './SecurityCenterPage.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const SecurityCenterPage: React.FC = () => {
  const [isIncidentModalVisible, setIsIncidentModalVisible] = useState(false);
  const [isAccessModalVisible, setIsAccessModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 安全事件数据
  const securityIncidents = [
    {
      key: '1',
      id: 'SEC-2024-001',
      title: '异常登录尝试',
      level: '中等',
      status: '已处理',
      reporter: '系统监控',
      time: '2024-02-15 14:30',
      description: '检测到来自异常IP的多次登录尝试',
    },
    {
      key: '2',
      id: 'SEC-2024-002',
      title: '权限异常访问',
      level: '高',
      status: '处理中',
      reporter: '张管理员',
      time: '2024-02-14 09:15',
      description: '发现用户尝试访问超出权限范围的敏感数据',
    },
    {
      key: '3',
      id: 'SEC-2024-003',
      title: '文件异常下载',
      level: '低',
      status: '已关闭',
      reporter: '自动检测',
      time: '2024-02-13 16:45',
      description: '检测到大量文件批量下载行为',
    },
  ];

  // 访问控制数据
  const accessControls = [
    {
      key: '1',
      user: '张教授',
      role: '项目负责人',
      permissions: ['数据查看', '数据编辑', '用户管理'],
      lastLogin: '2024-02-15 10:30',
      status: '活跃',
    },
    {
      key: '2',
      user: '李研究员',
      role: '研究员',
      permissions: ['数据查看', '数据编辑'],
      lastLogin: '2024-02-14 15:20',
      status: '活跃',
    },
    {
      key: '3',
      user: '王博士',
      role: '观察员',
      permissions: ['数据查看'],
      lastLogin: '2024-02-10 09:15',
      status: '非活跃',
    },
  ];

  // 合规检查项
  const complianceChecks = [
    {
      category: '数据保护',
      items: [
        { name: 'GDPR合规性检查', status: 'passed', score: 95 },
        { name: '个人信息保护', status: 'passed', score: 98 },
        { name: '数据加密标准', status: 'warning', score: 85 },
        { name: '数据备份策略', status: 'passed', score: 92 },
      ],
    },
    {
      category: '访问控制',
      items: [
        { name: '身份认证机制', status: 'passed', score: 96 },
        { name: '权限管理体系', status: 'passed', score: 94 },
        { name: '多因素认证', status: 'warning', score: 78 },
        { name: '会话管理', status: 'passed', score: 89 },
      ],
    },
    {
      category: '安全监控',
      items: [
        { name: '入侵检测系统', status: 'passed', score: 91 },
        { name: '异常行为监控', status: 'passed', score: 93 },
        { name: '日志审计机制', status: 'failed', score: 65 },
        { name: '实时告警系统', status: 'passed', score: 88 },
      ],
    },
  ];

  // 安全培训数据
  const securityTraining = [
    {
      title: '数据安全基础培训',
      progress: 85,
      participants: 156,
      deadline: '2024-03-01',
      status: 'active',
    },
    {
      title: '网络安全意识教育',
      progress: 72,
      participants: 134,
      deadline: '2024-03-15',
      status: 'active',
    },
    {
      title: '应急响应流程培训',
      progress: 45,
      participants: 89,
      deadline: '2024-04-01',
      status: 'pending',
    },
  ];

  // 安全指标时间线
  const securityTimeline = [
    {
      time: '今天 14:30',
      event: '完成系统安全扫描，发现3个中等风险漏洞',
      type: 'scan',
      status: 'warning',
    },
    {
      time: '今天 10:15',
      event: '更新防火墙规则，新增5条安全策略',
      type: 'update',
      status: 'success',
    },
    {
      time: '昨天 16:45',
      event: '处理完成安全事件 SEC-2024-001',
      type: 'incident',
      status: 'success',
    },
    {
      time: '昨天 09:30',
      event: '系统安全证书续期成功',
      type: 'certificate',
      status: 'success',
    },
  ];

  const incidentColumns = [
    {
      title: '事件ID',
      dataIndex: 'id',
      key: 'id',
      width: 130,
      fixed: 'left' as const,
      render: (text: any) => <strong>{text}</strong>,
    },
    {
      title: '事件标题',
      dataIndex: 'title',
      key: 'title',
      width: 180,
      ellipsis: true,
    },
    {
      title: '风险等级',
      dataIndex: 'level',
      key: 'level',
      width: 90,
      render: (level: any) => {
        const colors = { 高: 'red', 中等: 'orange', 低: 'green' };
        return <Tag color={colors[level as keyof typeof colors]}>{level}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      render: (status: any) => {
        const colors = { 已处理: 'green', 处理中: 'blue', 已关闭: 'gray' };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: '报告人',
      dataIndex: 'reporter',
      key: 'reporter',
      width: 100,
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 140,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      render: (_: any) => (
        <Space size='small'>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            查看
          </Button>
          <Button type='link' size='small'>
            处理
          </Button>
        </Space>
      ),
    },
  ];

  const accessColumns = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      width: 120,
      fixed: 'left' as const,
      render: (text: any) => (
        <Space>
          <UserOutlined />
          <strong>{text}</strong>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 110,
      render: (text: any) => <Tag color='blue'>{text}</Tag>,
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 180,
      render: (permissions: any) => (
        <div style={{ maxWidth: 160 }}>
          {permissions.slice(0, 2).map((perm: any, index: any) => (
            <Tag key={index} style={{ marginBottom: 4, fontSize: 11 }}>
              {perm}
            </Tag>
          ))}
          {permissions.length > 2 && (
            <Tag style={{ fontSize: 11, color: '#666' }}>+{permissions.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: 140,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: any) => {
        return <Badge status={status === '活跃' ? 'success' : 'warning'} text={status} />;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      fixed: 'right' as const,
      render: (_: any) => (
        <Space size='small'>
          <Button type='link' size='small' icon={<SettingOutlined />}>
            设置
          </Button>
          <Button type='link' size='small' icon={<EyeOutlined />}>
            日志
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className='security-center-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>安全中心</h1>
            <p>全面保障数据安全，构建可信赖的科研环境</p>
            <div className='header-actions'>
              <Button
                type='primary'
                size='large'
                icon={<WarningOutlined />}
                onClick={() => setIsIncidentModalVisible(true)}
              >
                报告安全事件
              </Button>
              <Button
                size='large'
                icon={<KeyOutlined />}
                onClick={() => setIsAccessModalVisible(true)}
              >
                权限申请
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='安全事件'
                  value={12}
                  suffix='起'
                  prefix={<WarningOutlined style={{ color: '#fa8c16' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='安全评分'
                  value={94.5}
                  suffix='/100'
                  prefix={<SecurityScanOutlined style={{ color: '#52c41a' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='合规检查'
                  value={85.2}
                  suffix='%'
                  prefix={<SafetyCertificateOutlined style={{ color: '#1890ff' }} />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className='stat-card'>
                <Statistic
                  title='活跃用户'
                  value={156}
                  suffix='人'
                  prefix={<UserOutlined style={{ color: '#722ed1' }} />}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* 主要内容 */}
      <section className='content-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={16}>
              <Card>
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                  <TabPane
                    tab={
                      <span>
                        <SecurityScanOutlined />
                        安全概览
                      </span>
                    }
                    key='overview'
                  >
                    <Alert
                      message='系统安全状态良好'
                      description='最近24小时内检测到3起中等风险事件，均已妥善处理。建议定期更新安全策略。'
                      type='success'
                      showIcon
                      style={{ marginBottom: 24 }}
                    />

                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={12}>
                        <Card title='安全扫描结果' size='small'>
                          <div style={{ textAlign: 'center' }}>
                            <Progress
                              type='circle'
                              percent={94}
                              strokeColor='#52c41a'
                              format={percent => `${percent}分`}
                            />
                            <p style={{ marginTop: 16, color: '#52c41a' }}>安全状态良好</p>
                          </div>
                        </Card>
                      </Col>
                      <Col xs={24} md={12}>
                        <Card title='威胁等级分布' size='small'>
                          <div className='threat-levels'>
                            <div className='threat-item'>
                              <Tag color='red'>高危</Tag>
                              <span>0 个</span>
                            </div>
                            <div className='threat-item'>
                              <Tag color='orange'>中等</Tag>
                              <span>3 个</span>
                            </div>
                            <div className='threat-item'>
                              <Tag color='green'>低危</Tag>
                              <span>5 个</span>
                            </div>
                          </div>
                        </Card>
                      </Col>
                    </Row>

                    <Card title='安全培训进度' style={{ marginTop: 16 }}>
                      {securityTraining.map((training, index) => (
                        <div key={index} className='training-item'>
                          <div className='training-header'>
                            <span className='training-title'>{training.title}</span>
                            <Tag color={training.status === 'active' ? 'blue' : 'orange'}>
                              {training.status === 'active' ? '进行中' : '待开始'}
                            </Tag>
                          </div>
                          <Progress
                            percent={training.progress}
                            size='small'
                            format={percent => `${training.participants}人参与 (${percent}%)`}
                          />
                          <div className='training-deadline'>截止时间：{training.deadline}</div>
                        </div>
                      ))}
                    </Card>
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <WarningOutlined />
                        安全事件
                      </span>
                    }
                    key='incidents'
                  >
                    <div className='tab-header'>
                      <div className='search-filters'>
                        <Search placeholder='搜索事件ID或标题' style={{ width: 250 }} />
                        <Select placeholder='风险等级' style={{ width: 120 }}>
                          <Option value='high'>高</Option>
                          <Option value='medium'>中等</Option>
                          <Option value='low'>低</Option>
                        </Select>
                        <Select placeholder='状态' style={{ width: 120 }}>
                          <Option value='handled'>已处理</Option>
                          <Option value='handling'>处理中</Option>
                          <Option value='closed'>已关闭</Option>
                        </Select>
                      </div>
                      <Button
                        type='primary'
                        icon={<WarningOutlined />}
                        onClick={() => setIsIncidentModalVisible(true)}
                      >
                        报告事件
                      </Button>
                    </div>
                    <Table
                      columns={incidentColumns}
                      dataSource={securityIncidents}
                      pagination={{ pageSize: 10 }}
                      scroll={{ x: 'max-content' }}
                    />
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <KeyOutlined />
                        访问控制
                      </span>
                    }
                    key='access'
                  >
                    <div className='tab-header'>
                      <div className='search-filters'>
                        <Search placeholder='搜索用户名' style={{ width: 250 }} />
                        <Select placeholder='角色' style={{ width: 130 }}>
                          <Option value='admin'>管理员</Option>
                          <Option value='leader'>项目负责人</Option>
                          <Option value='researcher'>研究员</Option>
                          <Option value='observer'>观察员</Option>
                        </Select>
                        <Select placeholder='状态' style={{ width: 120 }}>
                          <Option value='active'>活跃</Option>
                          <Option value='inactive'>非活跃</Option>
                        </Select>
                      </div>
                      <Button
                        type='primary'
                        icon={<KeyOutlined />}
                        onClick={() => setIsAccessModalVisible(true)}
                      >
                        权限申请
                      </Button>
                    </div>
                    <Table
                      columns={accessColumns}
                      dataSource={accessControls}
                      pagination={{ pageSize: 10 }}
                      scroll={{ x: 'max-content' }}
                    />
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <SafetyCertificateOutlined />
                        合规检查
                      </span>
                    }
                    key='compliance'
                  >
                    {complianceChecks.map((category, index) => (
                      <Card key={index} title={category.category} style={{ marginBottom: 16 }}>
                        <Row gutter={[16, 16]}>
                          {category.items.map((item, itemIndex) => (
                            <Col xs={24} md={12} key={itemIndex}>
                              <div className='compliance-item'>
                                <div className='compliance-header'>
                                  <span className='compliance-name'>{item.name}</span>
                                  <Tag
                                    color={
                                      item.status === 'passed'
                                        ? 'green'
                                        : item.status === 'warning'
                                          ? 'orange'
                                          : 'red'
                                    }
                                  >
                                    {item.status === 'passed'
                                      ? '通过'
                                      : item.status === 'warning'
                                        ? '警告'
                                        : '不通过'}
                                  </Tag>
                                </div>
                                <Progress
                                  percent={item.score}
                                  size='small'
                                  strokeColor={
                                    item.score >= 90
                                      ? '#52c41a'
                                      : item.score >= 70
                                        ? '#faad14'
                                        : '#ff4d4f'
                                  }
                                />
                              </div>
                            </Col>
                          ))}
                        </Row>
                      </Card>
                    ))}
                  </TabPane>
                </Tabs>
              </Card>
            </Col>

            <Col xs={24} lg={8}>
              {/* 实时动态 */}
              <Card title='安全动态' className='timeline-card'>
                <Timeline>
                  {securityTimeline.map((item, index) => (
                    <Timeline.Item
                      key={index}
                      dot={
                        item.type === 'scan' ? (
                          <SecurityScanOutlined style={{ color: '#1890ff' }} />
                        ) : item.type === 'update' ? (
                          <SettingOutlined style={{ color: '#52c41a' }} />
                        ) : item.type === 'incident' ? (
                          <WarningOutlined style={{ color: '#fa8c16' }} />
                        ) : (
                          <SafetyCertificateOutlined style={{ color: '#722ed1' }} />
                        )
                      }
                      color={
                        item.status === 'success'
                          ? 'green'
                          : item.status === 'warning'
                            ? 'orange'
                            : 'red'
                      }
                    >
                      <div className='timeline-content'>
                        <p>{item.event}</p>
                        <span className='timeline-time'>{item.time}</span>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>

              {/* 安全策略 */}
              <Card title='安全策略' className='policy-card'>
                <List
                  size='small'
                  dataSource={[
                    { title: '数据加密传输', status: '已启用' },
                    { title: '双因素认证', status: '已启用' },
                    { title: '异常行为监控', status: '已启用' },
                    { title: '定期密码更新', status: '已启用' },
                    { title: '文件访问审计', status: '已启用' },
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        title={item.title}
                        description={
                          <Tag color='green' icon={<CheckCircleOutlined />}>
                            {item.status}
                          </Tag>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>

              {/* 安全工具 */}
              <Card title='安全工具' className='tools-card'>
                <div className='security-tools'>
                  <Button
                    type='primary'
                    icon={<SecurityScanOutlined />}
                    block
                    style={{ marginBottom: 8 }}
                  >
                    系统扫描
                  </Button>
                  <Button icon={<MonitorOutlined />} block style={{ marginBottom: 8 }}>
                    实时监控
                  </Button>
                  <Button icon={<AuditOutlined />} block style={{ marginBottom: 8 }}>
                    日志审计
                  </Button>
                  <Button icon={<BugOutlined />} block>
                    漏洞检测
                  </Button>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* 安全事件报告模态框 */}
      <Modal
        title='报告安全事件'
        visible={isIncidentModalVisible}
        onCancel={() => setIsIncidentModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='事件标题' name='title' rules={[{ required: true }]}>
            <Input placeholder='请输入事件标题' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='风险等级' name='level' rules={[{ required: true }]}>
                <Select placeholder='选择风险等级'>
                  <Option value='high'>高</Option>
                  <Option value='medium'>中等</Option>
                  <Option value='low'>低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='事件类型' name='type' rules={[{ required: true }]}>
                <Select placeholder='选择事件类型'>
                  <Option value='intrusion'>入侵尝试</Option>
                  <Option value='data'>数据泄露</Option>
                  <Option value='malware'>恶意软件</Option>
                  <Option value='access'>异常访问</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='事件描述' name='description' rules={[{ required: true }]}>
            <TextArea rows={4} placeholder='请详细描述安全事件的具体情况' />
          </Form.Item>
          <Form.Item label='影响范围' name='impact'>
            <TextArea rows={2} placeholder='描述事件的影响范围和程度' />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                提交报告
              </Button>
              <Button onClick={() => setIsIncidentModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限申请模态框 */}
      <Modal
        title='权限申请'
        visible={isAccessModalVisible}
        onCancel={() => setIsAccessModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout='vertical'>
          <Form.Item label='申请用户' name='user' rules={[{ required: true }]}>
            <Input placeholder='请输入用户名或邮箱' />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='申请角色' name='role' rules={[{ required: true }]}>
                <Select placeholder='选择角色'>
                  <Option value='admin'>管理员</Option>
                  <Option value='leader'>项目负责人</Option>
                  <Option value='researcher'>研究员</Option>
                  <Option value='observer'>观察员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='有效期' name='duration' rules={[{ required: true }]}>
                <Select placeholder='选择有效期'>
                  <Option value='30'>30天</Option>
                  <Option value='90'>90天</Option>
                  <Option value='180'>180天</Option>
                  <Option value='365'>1年</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label='权限范围' name='permissions' rules={[{ required: true }]}>
            <Select mode='multiple' placeholder='选择权限范围'>
              <Option value='data-view'>数据查看</Option>
              <Option value='data-edit'>数据编辑</Option>
              <Option value='data-delete'>数据删除</Option>
              <Option value='user-manage'>用户管理</Option>
              <Option value='system-config'>系统配置</Option>
            </Select>
          </Form.Item>
          <Form.Item label='申请理由' name='reason' rules={[{ required: true }]}>
            <TextArea rows={3} placeholder='请说明申请权限的理由' />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                提交申请
              </Button>
              <Button onClick={() => setIsAccessModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SecurityCenterPage;
