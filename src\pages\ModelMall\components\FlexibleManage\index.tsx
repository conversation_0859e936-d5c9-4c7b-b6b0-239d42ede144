import React, { useState } from 'react';

import './index.less';

const FlexibleManage: React.FC = () => {
  const [expandedCard, setExpandedCard] = useState<number | null>(1);

  const newsItems = [
    {
      id: 1,
      subtitle: '人工智能大会现场报道',
      content: '预置Deepseek、 Qwen3等五十余种通用大模型， Medgemma等二十余种医疗大模型',
      category: 'conference',
      bgImage: '/platform/images/model-mall/flexible-manage-banner.png',
      icon: '/platform/images/model-mall/ai-service-flexible-manage-1.svg',
      featured: true,
    },
    {
      id: 2,
      subtitle: '医学影像大模型',
      content:
        '基于深度学习的医疗影像分析系统取得重大突破，诊断准确率达到95%以上，为医疗行业带来革命性变化。',
      category: 'news',
      bgImage: '/platform/images/model-mall/flexible-manage-banner.png',
      icon: '/platform/images/model-mall/ai-service-flexible-manage-2.svg',
      featured: false,
    },
    {
      id: 3,
      subtitle: '标签化 分类',
      content:
        '全面升级算力基础设施，部署最新GPU集群，提供更强大的AI训练和推理能力，为用户提供更高效的服务保障。',
      category: 'news',
      bgImage: '/platform/images/model-mall/flexible-manage-banner.png',
      icon: '/platform/images/model-mall/ai-service-flexible-manage-3.svg',
      featured: false,
    },
    {
      id: 4,
      subtitle: '合作机构管理',
      content:
        '全面升级算力基础设施，部署最新GPU集群，提供更强大的AI训练和推理能力，为用户提供更高效的服务保障。',
      category: 'news',
      bgImage: '/platform/images/model-mall/flexible-manage-banner.png',
      icon: '/platform/images/model-mall/ai-service-flexible-manage-4.svg',
      featured: false,
    },
  ];

  const handleCardClick = (cardId: number) => {
    setExpandedCard(cardId);
    // setExpandedCard(expandedCard === cardId ? null : cardId);
  };

  return (
    <section className='flexible-manage-section'>
      <div className='news-container'>
        <h2 className='section-title'>高效协作，灵活管理</h2>
        <div className='news-grid'>
          {newsItems.map(item => (
            <div
              key={item.id}
              className={`news-item  ${expandedCard === item.id ? 'featured' : ''}`}
              onClick={() => handleCardClick(item.id)}
            >
              <div className='news-item-bg' style={{ backgroundImage: `url(${item.bgImage})` }}>
                <div
                  className='news-background'
                  style={{ backgroundImage: `url(${item.bgImage})` }}
                >
                  <div className='news-overlay'>
                    {expandedCard === item.id && (
                      <>
                        <img className='position-icon' src={item.icon} alt='' />
                        <div className='news-expanded-content'>
                          <h3 className='news-subtitle'>{item.subtitle}</h3>
                          <p className='news-content'>{item.content}</p>
                          <div className='news-actions'>了解详情</div>
                        </div>
                      </>
                    )}
                    {expandedCard !== item.id && (
                      <div className='news-coll-content'>
                        <div>
                          <img src={item.icon} alt='' />
                          <p>{item.subtitle}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FlexibleManage;
