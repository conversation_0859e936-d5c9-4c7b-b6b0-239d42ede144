import React from 'react';
import { Button } from 'antd';
import './index.less';
import { useNavigate } from 'react-router-dom';

const HeroSection: React.FC = () => {
  const navigate = useNavigate();
  return (
    <section className='hero-section'>
      <div className='hero-container'>
        <div className='hero-content'>
          <div className='hero-text'>
            <div className='text-content'>
              <h1 className='main-title'>一体化AI赋能平台</h1>
              <p className='subtitle'>
                打通算力、模型、数据与科研应用的全链路，支撑智能化场景快速落地
              </p>
            </div>
            <Button
              type='primary'
              size='large'
              className='cta-button'
              onClick={() => window.open('/platform/management/dashboard', '_blank')}
            >
              立即体验
            </Button>
          </div>
          <div className='hero-visual'>
            <img
              src='/platform/images/home/<USER>'
              alt='AI赋能平台视觉背景'
              className='hero-bg-complete'
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
