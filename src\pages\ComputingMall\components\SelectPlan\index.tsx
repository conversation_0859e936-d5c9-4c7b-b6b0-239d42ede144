import React from 'react';
import './index.less';
import { Tabs, type TabsProps } from 'antd';

const Month = () => {
  return (
    <div className='select-plan-month'>
      {[1, 2, 3, 4, 5, 6].map(item => (
        <div key={item} className='select-plan-month-item'>
          <div className='select-plan-month-item-title'>
            <h4>GeForce RTX 4090 24G</h4>
            <img src='/platform/images/computing-mall/select-plan-select-expand.svg' alt='' />
          </div>
          <div className='select-plan-month-item-price'>
            <span>1.58</span>
            <p>元/时</p>
          </div>
          <div className='select-plan-month-item-info'>
            <p>
              <span>24GB</span>显存
            </p>
            <p>
              <span>16核</span>中央处理器
            </p>
            <p>
              <span>60GB</span>内存
            </p>
            <p>
              <span>80GB</span>硬盘
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

const tabs = [
  { label: '包月', key: '1', children: <Month /> },
  { label: '包年', key: '2', children: <Month /> },
  { label: '竞价', key: '3', children: <Month /> },
];

const renderTabBar: TabsProps['renderTabBar'] = (props, DefaultTabBar) => (
  <DefaultTabBar {...props} className='select-plan-tabs-bar' />
);

const SelectPlan: React.FC = () => {
  return (
    <div className='select-plan'>
      <div className='select-plan-container'>
        <div className='select-plan-title'>
          <h2>选择适合您的算力方案</h2>
          <div className='select-plan-title-desc'>
            <p>多种算力租用方案，满足不同算力需求、不同预算的用户</p>
            <p className='find-all'>
              查看全部
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
              <img src='/platform/images/featured-apps/feature-app-right.svg' alt='查看全部应用' />
            </p>
          </div>
        </div>
        <div className='select-plan-tabs'>
          <Tabs
            defaultActiveKey='1'
            centered
            popupClassName='select-plan-tabs-popup'
            renderTabBar={renderTabBar}
            tabBarGutter={40}
            indicator={{ size: origin => origin + 20, align: 'center' }}
            items={tabs}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectPlan;
