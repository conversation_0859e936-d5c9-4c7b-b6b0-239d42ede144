.trainingCourse {
  --active-color: #3377ff;
  --inactive-color: rgba(0, 0, 0, 0.45);

  padding: 52px 340px;
  background: #eef5ff;

  .sectionTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    margin-bottom: 32px;
  }

  .newsContent {
    position: relative;
    z-index: 2;
    width: 1288px;
    margin: 0 auto;
    padding: 52px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .newsGrid {
    display: flex;
    gap: 180px;
    border-radius: 12px;
    backdrop-filter: blur(4px);
  }

  //   左半部分
  .assistantBg {
    // border-radius: 12px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    img {
      width: 55%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
    }
  }

  .trainingNewClass {
    border-radius: 12px 0 0 0;
    background-color: #EBB923;
    color: #fff;
    height: 45px;
    width: 107px;
    line-height: 45px;
    font-display: center;
    text-align: center;
  }

  .assistantInfo {
    width: 105%;
    position: relative;
    z-index: 2;

    .course-title {
      background-color: #ECF5FB;
      border-radius: 12px;
      margin: 78px 107px 6px 24px;
      padding: 44px 0px 0px 25px;
      opacity: 0.8;
      height: 181px;
      width: 482px;
      border: 1px solid #fff;

      .course-title-p {
        font-size: 16px;
        margin: 10px 0 0;
        color: #365B72;
        font-weight: bold;

      }

    }

    .course-title-h1 {
      font-size: 36px;
      margin-bottom: 10px;
      color: #093450;
      margin: 0;
      font-weight: bold;
    }

    .bottomBar {
      background-color: #666;
      display: flex;
      justify-content: space-between;
      margin-top: 173px;
      border-radius: 0 0 12px 12px;
      height: 51px;
      width: 708px;
      color: #fff;
      padding: 13px;
      opacity: 0.8;
      font-size: 17px;
    }
  }
}

.assistantName {
  font-family: 'Alibaba PuHuiTi';
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
  line-height: 150%;
  /* 60px */
  background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;

}

.assistantDesc {
  font-family: 'Alibaba PuHuiTi', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
}

.featuredNews {
  width: 550px;
  height: 528px;
}

.newsImage {
  flex: 1;
  display: flex;
  // align-items: flex-end;
  margin-bottom: 10px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.popular-courses {
  padding: 20px;
  background-image: linear-gradient(to bottom right, #ceebff 10%, white 50%);
  border-radius: 12px;
  width: 480px;
  height: 528px;
}

.hot-courses {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .hot-courses-title{
    display: inline-block;
    align-items: center;
  }
  img{
    vertical-align: middle;
    margin-right: 10px;
  }
  h2 {
    margin: 0;
    display: inline-block; /* Ensures the heading is on the same line */
    vertical-align: middle; /* Aligns the text vertically in the middle */
}
}

.courseDetails {
  height: 428px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.course-details {
  .courses-first-title{
    display: flex;
   align-items: start;
  }
  h3 {
    font-size: 16px;
    margin:0 5px 23px;
  }

  p {
    font-size: 14px;
    color: #666;
  }
}

.course-item {
  display: flex;
  align-items: center; // 垂直居中对齐
  gap: 20px; // 图片和文字之间的间距

  .course-icon {
    width: 199px;
    height: 123px;
  }

}