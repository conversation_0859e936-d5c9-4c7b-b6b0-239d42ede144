import React from 'react';
import './index.less';

// 箭头图标组件
const ArrowIcon: React.FC = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18L15 12L9 6" stroke="rgba(0, 0, 0, 0.25)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const NewsSection: React.FC = () => {
  const newsItems = [
    {
      id: 1,
      title: '百度何明科：大模型为医疗健康行业带来了哪些新可能？',
      date: '2025-05-11',
      featured: true,
    },
    {
      id: 2,
      title: '人工智能在医疗诊断领域取得重大新突破',
      date: '2025-05-11',
      featured: false,
    },
    {
      id: 3,
      title: '"AI+医疗"已成为热门应用领域 "AI医生"如何辅助医疗？',
      date: '2025-05-11',
      featured: false,
    },
    {
      id: 4,
      title: 'AI医疗战火升级！Agent爆发，大三甲争相引入，医疗系统新一轮变革！',
      content: '2025年，DeepSeek迅速席卷全国医疗行业。超过300家医院已成功部署DeepSeek，覆盖了患者服务、科研、诊疗、办公、管理等各个方面。',
      date: '2025-05-11',
      featured: true,
    },
  ];

  return (
    <section className='news-section'>
      <div className='news-container'>
        <div className='news-header'>
          <h2 className='section-title'>最新讯息</h2>
        </div>

        <div className='news-content'>
          <div className='news-main-image'>
            <img
              src='/images/home/<USER>'
              alt='新闻主图'
              className='main-news-bg'
            />
          </div>

          <div className='news-list'>
            {newsItems.map((item, index) => (
              <div key={item.id} className={`news-item ${index === 3 ? 'featured' : ''}`}>
                <div className='news-item-content'>
                  <h3 className='news-title'>{item.title}</h3>
                  {item.content && (
                    <p className='news-description'>{item.content}</p>
                  )}
                </div>
                <div className='news-item-footer'>
                  <span className='news-date'>{item.date}</span>
                  <ArrowIcon />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
