import React from 'react';
import { Card, Row, Col, Button, Tag, Divider } from 'antd';
import {
  CheckOutlined,
  StarOutlined,
  CloudOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import './index.less';

const ProductPage: React.FC = () => {
  const products = [
    {
      id: 'computing',
      title: '算力商城',
      description: '提供高性能计算资源，支持AI模型训练和推理',
      icon: <CloudOutlined style={{ fontSize: '32px', color: '#1890ff' }} />,
      features: ['GPU集群', '弹性扩容', '按需计费', '高性能网络'],
      price: '按小时计费',
      status: '正式运营',
      link: '/computing-mall',
    },
    {
      id: 'model',
      title: '模型商城',
      description: '汇聚优质AI模型，支持一键部署和调用',
      icon: <DatabaseOutlined style={{ fontSize: '32px', color: '#52c41a' }} />,
      features: ['预训练模型', '自定义微调', 'API调用', '模型版本管理'],
      price: '按调用次数',
      status: '正式运营',
      link: '/model-mall',
    },
    {
      id: 'app',
      title: '应用商城',
      description: '丰富的AI应用生态，满足各行业需求',
      icon: <AppstoreOutlined style={{ fontSize: '32px', color: '#722ed1' }} />,
      features: ['行业应用', '开箱即用', '定制开发', '技术支持'],
      price: '按应用计费',
      status: '正式运营',
      link: '/app-mall',
    },
    {
      id: 'research',
      title: '科研赋能平台',
      description: '为科研机构提供AI技术支持和资源整合',
      icon: <StarOutlined style={{ fontSize: '32px', color: '#fa8c16' }} />,
      features: ['科研项目管理', '数据共享', '协作平台', '成果转化'],
      price: '定制报价',
      status: '试运营',
      link: '/scientific-research',
    },
  ];

  const handleExplore = (link: string) => {
    window.location.href = link;
  };

  return (
    <div className='product-page'>
      {/* Hero Section */}
      <section className='hero-section'>
        <div className='hero-content'>
          <h1>AI赋能产品矩阵</h1>
          <p>构建完整的人工智能生态体系，为企业和科研机构提供全方位AI服务</p>
        </div>
      </section>

      {/* Products Grid */}
      <section className='products-section'>
        <div className='container'>
          <div className='section-header'>
            <h2>核心产品</h2>
            <p>从算力资源到模型应用，从科研支持到产业落地</p>
          </div>

          <Row gutter={[24, 24]}>
            {products.map(product => (
              <Col xs={24} sm={12} lg={6} key={product.id}>
                <Card
                  className='product-card'
                  hoverable
                  cover={<div className='product-icon'>{product.icon}</div>}
                  actions={[
                    <Button
                      type='primary'
                      onClick={() => handleExplore(product.link)}
                      style={{ width: '80%' }}
                    >
                      了解详情
                    </Button>,
                  ]}
                >
                  <div className='product-header'>
                    <h3>{product.title}</h3>
                    <Tag color={product.status === '正式运营' ? 'green' : 'orange'}>
                      {product.status}
                    </Tag>
                  </div>

                  <p className='product-description'>{product.description}</p>

                  <div className='product-features'>
                    {product.features.map((feature, index) => (
                      <div key={index} className='feature-item'>
                        <CheckOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Divider style={{ margin: '16px 0' }} />

                  <div className='product-pricing'>
                    <strong>计费方式: {product.price}</strong>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* Features Section */}
      <section className='features-section'>
        <div className='container'>
          <div className='section-header'>
            <h2>平台优势</h2>
            <p>专业、可靠、创新的AI服务体验</p>
          </div>

          <Row gutter={[32, 32]}>
            <Col xs={24} sm={8}>
              <div className='feature-item'>
                <div className='feature-icon'>
                  <CloudOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                </div>
                <h3>高性能算力</h3>
                <p>提供GPU集群、高性能计算资源，支持大规模AI模型训练和推理任务</p>
              </div>
            </Col>

            <Col xs={24} sm={8}>
              <div className='feature-item'>
                <div className='feature-icon'>
                  <DatabaseOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                </div>
                <h3>丰富模型库</h3>
                <p>汇聚国内外优质AI模型，覆盖NLP、CV、语音等多个领域</p>
              </div>
            </Col>

            <Col xs={24} sm={8}>
              <div className='feature-item'>
                <div className='feature-icon'>
                  <AppstoreOutlined style={{ fontSize: '48px', color: '#722ed1' }} />
                </div>
                <h3>应用生态</h3>
                <p>构建完整的AI应用生态，支持各行业智能化升级转型</p>
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className='cta-section'>
        <div className='container'>
          <div className='cta-content'>
            <h2>开启AI赋能之旅</h2>
            <p>立即体验我们的产品服务，加速您的AI项目落地</p>
            <Button
              type='primary'
              size='large'
              onClick={() => (window.location.href = '/about')}
              style={{ marginTop: '24px' }}
            >
              联系我们
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductPage;
