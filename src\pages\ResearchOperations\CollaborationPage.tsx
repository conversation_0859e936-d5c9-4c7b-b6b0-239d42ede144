import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Table,
  Tag,
  Progress,
  Avatar,
  Timeline,
  Input,
  Form,
  Select,
  Modal,
  message,
} from 'antd';
import {
  TeamOutlined,
  ProjectOutlined,
  TrophyOutlined,
  PlusOutlined,
  SearchOutlined,
  UserOutlined,
  StarOutlined,
} from '@ant-design/icons';
import './CollaborationPage.less';

const { Search } = Input;
const { Option } = Select;

const CollaborationPage: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 合作统计数据
  const collaborationStats = [
    { title: '合作机构', value: 1268, prefix: <TeamOutlined />, color: '#1890ff' },
    { title: '合作项目', value: 3456, prefix: <ProjectOutlined />, color: '#52c41a' },
    { title: '成功案例', value: 2187, prefix: <TrophyOutlined />, color: '#fa8c16' },
    { title: '签约金额', value: '15.8', suffix: '亿元', color: '#f5222d' },
  ];

  // 合作机构数据
  const partners = [
    {
      key: '1',
      name: '郑州大学',
      type: '211高校',
      location: '郑州市',
      projects: 156,
      status: 'active',
      cooperation_date: '2020-03-15',
      rating: 5,
      logo: '/platform/images/research-operations/partner-zzu.png',
    },
    {
      key: '2',
      name: '宇通客车股份有限公司',
      type: '科技企业',
      location: '郑州市',
      projects: 89,
      status: 'active',
      cooperation_date: '2021-07-22',
      rating: 5,
      logo: '/platform/images/research-operations/partner-yutong.png',
    },
    {
      key: '3',
      name: '河南省科学院',
      type: '科研院所',
      location: '郑州市',
      projects: 234,
      status: 'active',
      cooperation_date: '2019-11-08',
      rating: 5,
      logo: '/platform/images/research-operations/partner-hnkxy.png',
    },
    {
      key: '4',
      name: '河南大学',
      type: '省属重点',
      location: '开封市',
      projects: 198,
      status: 'active',
      cooperation_date: '2020-05-12',
      rating: 4,
      logo: '/platform/images/research-operations/partner-henu.png',
    },
    {
      key: '5',
      name: '双汇集团',
      type: '食品企业',
      location: '漯河市',
      projects: 67,
      status: 'negotiating',
      cooperation_date: '2023-01-20',
      rating: 4,
      logo: '/platform/images/research-operations/partner-shuanghui.png',
    },
  ];

  // 正在进行的项目
  const ongoingProjects = [
    {
      title: '智能制造关键技术研究',
      partners: ['郑州大学', '宇通客车'],
      progress: 75,
      budget: '2800万元',
      startDate: '2023-01-15',
      endDate: '2025-12-31',
      leader: '张教授',
      status: 'in_progress',
    },
    {
      title: '新能源材料产业化',
      partners: ['河南省科学院', '河南理工大学'],
      progress: 60,
      budget: '3200万元',
      startDate: '2023-03-01',
      endDate: '2026-02-28',
      leader: '李研究员',
      status: 'in_progress',
    },
    {
      title: '人工智能医疗诊断系统',
      partners: ['河南大学', '双汇集团'],
      progress: 45,
      budget: '1500万元',
      startDate: '2023-06-01',
      endDate: '2025-05-31',
      leader: '王博士',
      status: 'in_progress',
    },
  ];

  // 成功案例
  const successCases = [
    {
      title: '智能交通核心技术突破',
      description: '与宇通客车合作开发的智能交通关键技术已实现产业化，创造经济价值超过50亿元',
      partners: ['郑州大学', '宇通客车'],
      achievement: '获得国家科技进步一等奖',
      date: '2022-12-15',
    },
    {
      title: '食品安全检测平台建设',
      description: '产学研合作建立的食品安全检测平台，已成功开发3套检测系统并获得权威认证',
      partners: ['河南大学', '双汇集团'],
      achievement: '3套检测系统获得认证',
      date: '2022-09-20',
    },
    {
      title: '智慧农业解决方案',
      description: '多方合作开发的智慧农业整体解决方案已在河南20个县区成功部署',
      partners: ['河南省科学院', '华北水利水电大学'],
      achievement: '在20个县区部署应用',
      date: '2022-06-30',
    },
  ];

  const columns = [
    {
      title: '机构名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar src={record.logo} icon={<UserOutlined />} style={{ marginRight: 8 }} />
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: '机构类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeColorMap: { [key: string]: string } = {
          '211高校': 'blue',
          省属重点: 'cyan',
          科技企业: 'green',
          食品企业: 'orange',
          科研院所: 'purple',
        };
        const color = typeColorMap[type] || 'default';
        return <Tag color={color}>{type}</Tag>;
      },
    },
    {
      title: '所在地区',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '合作项目',
      dataIndex: 'projects',
      key: 'projects',
      render: (count: number) => <span>{count} 个</span>,
    },
    {
      title: '合作状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          active: { color: 'success', text: '合作中' },
          negotiating: { color: 'warning', text: '洽谈中' },
          paused: { color: 'default', text: '暂停' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap];
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '合作评级',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating: number) => (
        <div>
          {[...Array(5)].map((_, i) => (
            <StarOutlined key={i} style={{ color: i < rating ? '#faad14' : '#d9d9d9' }} />
          ))}
        </div>
      ),
    },
    {
      title: '合作时间',
      dataIndex: 'cooperation_date',
      key: 'cooperation_date',
    },
  ];

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('合作申请:', values);
      message.success('合作申请已提交，我们将尽快与您联系！');
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <div className='collaboration-page'>
      {/* 页面头部 */}
      <section className='page-header'>
        <div className='container'>
          <div className='header-content'>
            <h1>多组织协作平台</h1>
            <p>构建产学研深度融合的协作生态，推动科技成果转化与产业创新发展</p>
            <div className='header-actions'>
              <Button type='primary' size='large' icon={<PlusOutlined />} onClick={showModal}>
                申请合作
              </Button>
              <Button size='large' icon={<SearchOutlined />}>
                寻找合作伙伴
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className='stats-section'>
        <div className='container'>
          <Row gutter={[24, 24]}>
            {collaborationStats.map((stat, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className='stat-card'>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    prefix={stat.prefix}
                    suffix={stat.suffix}
                    valueStyle={{ color: stat.color }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 合作机构 */}
      <section className='partners-section'>
        <div className='container'>
          <div className='section-header'>
            <h2>合作机构</h2>
            <div className='section-controls'>
              <Search
                placeholder='搜索合作机构'
                style={{ width: 300 }}
                onSearch={value => console.log('搜索:', value)}
              />
              <Select defaultValue='all' style={{ width: 120 }}>
                <Option value='all'>全部类型</Option>
                <Option value='university'>高等院校</Option>
                <Option value='enterprise'>科技企业</Option>
                <Option value='institute'>科研院所</Option>
              </Select>
            </div>
          </div>
          <Card>
            <Table
              dataSource={partners}
              columns={columns}
              pagination={{ pageSize: 5 }}
              scroll={{ x: 800 }}
            />
          </Card>
        </div>
      </section>

      {/* 正在进行的项目 */}
      <section className='ongoing-projects-section'>
        <div className='container'>
          <h2>正在进行的合作项目</h2>
          <Row gutter={[16, 16]}>
            {ongoingProjects.map((project, index) => (
              <Col xs={24} lg={8} key={index}>
                <Card
                  title={project.title}
                  extra={<Tag color='processing'>进行中</Tag>}
                  className='project-card'
                >
                  <div className='project-info'>
                    <p>
                      <strong>合作方:</strong> {project.partners.join(' × ')}
                    </p>
                    <p>
                      <strong>项目负责人:</strong> {project.leader}
                    </p>
                    <p>
                      <strong>项目预算:</strong> {project.budget}
                    </p>
                    <p>
                      <strong>项目周期:</strong> {project.startDate} ~ {project.endDate}
                    </p>
                    <div className='progress-info'>
                      <p>
                        <strong>进度:</strong>
                      </p>
                      <Progress percent={project.progress} status='active' />
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* 成功案例 */}
      <section className='success-cases-section'>
        <div className='container'>
          <h2>成功案例</h2>
          <Timeline>
            {successCases.map((case_, index) => (
              <Timeline.Item
                key={index}
                dot={<TrophyOutlined style={{ fontSize: '16px', color: '#fa8c16' }} />}
              >
                <Card className='case-card'>
                  <div className='case-header'>
                    <h3>{case_.title}</h3>
                    <span className='case-date'>{case_.date}</span>
                  </div>
                  <p className='case-description'>{case_.description}</p>
                  <div className='case-details'>
                    <p>
                      <strong>合作伙伴:</strong> {case_.partners.join(' × ')}
                    </p>
                    <p>
                      <strong>主要成就:</strong> {case_.achievement}
                    </p>
                  </div>
                </Card>
              </Timeline.Item>
            ))}
          </Timeline>
        </div>
      </section>

      {/* 合作申请模态框 */}
      <Modal
        title='申请合作'
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label='机构名称'
                name='organization'
                rules={[{ required: true, message: '请输入机构名称' }]}
              >
                <Input placeholder='请输入您的机构名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label='机构类型'
                name='type'
                rules={[{ required: true, message: '请选择机构类型' }]}
              >
                <Select placeholder='请选择机构类型'>
                  <Option value='university'>高等院校</Option>
                  <Option value='enterprise'>科技企业</Option>
                  <Option value='institute'>科研院所</Option>
                  <Option value='government'>政府机构</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label='联系人'
                name='contact'
                rules={[{ required: true, message: '请输入联系人姓名' }]}
              >
                <Input placeholder='请输入联系人姓名' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label='联系电话'
                name='phone'
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder='请输入联系电话' />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label='合作领域'
            name='field'
            rules={[{ required: true, message: '请选择合作领域' }]}
          >
            <Select mode='multiple' placeholder='请选择合作领域'>
              <Option value='ai'>人工智能</Option>
              <Option value='biotech'>生物医学</Option>
              <Option value='materials'>新材料</Option>
              <Option value='energy'>新能源</Option>
              <Option value='environment'>环境工程</Option>
              <Option value='manufacturing'>智能制造</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label='合作需求描述'
            name='description'
            rules={[{ required: true, message: '请描述您的合作需求' }]}
          >
            <Input.TextArea rows={4} placeholder='请详细描述您的合作需求、预期目标等' />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CollaborationPage;
