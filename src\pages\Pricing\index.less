.pricing-page {
  background: #f0f2f5;

  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    color: white;
    text-align: center;
    padding: 100px 0 80px;
    position: relative;

    .hero-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;

      h1 {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 24px;
        color: white;

        @media (max-width: 768px) {
          font-size: 36px;
        }
      }

      p {
        font-size: 20px;
        line-height: 1.6;
        opacity: 0.9;
        margin-bottom: 48px;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }

      .billing-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;

        span {
          font-size: 16px;
          font-weight: 600;
          opacity: 0.7;
          transition: all 0.3s ease;

          &.active {
            opacity: 1;
            font-weight: 600;
          }
        }
      }
    }
  }

  // Pricing Section
  .pricing-section {
    padding: 80px 0;
    background: white;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .ant-tabs {
      .ant-tabs-nav {
        margin-bottom: 48px;

        .ant-tabs-tab {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }

    .pricing-grid {
      .pricing-card {
        height: 100%;
        border-radius: 16px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }

        &.popular {
          border-color: #1890ff;
          transform: scale(1.05);
          z-index: 2;

          &:hover {
            transform: scale(1.05) translateY(-4px);
          }

          .popular-badge {
            position: absolute;
            top: -1px;
            right: -1px;
            background: linear-gradient(45deg, #1890ff, #722ed1);
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            border-radius: 0 16px 0 16px;
            z-index: 3;

            .anticon {
              margin-right: 4px;
            }
          }
        }

        .plan-header {
          text-align: center;
          margin-bottom: 32px;

          h3 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .plan-description {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
          }
        }

        .plan-price {
          text-align: center;
          margin-bottom: 24px;

          .price-amount {
            display: flex;
            align-items: baseline;
            justify-content: center;
            margin-bottom: 8px;

            .currency {
              font-size: 24px;
              font-weight: 600;
              color: #1f2937;
              margin-right: 4px;
            }

            .amount {
              font-size: 48px;
              font-weight: 700;
              color: #1f2937;
              line-height: 1;
            }
          }

          .price-unit {
            color: #6b7280;
            font-size: 14px;
          }
        }

        .plan-features {
          margin-bottom: 32px;

          .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 14px;
            color: #374151;
            line-height: 1.4;

            &:last-child {
              margin-bottom: 0;
            }

            .anticon {
              margin-top: 2px;
              flex-shrink: 0;
            }
          }
        }

        .plan-action {
          .ant-btn {
            height: 48px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
          }
        }
      }
    }
  }

  // FAQ Section
  .faq-section {
    padding: 80px 0;
    background: #f9fafb;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .section-header {
      text-align: center;
      margin-bottom: 64px;

      h2 {
        font-size: 36px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .ant-list-item {
      border: none;
      padding: 16px 0;

      .ant-list-item-meta-title {
        margin-bottom: 8px;

        .ant-typography {
          color: #1f2937;
          font-size: 18px;
          font-weight: 600;
          margin: 0;
        }
      }

      .ant-list-item-meta-description {
        .ant-typography {
          color: #6b7280;
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }
  }

  // CTA Section
  .cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .cta-content {
      h2 {
        font-size: 36px;
        font-weight: 600;
        color: white;
        margin-bottom: 16px;
      }

      p {
        font-size: 18px;
        line-height: 1.6;
        opacity: 0.9;
        margin-bottom: 32px;
      }

      .cta-buttons {
        .ant-btn {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
          font-weight: 600;
          border-radius: 8px;

          &[data-type='primary'] {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;

            &:hover {
              background: rgba(255, 255, 255, 0.3);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }

          &:not([data-type='primary']) {
            background: transparent;
            border-color: rgba(255, 255, 255, 0.3);
            color: white;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 992px) {
    .hero-section {
      padding: 80px 0 60px;
    }

    .pricing-section,
    .faq-section,
    .cta-section {
      padding: 60px 0;
    }

    .pricing-section .pricing-grid .pricing-card.popular {
      transform: none;

      &:hover {
        transform: translateY(-4px);
      }
    }

    .faq-section .section-header {
      margin-bottom: 40px;

      h2 {
        font-size: 28px;
      }
    }
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 60px 0 40px;

      .hero-content .billing-toggle {
        flex-direction: column;
        gap: 12px;

        span {
          font-size: 14px;
        }
      }
    }

    .pricing-section,
    .faq-section,
    .cta-section {
      padding: 40px 0;
    }

    .pricing-section {
      .ant-tabs .ant-tabs-nav {
        margin-bottom: 32px;

        .ant-tabs-tab {
          font-size: 16px;
        }
      }

      .pricing-grid .pricing-card {
        .plan-price .price-amount .amount {
          font-size: 36px;
        }
      }
    }

    .faq-section .section-header {
      margin-bottom: 32px;

      h2 {
        font-size: 24px;
      }
    }

    .cta-section .cta-content {
      h2 {
        font-size: 28px;
      }

      .cta-buttons {
        .ant-btn {
          display: block;
          width: 100%;
          margin: 0 0 16px 0 !important;

          &:last-child {
            margin-bottom: 0 !important;
          }
        }
      }
    }
  }
}
