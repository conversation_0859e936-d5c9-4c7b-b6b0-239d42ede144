import React from 'react';
import ReactECharts from 'echarts-for-react';

interface GaugeChartProps {
    value: number;
    title: string;
  }

class GaugeChart extends React.Component<GaugeChartProps> {
  getOption = () => {
    const { value, title } = this.props;
    return {
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
            //   width: 30,
              color: [
                [0.3, '#67e0e3'],
                [0.7, '#9fe6b8'],
                [1, '#ff6fa0']
              ]
            }
          },
        //   pointer: {
        //     icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        //     length: '12%',
        //     width: 20
        //   },
          axisTick: {
            length: 12,
            lineStyle: {
              color: 'auto',
              width: 2
            }
          },
        //   splitLine: {
        //     length: 20,
        //     lineStyle: {
        //       color: 'auto',
        //       width: 5
        //     }
        //   },
          axisLabel: {
            color: '#464646',
            distance: -60,
            fontSize: 20
          },
          title: {
            offsetCenter: [0, '-60%'],
            fontSize: 30
          },
          detail: {
            offsetCenter: [0, '0%'],
            valueAnimation: true,
            fontSize: 40,
            color: 'auto'
          },
          data: [
            {
              value: value,
              name: title
            }
          ]
        }
      ]
    };
  };

  render() {
    return <ReactECharts option={this.getOption()} style={{ width: '100%', height: '100%' }} />;
  }
}

export default GaugeChart;