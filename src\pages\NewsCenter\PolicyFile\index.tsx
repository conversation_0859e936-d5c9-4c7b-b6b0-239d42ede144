import CTASection from '@/pages/Home/components/CTASection';
import React from 'react';
import './index.less';

const PolicyFileList = [
  {
    id: 1,
    title: '加大政策支持 推动平台经济健康发展',
    content:
      '发展平台经济事关扩内需、稳就业、惠民生，事关赋能实体经济、发展新质生产力。22日召开的国务院常务会议明确提出，要进一步加强对平台经济健康发展的统筹指导，加大政策支持力度。',
    date: '2025-10-01',
  },
  {
    id: 2,
    title: '国务院常务会议解读 | 我国部署深入实施“人工智能+”行动',
    content: '31日召开的国务院常务会议，审议通过《关于深入实施“人工智能+”行动的意见》。',
    date: '2025-10-06',
  },
  {
    id: 3,
    title:
      '科技部等六部门关于印发《关于加快场景创新以人工智能高水平应用促进经济高质量发展的指导意见》的通知',
    content:
      '为落实《新一代人工智能发展规划》，系统指导各地方和各主体加快人工智能场景应用，推动经济高质量发展，现将《关于加快场景创新 以人工智能高水平应用促进经济高质量发展的指导意见》印发给你们，请结合实际，认真贯彻落实。',
    date: '2025-08-18',
  },
  {
    id: 4,
    title: '从“能用”到“好用”：AI激活医疗创新动能',
    content:
      '在人工智能（AI）的辅助下，贵州山区的患者得到北京专家的接诊，抗肿瘤药物的研发周期大幅缩短，急诊室的抢救时间以分钟级的速度优化……当前，AI通过对医疗数据进行分析处理，可以辅助医生进行医疗决策、提升诊疗效率、改善服务质量，推动医疗行业创新发展。',
    date: '2025-07-29',
  },
  {
    id: 5,
    title: '工业和信息化部等七部门关于印发《医药工业数智化转型实施方案（2025—2030年）》的通知',
    content:
      '各省、自治区、直辖市及计划单列市、新疆生产建设兵团工业和信息化、商务、卫生健康、医保、数据、中医药、药监主管部门，现将《医药工业数智化转型实施方案（2025—2030年）》印发给你们，请结合实际，抓好贯彻落实。',
    date: '2025-07-07',
  },
  {
    id: 6,
    title: '国家卫生健康委员会办公厅关于印发卫生健康行业人工智能应用场景参考指引的通知',
    content:
      '为贯彻落实党中央、国务院关于开展“人工智能+”行动的决策部署，国家卫生健康委、国家中医药局、国家疾控局研究制定了《卫生健康行业人工智能应用场景参考指引》，现印发给你们，请参照执行，积极推进卫生健康行业“人工智能+”应用创新发展。',
    date: '2025-06-10',
  },
  {
    id: 7,
    title: '人工智能安全治理框架',
    content:
      '9月9日，在2024年国家网络安全宣传周主论坛上，全国网络安全标准化技术委员会（以下简称“网安标委”）发布《人工智能安全治理框架》1.0版。',
    date: '2025-06-05',
  },
  {
    id: 8,
    title:
      '工业和信息化部办公厅关于开展数字化赋能、科技成果赋智、质量标准品牌赋值中小企业全国行活动的通知',
    content:
      '为深入贯彻党中央、国务院关于支持中小企业创新发展的决策部署，落实《中小企业数字化赋能专项行动方案》《科技成果赋智中小企业专项行动（2023-2025年）》《质量标准品牌赋值中小企业专项行动（2023-2025年）》，加快中小企业数字化转型步伐，提高科技成果转化和产业化水平，推动中小企业向价值链中高端迈进，现组织开展数字化赋能、科技成果赋智、质量标准品牌赋值中小企业全国行活动（以下称"三赋"全国行）。',
    date: '2025-06-02',
  },
  {
    id: 9,
    title: '国家人工智能产业综合标准化体系建设指南 (2024 版)',
    content:
      '为深入贯彻落实党中央、国务院决策部署，加强人工智能标准化工作系统谋划，工业和信息化部、中央网络安全和信息化委员会办公室、国家发展和改革委员会、国家标准化管理委员会组织编制了《国家人工智能产业综合标准化体系建设指南（2024版）》。现印发给你们，请结合实际，抓好贯彻落实。',
    date: '2025-05-14',
  },
  {
    id: 10,
    title:
      '科技部等六部门关于印发《关于加快场景创新以人工智能高水平应用促进经济高质量发展的指导意见》的通知',
    content:
      '为落实《新一代人工智能发展规划》，系统指导各地方和各主体加快人工智能场景应用，推动经济高质量发展，现将《关于加快场景创新 以人工智能高水平应用促进经济高质量发展的指导意见》印发给你们，请结合实际，认真贯彻落实。',
    date: '2025-03-23',
  },
];

const PolicyFile: React.FC = () => {
  return (
    <div className='policy-file'>
      <div className='policy-file-container'>
        <div className='policy-file-list'>
          {PolicyFileList.map(item => (
            <div key={item.id} className='policy-file-item'>
              <div className='policy-file-item-date'>
                <h3>{item.date.substring(8)}</h3>
                <p>{item.date.substring(0, 7)}</p>
              </div>
              <div className='policy-file-item-content'>
                <h4>{item.title}</h4>
                <p>{item.content}</p>
              </div>
              <div className='policy-file-item-all-arrow'> </div>
            </div>
          ))}
        </div>
      </div>

      <CTASection />
    </div>
  );
};

export default PolicyFile;
