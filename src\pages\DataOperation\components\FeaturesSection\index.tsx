import React from 'react';
import './index.less';
import type {
  CustomSwiperProps,
  MaskConfig,
  SlideData,
  SlideSizeConfig,
} from '@/components/CustomSwiper';
import CustomSwiper from '@/components/CustomSwiper';

const features = [
  {
    id: 1,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 2,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 3,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 4,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 5,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 6,
    title: '分布式技术与链码相结合',
    description:
      '布式技术与链码结合使用，优化了数据处理效率，并增强了数据的可信度和安全性，两者结合可构建更高效、透明的数据管理体系',
    image: '/platform/images/home/<USER>',
  },
];

const FeaturesSection: React.FC = () => {
  // 将features数据转换为SlideData格式
  const slides: SlideData[] = features.map(feature => ({
    id: feature.id,
    content: (
      <div className='features-card'>
        <div className='features-image'>
          <img src={feature.image} alt={feature.title} className='background-image' />
        </div>
        <div className='card-content'>
          {/* <div className='card-header'>
            <img src='/platform/images/home/<USER>' alt='展开' className='expand-icon' />
          </div> */}
          <h3>{feature.title}</h3>
          <p className='feature-description'>{feature.description}</p>
        </div>
      </div>
    ),
  }));

  // 尺寸配置
  const slideSizeConfig: SlideSizeConfig = {
    normalWidth: '480px',
    activeWidth: '620px',
    normalHeight: '336px',
    activeHeight: '100%',
  };

  // 遮罩配置
  const maskConfig: MaskConfig = {
    enabled: true,
  };

  // Swiper配置 - 使用内置箭头和遮罩
  const swiperConfig: CustomSwiperProps = {
    slides,
    showArrows: true, // 恢复内置箭头
    arrowConfig: {
      leftIcon: '/platform/images/scientific-research/arrow-left.svg',
      rightIcon: '/platform/images/scientific-research/arrow-right.svg',
      leftPosition: '192px',
      rightPosition: '192px',
    },
    slideSizeConfig,
    maskConfig, // 添加遮罩配置
    swiperOptions: {
      spaceBetween: 32,
      slidesPerView: 3,
      centeredSlides: true,
      initialSlide: 1,
      loop: true,
    },
    onSlideChange: activeIndex => {
      console.log('Active slide index:', activeIndex);
    },
    className: 'feature-card-container',
  };

  return (
    <section className='features-section'>
      <div className='features-container'>
        <h2 className='section-title'>核心优势与能力</h2>
        <div className='features-content'>
          <div className='features-grid'>
            <CustomSwiper {...swiperConfig} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
