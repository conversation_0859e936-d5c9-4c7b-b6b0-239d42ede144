.product-page {
  background: #f0f2f5;

  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('/platform/images/hero-background.png') center/cover;
      opacity: 0.1;
      z-index: 0;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;

      h1 {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 24px;
        color: white;

        @media (max-width: 768px) {
          font-size: 36px;
        }
      }

      p {
        font-size: 20px;
        line-height: 1.6;
        opacity: 0.9;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }
    }
  }

  // Products Section
  .products-section {
    padding: 80px 0;
    background: white;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .section-header {
      text-align: center;
      margin-bottom: 48px;

      h2 {
        font-size: 36px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .product-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: none;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .ant-card-cover {
        padding: 32px 24px 16px;
        background: #fafafa;
      }

      .product-icon {
        text-align: center;
        padding: 16px 0;
      }

      .ant-card-body {
        padding: 24px;
        height: calc(100% - 120px);
        display: flex;
        flex-direction: column;
      }

      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }
      }

      .product-description {
        color: #6b7280;
        line-height: 1.5;
        margin-bottom: 16px;
        flex-grow: 1;
      }

      .product-features {
        margin-bottom: 16px;

        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;
          color: #374151;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .product-pricing {
        color: #1890ff;
        font-size: 16px;
        text-align: center;
      }

      .ant-card-actions {
        border-top: 1px solid #f0f0f0;
        background: #fafafa;

        li {
          margin: 8px 0;
        }
      }
    }
  }

  // Features Section
  .features-section {
    padding: 80px 0;
    background: #f9fafb;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .section-header {
      text-align: center;
      margin-bottom: 64px;

      h2 {
        font-size: 36px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: #6b7280;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .feature-item {
      text-align: center;
      padding: 32px 24px;

      .feature-icon {
        margin-bottom: 24px;
      }

      h3 {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: #6b7280;
        line-height: 1.6;
        margin: 0;
      }
    }
  }

  // CTA Section
  .cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    color: white;
    text-align: center;

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .cta-content {
      h2 {
        font-size: 36px;
        font-weight: 600;
        color: white;
        margin-bottom: 16px;
      }

      p {
        font-size: 18px;
        line-height: 1.6;
        opacity: 0.9;
        margin: 0;
      }
    }
  }

  // Responsive Design
  @media (max-width: 992px) {
    .hero-section {
      padding: 80px 0 60px;
    }

    .products-section,
    .features-section,
    .cta-section {
      padding: 60px 0;
    }

    .section-header h2 {
      font-size: 28px;
    }
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 60px 0 40px;
    }

    .products-section,
    .features-section,
    .cta-section {
      padding: 40px 0;
    }

    .section-header {
      margin-bottom: 32px;

      h2 {
        font-size: 24px;
      }
    }

    .features-section .section-header {
      margin-bottom: 40px;
    }

    .product-card .ant-card-body {
      padding: 16px;
    }

    .feature-item {
      padding: 24px 16px;

      h3 {
        font-size: 20px;
      }
    }

    .cta-content h2 {
      font-size: 28px;
    }
  }
}
