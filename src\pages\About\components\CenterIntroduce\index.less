.about-center-introduce {
  display: flex;
  height: 100vh;
  padding: 80px 0;
  background: var(---, #fff);
  position: relative;

  .about-center-introduce-bg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 394px;
    background: rgba(52, 121, 251, 0.2);

    .about-center-introduce-bg-container {
      position: relative;
      width: 100%;
      height: 100%;
      .about-center-introduce-bg-row {
        position: relative;
        z-index: 4;
        display: flex;
        width: 1153px;
        height: 170px;
        padding-left: 340px;
        padding-right: 50px;
        justify-content: space-between;
        align-items: center;
        border-radius: 2px;
        background: linear-gradient(0deg, rgba(25, 102, 255, 0.8) 0%, rgba(25, 102, 255, 0.8) 100%);

        .about-center-introduce-bg-row-title {
          color: var(---, #fff);

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
          text-align: center;
        }

        .about-center-introduce-bg-row-statistic .ant-statistic-content {
          text-align: center;
        }
        .about-center-introduce-bg-row-statistic .ant-statistic-content-value,
        .about-center-introduce-bg-row-statistic .ant-statistic-content-suffix {
          color: var(---, #fff);
          text-align: center;
          font-family: 'Alibaba PuHuiTi';
          font-size: 36px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 54px */
        }
      }
    }
  }

  .about-center-introduce-container {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    gap: 46px;
    position: relative;
    z-index: 2;

    .about-center-introduce-content {
      display: flex;
      padding: 60px 0 8px 0;
      flex-direction: column;
      gap: 36px;
      flex: 1 0 0;
      .about-center-introduce-title > h3 {
        overflow: hidden;
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-overflow: ellipsis;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
      .about-center-introduce-content-description > p {
        color: var(---65, rgba(0, 0, 0, 0.65));

        /* t2-段落 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 180%; /* 28.8px */
      }
    }

    .about-center-introduce-image {
      width: 582px;
      align-self: stretch;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
