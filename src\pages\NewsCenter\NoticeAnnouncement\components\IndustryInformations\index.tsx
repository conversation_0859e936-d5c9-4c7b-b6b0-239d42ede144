import React from 'react';
import './index.less';

const IndustryInformations: React.FC = () => {
  return (
    <div className='industry-informations'>
      <div className='industry-informations-container'>
        <div className='industry-informations-header'>
          <h1>行业资讯</h1>
        </div>
        <div className='industry-informations-content'>
          <div className='industry-informations-main-item'>
            <div>
              <img
                src='/platform/images/news-center/industry-informations-1.png'
                alt='行业资讯主图'
              />
            </div>
          </div>
          <div className='industry-informations-item'>
            <h3>百度何明科：大模型为医疗健康行业带来了哪些新可能？</h3>
            <div className='industry-informations-item-content'>
              <p>
                百度集团资深副总裁、大健康事业群组总裁何明科受邀出席并发表题为《大模型，“道生医”》的主题演讲。
              </p>
            </div>
            <div className='industry-informations-item-bottom'>
              <div className='industry-informations-item-bottom-date'>
                <p className='industry-informations-item-bottom-date-month'>2025-05</p>
                <p className='industry-informations-item-bottom-date-day'>08</p>
              </div>
              <div className='industry-informations-item-bottom-arrow'>
                <img src='/platform/images/news-center/industry-informations-right.svg' alt='' />
              </div>
            </div>
          </div>
          <div className='industry-informations-item'>
            <h3>人工智能在医疗诊断领域取得重大新突破</h3>
            <div className='industry-informations-item-content'>
              <p>
                在科技飞速发展的今天，人工智能正以前所未有的速度渗透到医疗领域，尤其是在诊断环节，一系列重大突破正重塑着我们对疾病认知与治疗的边界。
              </p>
            </div>
            <div className='industry-informations-item-bottom'>
              <div className='industry-informations-item-bottom-date'>
                <p className='industry-informations-item-bottom-date-month'>2025-05</p>
                <p className='industry-informations-item-bottom-date-day'>11</p>
              </div>
              <div className='industry-informations-item-bottom-arrow'>
                <img src='/platform/images/news-center/industry-informations-right.svg' alt='' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndustryInformations;
