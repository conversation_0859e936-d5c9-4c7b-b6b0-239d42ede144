.solution {
  padding: 52px 0;
  width: 100%;
  background: url('/platform/images/data-operation/solution-bg.png') no-repeat center center;
  background-size: cover;
  .solution-container {
    width: 1240px;
    margin: 0 auto;
    .solution-title {
      color: var(---85, rgba(0, 0, 0, 0.85));
      font-family: 'Alibaba PuHuiTi';
      font-size: 35.495px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 53.242px */
      text-align: center;
    }
    .solution-content {
      margin-top: 32px;
      display: flex;
      gap: 24px;
      height: 346px;
      > div {
        border-radius: 20px;
        border: 1px solid #fff;
        background: rgba(255, 255, 255, 0.6);

        /* 卡片投影 */
        box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
      }

      .solution-content-left {
        padding: 32px 48px;
        width: 513px;
        > h3 {
          color: var(---85, rgba(0, 0, 0, 0.85));
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 36px */
        }
        .solution-content-left-items {
          margin-top: 16px;
          display: grid;
          gap: 20px;
          grid-template-columns: repeat(2, 1fr);

          .solution-content-left-item {
            height: 62px;
            border-radius: 8px;
            background: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-left: 20px;
            cursor: pointer;

            /* 卡片投影 */
            box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
            .solution-content-left-item-img {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 6px;
              background: #d9e6ff;
            }
            > p {
              color: var(---65, rgba(0, 0, 0, 0.65));
              font-family: 'Alibaba PuHuiTi';
              font-size: 20px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 30px */
            }
          }
        }
      }
      .solution-content-right {
        flex: 1;
        padding: 41px 81px;
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(4, 1fr);
        .solution-content-right-item {
          display: flex;
          width: 120px;
          height: 120px;
          padding: 10px 0;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 6px;
          border-radius: 20px;
          border: 1px solid #fff;
          background: #fff;

          /* 卡片投影 */
          box-shadow: 0 10px 20px 0 rgba(134, 156, 199, 0.12);
          .solution-content-right-item-img {
            width: 65px;
            height: 62px;
          }
          > p {
            color: #000;
            text-align: center;
            font-family: 'Alibaba PuHuiTi';
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 30px */
          }
        }
        .info-none > p {
          color: var(---45, rgba(0, 0, 0, 0.45));
        }
      }
    }
  }
}
