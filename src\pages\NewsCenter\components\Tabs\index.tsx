import { SearchOutlined } from '@ant-design/icons';
import { Input } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { useLocation, useNavigate } from 'react-router-dom';

const tabs = [
  {
    path: '/platform/news-center/notice-announcement',
    title: '通知公告',
  },
  {
    path: '/platform/news-center/policy-file',
    title: '政策文件',
  },
  {
    path: '/platform/news-center/industry-information',
    title: '行业资讯',
    disabled: true,
  },
];

const Tabs: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const tabsRef = useRef<(HTMLParagraphElement | null)[]>([]);
  const [underlineStyle, setUnderlineStyle] = useState<React.CSSProperties>({});

  // 更新下划线位置
  const updateUnderlinePosition = React.useCallback(() => {
    const activeIndex = tabs.findIndex(tab => tab.path === location.pathname);
    if (activeIndex !== -1 && tabsRef.current[activeIndex]) {
      const activeTab = tabsRef.current[activeIndex];
      if (activeTab) {
        const { offsetLeft, offsetWidth } = activeTab;
        setUnderlineStyle({
          left: offsetLeft,
          width: offsetWidth,
        });
      }
    }
  }, [location.pathname]);

  useEffect(() => {
    updateUnderlinePosition();
  }, [updateUnderlinePosition]);

  return (
    <div className='news-center-tabs'>
      <div className='news-center-tabs-container'>
        <div className='news-center-tabs-left'>
          {tabs.map((tab, index) => (
            <p
              key={tab.path}
              ref={el => {
                tabsRef.current[index] = el;
              }}
              className={location.pathname === tab.path ? 'active' : ''}
              onClick={() => navigate(tab.path)}
              style={{
                cursor: !tab.disabled ? 'pointer' : 'not-allowed',
                opacity: tab.disabled ? 0.5 : 1,
              }}
            >
              {tab.title}
            </p>
          ))}
          <div className='animated-underline' style={underlineStyle} />
        </div>
        <div className='news-center-tabs-right'>
          <Input className='' placeholder='搜索...' suffix={<SearchOutlined />} />
        </div>
      </div>
    </div>
  );
};

export default Tabs;
