.service-content {
  width: 100%;
  padding: 52px 0;
  .service-content-container {
    width: 1240px;
    margin: 0 auto;

    .service-content-title > h2 {
      color: var(---85, rgba(0, 0, 0, 0.85));
      text-align: center;
      font-family: 'Alibaba PuHuiTi';
      font-size: 35.495px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 53.242px */
    }
    .service-content-content {
      width: 100%;
      background-color: #fff;
      margin-top: 119px;
      display: flex;
      gap: 24px;

      .service-content-item {
        border-radius: 12px;
        border: 1px solid #fff;
        background: #fff;
        flex: 1;
        border: 1px solid #fff;
        background: url(/platform/images/data-operation/service-content-item-bg.png) lightgray 50% /
          cover no-repeat;
        height: 323px;
        background-color: linear-gradient(180deg, #d9d9d9 0%, rgba(115, 115, 115, 0) 100%);
        padding: 180px 38px 0 38px;
        position: relative;
        > h4 {
          color: var(---85, rgba(0, 0, 0, 0.85));
          font-family: 'Alibaba PuHuiTi';
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 30px */
        }
        > p {
          margin-top: 4px;
          color: var(---45, rgba(0, 0, 0, 0.45));
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
        }
        >img{
            position: absolute;
            width: 188px;
            height: 188px;
            left: 11px;
            top: -68px;
        }
      }
    }
  }
}
