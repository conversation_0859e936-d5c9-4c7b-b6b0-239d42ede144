@import '../../styles/variables.less';

.security-dashboard {
  width: 100%;
  height: 100vh;
  position: relative;
  background: url(/platform/images/security-dashboard/background-main.png) no-repeat center center;

  .security-dashboard-content {
    width: 1247.783px;
    height: calc(100vh - 140px);
    flex-shrink: 0;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .security-dashboard-date-info {
      margin: 0 auto;
      display: flex;
      width: 928px;
      height: 152px;
      background: linear-gradient(90deg,
          rgba(0, 114, 244, 0) 0%,
          rgba(0, 114, 244, 0.3) 51.54%,
          rgba(0, 114, 244, 0) 100%);

      .security-dashboard-date-info-left {
        width: 40px;
        height: 152px;
      }

      .security-dashboard-date-info-center {
        flex: 1;
        height: 152px;
        padding: 20px 0;
        display: flex;
        background: linear-gradient(270deg,
            rgba(0, 119, 255, 0) 0%,
            rgba(0, 119, 255, 0.4) 50.66%,
            rgba(0, 119, 255, 0) 100%);
        box-shadow: 0 0 18px 0 rgba(0, 21, 48, 0.55);

        .security-dashboard-date-info-center-date-picker {
          width: 50px;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-around;

          >p {
            cursor: pointer;
            width: 40px;
            color: #fff;
            text-align: center;
            font-family: 'Source Han Sans SC';
            font-size: 18px;
            font-style: normal;
            font-weight: 350;
            line-height: normal;
          }

          .active {
            background: linear-gradient(270deg,
                rgba(0, 119, 255, 0) 0%,
                rgba(0, 119, 255, 0.4) 50.66%,
                rgba(0, 119, 255, 0) 100%);
            box-shadow: 0 0 18px 0 rgba(0, 21, 48, 0.55);
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: -28px;
              transform: translateY(-50%);
              width: 12px;
              height: 16px;
              background: url(/platform/images/security-dashboard/date-picker-active.svg) no-repeat center center;
            }
          }
        }

        .security-dashboard-date-info-center-content {
          width: 100%;
          padding: 8px 72px 16px;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 80px;
          background: linear-gradient(270deg,
              rgba(0, 119, 255, 0) 0%,
              rgba(0, 119, 255, 0.4) 50.66%,
              rgba(0, 119, 255, 0) 100%);
          box-shadow: 0 0 18px 0 rgba(0, 21, 48, 0.55);

          .security-dashboard-date-info-center-content-item {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: start;

            .security-dashboard-date-info-center-content-item-left {
              width: 56.392px;
              height: 58px;
            }

            .security-dashboard-date-info-center-content-item-right {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: start;
              justify-content: space-around;

              .content-item-right-number {
                color: #d9f3ff;
                font-family: 'Alibaba PuHuiTi 3.0';
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 1.4px;

                >span {
                  color: #d9f3ff;
                  font-family: Roboto;
                  font-size: 44px;
                  font-style: italic;
                  font-weight: 400;
                  line-height: normal;
                  letter-spacing: 2.2px;
                  margin-right: 8px;
                }
              }

              .content-item-right-title {
                color: #fff;
                font-family: 'Source Han Sans SC';
                font-size: 18px;
                font-style: normal;
                font-weight: 350;
                line-height: normal;
                margin-top: 6px;
              }
            }
          }
        }
      }

      .security-dashboard-date-info-right {
        width: 40px;
        height: 152px;
      }
    }

    .security-dashboard-content-main {
      width: 1236.302px;
      height: calc(100% - 152px);
      fill:
        radial-gradient(10.65% 39.99% at 102.56% 29.65%,
          rgba(235, 254, 255, 0.3) 23.74%,
          rgba(233, 254, 255, 0) 54.19%),
        radial-gradient(24.63% 39.99% at 102.56% 29.65%,
          rgba(0, 240, 255, 0.3) 30.49%,
          rgba(0, 240, 255, 0) 54.19%),
        radial-gradient(62.94% 38.31% at 103.54% 34.14%,
          rgba(57, 185, 255, 0.81) 30.49%,
          rgba(57, 185, 255, 0) 100%),
        rgba(34, 95, 253, 0.2);
      stroke-width: 2px;
      stroke: rgba(34, 95, 253, 0.22);

      >img {
        width: 100%;
        height: 100%;
      }
    }

    // 让左侧数据栏覆盖在中间区域左侧
    position: relative;

    .DashBoardSideLeft {
      position: absolute;
      left: -305px; 
      top: 0px;
      width: 440px;
      height: 100%;
      border-radius: 4px;
      background: linear-gradient(180deg, rgba(0, 50, 89, 0.40) 0%, rgba(0, 63, 111, 0.26) 100%);
      backdrop-filter: blur(50px);
      pointer-events: auto;
    }
    .DashBoardSideRight {
      position: absolute;
      right: -305px; 
      top: 0px; 
    width: 440px;
      height: 100%;
      border-radius: 4px;
      background: linear-gradient(180deg, rgba(0, 50, 89, 0.40) 0%, rgba(0, 63, 111, 0.26) 100%);
      backdrop-filter: blur(50px);
      pointer-events: auto;
    }
  }

  // 底部不再单独渲染左侧数据栏，这里保留通用样式即可
}

// 新增动画效果
@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes consoleGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }

  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes radarSweep {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes threatPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

@keyframes domeRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes energyFlow {

  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }

  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

@keyframes nodePulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes flowMove {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 20;
  }
}

@keyframes particleMove {

  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@keyframes statusBlink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes panelFloat {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes progressPulse {

  0%,
  100% {
    opacity: 0.8;
  }

  50% {
    opacity: 1;
  }
}

// 右侧面板
.right-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;

  .panel-card {
    background: linear-gradient(135deg,
        rgba(0, 30, 60, 0.4) 0%,
        rgba(0, 50, 100, 0.3) 50%,
        rgba(0, 20, 40, 0.4) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.8), transparent);
    }

    &:hover {
      border-color: rgba(0, 212, 255, 0.4);
      box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .card-icon {
        font-size: 16px;
        color: #00d4ff;
      }

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
        flex: 1;
        margin-left: 12px;
      }

      .card-controls {
        .control-btn {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          cursor: pointer;
          transition: color 0.3s ease;

          &:hover {
            color: #00d4ff;
          }
        }
      }
    }

    // 安全运营合规样式
    &.compliance-panel {
      .compliance-score {
        display: flex;
        align-items: center;
        gap: 20px;

        .score-circle {
          position: relative;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: radial-gradient(circle,
              rgba(0, 212, 255, 0.2) 0%,
              rgba(0, 212, 255, 0.05) 70%,
              transparent 100%);
          border: 3px solid rgba(0, 212, 255, 0.3);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .score-value {
            font-size: 28px;
            font-weight: 700;
            color: #00d4ff;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
          }

          .score-unit {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: -5px;
          }

          .score-label {
            position: absolute;
            bottom: -25px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            white-space: nowrap;
          }
        }

        .score-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;

          .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);

            .detail-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }

            .detail-value {
              font-size: 16px;
              font-weight: 600;
              color: #ffffff;
              font-family: 'Courier New', monospace;
            }

            .detail-unit {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.5);
              margin-left: 4px;
            }
          }
        }
      }
    }

    // 应用订阅趋势样式
    &.trend-panel {
      .trend-chart {
        position: relative;

        .chart-svg {
          width: 100%;
          height: 120px;
          border-radius: 8px;
          background: rgba(0, 0, 0, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chart-labels {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          padding: 0 10px;

          .label {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }
    }

    // 热门应用TOP样式
    &.top-apps-panel {
      .top-apps-list {
        .app-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);

          &:last-child {
            border-bottom: none;
          }

          .app-rank {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .rank-badge {
              padding: 2px 8px;
              background: linear-gradient(135deg, #00d4ff, #5b8fff);
              border-radius: 12px;
              font-size: 10px;
              font-weight: 600;
              color: #ffffff;
            }

            .app-name {
              font-size: 12px;
              color: #ffffff;
              font-weight: 500;
            }
          }

          .app-usage {
            display: flex;
            align-items: center;
            gap: 2px;

            .usage-value {
              font-size: 14px;
              font-weight: 600;
              color: #00ff9b;
              font-family: 'Courier New', monospace;
            }

            .usage-unit {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }

    // 背景装饰元素
    .background-decorations {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 3;

      .grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
        background-size: 50px 50px;
        animation: gridMove 20s linear infinite;
      }

      .floating-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .particle {
          position: absolute;
          width: 3px;
          height: 3px;
          background: #00d4ff;
          border-radius: 50%;
          opacity: 0.6;
          animation: floatParticle 8s ease-in-out infinite;

          &:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
            background: #00d4ff;
          }

          &:nth-child(2) {
            top: 40%;
            left: 80%;
            animation-delay: 1.3s;
            background: #00ff9b;
          }

          &:nth-child(3) {
            top: 70%;
            left: 20%;
            animation-delay: 2.6s;
            background: #ffbb55;
          }

          &:nth-child(4) {
            top: 30%;
            left: 60%;
            animation-delay: 3.9s;
            background: #ff6b9d;
          }

          &:nth-child(5) {
            top: 80%;
            left: 70%;
            animation-delay: 5.2s;
            background: #00d4ff;
          }

          &:nth-child(6) {
            top: 15%;
            left: 40%;
            animation-delay: 6.5s;
            background: #00ff9b;
          }
        }
      }

      .corner-decorations {
        .corner-lines {
          position: absolute;
          width: 60px;
          height: 60px;
          border: 2px solid rgba(0, 212, 255, 0.3);

          &.top-left {
            top: 20px;
            left: 20px;
            border-right: none;
            border-bottom: none;
            border-radius: 10px 0 0 0;
          }

          &.top-right {
            top: 20px;
            right: 20px;
            border-left: none;
            border-bottom: none;
            border-radius: 0 10px 0 0;
          }

          &.bottom-left {
            bottom: 20px;
            left: 20px;
            border-right: none;
            border-top: none;
            border-radius: 0 0 0 10px;
          }

          &.bottom-right {
            bottom: 20px;
            right: 20px;
            border-left: none;
            border-top: none;
            border-radius: 0 0 10px 0;
          }
        }
      }
    }

    // 动画定义
    @keyframes lineFlow {

      0%,
      100% {
        opacity: 0.3;
        transform: scaleX(0.8);
      }

      50% {
        opacity: 1;
        transform: scaleX(1);
      }
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 0.6;
        transform: scale(1);
      }

      50% {
        opacity: 1;
        transform: scale(1.05);
      }
    }

    @keyframes platformPulse {

      0%,
      100% {
        opacity: 0.3;
        transform: scale(1);
      }

      50% {
        opacity: 0.6;
        transform: scale(1.1);
      }
    }

    @keyframes sphereRotate {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes ringRotate {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes serverBlink {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.3;
      }
    }

    @keyframes dataFlow {

      0%,
      100% {
        opacity: 0.4;
        transform: scale(0.8);
      }

      50% {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    @keyframes screenFlicker {

      0%,
      100% {
        opacity: 0.8;
      }

      50% {
        opacity: 1;
      }
    }

    @keyframes dataFlowLine {
      0% {
        stroke-dashoffset: 0;
      }

      100% {
        stroke-dashoffset: 24;
      }
    }

    @keyframes particleMove {
      0% {
        opacity: 0;
        transform: scale(0);
      }

      10% {
        opacity: 1;
        transform: scale(1);
      }

      90% {
        opacity: 1;
        transform: scale(1) translateX(100px);
      }

      100% {
        opacity: 0;
        transform: scale(0) translateX(150px);
      }
    }

    @keyframes gridMove {
      0% {
        transform: translate(0, 0);
      }

      100% {
        transform: translate(50px, 50px);
      }
    }

    @keyframes floatParticle {

      0%,
      100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
      }

      25% {
        transform: translateY(-15px) translateX(5px);
        opacity: 0.8;
      }

      50% {
        transform: translateY(-25px) translateX(-5px);
        opacity: 1;
      }

      75% {
        transform: translateY(-15px) translateX(5px);
        opacity: 0.8;
      }
    }

    // 响应式设计
    @media (max-width: 1600px) {
      .security-dashboard {
        .core-metrics {
          gap: 60px;

          .metric-item {
            padding: 16px 24px;

            .metric-icon {
              width: 40px;
              height: 40px;
              font-size: 20px;
            }

            .metric-content {
              .metric-value {
                font-size: 28px;
              }
            }
          }
        }

        .dashboard-main {
          gap: 30px;

          .left-panel,
          .right-panel {
            width: 320px;
          }

          .center-visualization {
            .security-command-center {
              transform: scale(0.9);
            }
          }
        }
      }
    }

    @media (max-width: 1200px) {
      .security-dashboard {
        .dashboard-header {
          height: 100px;
          padding: 0 30px;

          .platform-title-container {
            .platform-title {
              font-size: 28px;
              gap: 12px;

              .title-separator {
                font-size: 24px;
              }
            }

            .title-subtitle {
              font-size: 12px;
              gap: 8px;

              .subtitle-version {
                padding: 2px 8px;
                font-size: 10px;
              }
            }

            .title-status-indicators {
              gap: 16px;

              .status-indicator {
                font-size: 10px;

                .indicator-dot {
                  width: 6px;
                  height: 6px;
                }
              }
            }
          }

          .header-info {
            .current-time {
              .time-value {
                font-size: 14px;
              }
            }
          }
        }

        .core-metrics {
          gap: 40px;
          margin: 0 30px;

          .metric-item {
            padding: 12px 20px;

            .metric-content {
              .metric-value {
                font-size: 24px;
              }

              .metric-label {
                font-size: 12px;
              }
            }
          }
        }

        .dashboard-main {
          padding: 0 30px;
          gap: 20px;

          .left-panel,
          .right-panel {
            width: 280px;
          }

          .center-visualization {
            .security-command-center {
              transform: scale(0.8);
            }
          }
        }
      }
    }

    @media (max-width: 768px) {
      .security-dashboard {
        .dashboard-header {
          height: auto;
          min-height: 120px;
          padding: 20px;
          flex-direction: column;
          gap: 15px;
          justify-content: center;

          .platform-title-container {
            .title-decorative-elements {
              max-width: 300px;
              gap: 12px;

              .title-icon {
                font-size: 18px;
              }
            }

            .platform-title {
              font-size: 24px;
              gap: 8px;
              flex-direction: column;
              text-align: center;

              .title-separator {
                display: none;
              }
            }

            .title-subtitle {
              font-size: 11px;
              gap: 6px;
              flex-direction: column;

              .subtitle-version {
                padding: 2px 6px;
                font-size: 9px;
              }
            }

            .title-status-indicators {
              gap: 12px;
              flex-wrap: wrap;
              justify-content: center;

              .status-indicator {
                font-size: 9px;

                .indicator-dot {
                  width: 5px;
                  height: 5px;
                }
              }
            }
          }

          .header-info {
            position: static;
            transform: none;
            align-items: center;

            .system-status {
              display: none;
            }

            .current-time {
              padding: 4px 12px;

              .time-value {
                font-size: 12px;
              }
            }
          }
        }

        .core-metrics {
          flex-direction: column;
          gap: 15px;
          margin: 0 20px;

          .metric-item {
            padding: 10px 16px;

            .metric-icon {
              width: 35px;
              height: 35px;
              font-size: 16px;
            }

            .metric-content {
              .metric-value {
                font-size: 20px;
              }

              .metric-label {
                font-size: 11px;
              }
            }
          }
        }

        .dashboard-main {
          flex-direction: column;
          height: auto;
          padding: 0 20px;
          gap: 15px;

          .left-panel,
          .right-panel {
            width: 100%;

            .panel-card {
              padding: 15px;

              .card-header {
                margin-bottom: 15px;

                .card-title {
                  font-size: 14px;
                }
              }
            }
          }

          .center-visualization {
            height: 300px;
            order: -1;

            .security-command-center {
              transform: scale(0.6);
            }
          }
        }
      }
    }
  }

  // 新增动画效果
  @keyframes iconPulse {

    0%,
    100% {
      transform: scale(1);
      filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
    }

    50% {
      transform: scale(1.1);
      filter: drop-shadow(0 0 20px rgba(0, 212, 255, 1));
    }
  }

  @keyframes separatorGlow {
    0% {
      text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }

    100% {
      text-shadow:
        0 0 20px rgba(0, 212, 255, 0.8),
        0 0 30px rgba(0, 212, 255, 0.4);
    }
  }

  @keyframes statusPulse {

    0%,
    100% {
      transform: scale(1);
      box-shadow: 0 0 10px rgba(0, 255, 155, 0.6);
    }

    50% {
      transform: scale(1.2);
      box-shadow: 0 0 15px rgba(0, 255, 155, 0.8);
    }
  }

  @keyframes statusRipple {
    0% {
      transform: scale(1);
      opacity: 1;
    }

    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
}