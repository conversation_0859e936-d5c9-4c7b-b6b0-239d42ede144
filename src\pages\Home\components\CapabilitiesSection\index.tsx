import React from 'react';
import { useNavigate } from 'react-router-dom';
import './index.less';

const CapabilitiesSection: React.FC = () => {
  const navigate = useNavigate();

  // 路由映射
  const routeMap: { [key: string]: string } = {
    算力商城: '/platform/computing-mall',
    模型商城: '/platform/model-mall',
    应用商城: '/platform/app-mall',
    数据空间: '/platform/data-management',
    产学研赋能: '/platform/scientific-research',
    安全运营: '/platform/security-ops',
  };

  // 处理卡片点击
  const handleCardClick = (title: string) => {
    const route = routeMap[title];
    if (route) {
      // 产学研赋能在新标签页打开
      if (title === '产学研赋能') {
        window.open(route, '_blank');
      } else {
        // 其他页面在当前页面跳转
        navigate(route, { replace: false });
        // 使用 requestAnimationFrame 确保在下一帧重置滚动条
        requestAnimationFrame(() => {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        });
      }
    }
  };

  const capabilities = [
    {
      id: 1,
      title: '算力商城',
      description: '弹性调度、资源高效利用',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 2,
      title: '模型商城',
      description: '统一模型训练、部署与评估',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 3,
      title: '应用商城',
      description: '快速接入，灵活集成',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 4,
      title: '数据空间',
      description: '分布式存储与共享交换',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 5,
      title: '产学研赋能',
      description: '项目管理与成果孵化',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 6,
      title: '安全运营',
      description: '全链路安全守护',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
  ];

  return (
    <section className='capabilities-section'>
      <div className='capabilities-container'>
        <h2 className='section-title'>平台核心能力</h2>
        <div className='capabilities-grid'>
          <div className='capabilities-row'>
            {capabilities.slice(0, 3).map(capability => (
              <div
                key={capability.id}
                className='capability-card'
                onClick={() => handleCardClick(capability.title)}
                style={{ cursor: 'pointer' }}
              >
                <div className='card-background'>
                  <div className='glow-effect'>
                    <div className='glow-circle glow-1'></div>
                    <div className='glow-circle glow-2'></div>
                  </div>
                  <div className='card-header'>
                    <h3 className='capability-title'>{capability.title}</h3>
                    <img src={capability.expandIcon} alt='展开' className='expand-icon' />
                  </div>
                  <p className='capability-description'>{capability.description}</p>
                  <div className='capability-icon'>
                    <img src={capability.icon} alt={capability.title} />
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className='capabilities-row'>
            {capabilities.slice(3, 6).map(capability => (
              <div
                key={capability.id}
                className='capability-card'
                onClick={() => handleCardClick(capability.title)}
                style={{ cursor: 'pointer' }}
              >
                <div className='card-background'>
                  <div className='glow-effect'>
                    <div className='glow-circle glow-1'></div>
                    <div className='glow-circle glow-2'></div>
                  </div>
                  <div className='card-header'>
                    <h3 className='capability-title'>{capability.title}</h3>
                    <img src={capability.expandIcon} alt='展开' className='expand-icon' />
                  </div>
                  <p className='capability-description'>{capability.description}</p>
                  <div className='capability-icon'>
                    <img src={capability.icon} alt={capability.title} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CapabilitiesSection;
