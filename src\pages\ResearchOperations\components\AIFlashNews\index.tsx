import React from 'react';
import './index.less';

const tabs = [
  {
    id: 1,
    title: '金融',
    img: '/platform/images/research-operations/ai-flash-news-1.png',
  },
  {
    id: 2,
    title: '政务',
    img: '/platform/images/research-operations/ai-flash-news-2.png',
  },
  {
    id: 3,
    title: '医疗',
    img: '/platform/images/research-operations/ai-flash-news-3.png',
  },
  {
    id: 4,
    title: '新能源',
    img: '/platform/images/research-operations/ai-flash-news-4.png',
  },
  {
    id: 5,
    title: '人工智能',
    img: '/platform/images/research-operations/ai-flash-news-5.png',
  },
  {
    id: 6,
    title: '生物技术',
    img: '/platform/images/research-operations/ai-flash-news-6.png',
  },
  {
    id: 7,
    title: '数据模型',
    img: '/platform/images/research-operations/ai-flash-news-7.png',
  },
];

const AIFlashNews: React.FC = () => {
  // 处理新闻点击事件
  const handleNewsClick = () => {
    window.open('https://mp.weixin.qq.com/s/LFEzpmF135lwymXKR-yjIg', '_blank');
  };
  return (
    <div className='ai-flash-news-page'>
      <div className='ai-flash-news-container'>
        <div className='ai-flash-news-title'>
          <h2>AI快讯</h2>
          <p>
            查看更多
            <img src='/platform/images/research-operations/find-more.svg' alt='find-more' />
          </p>
        </div>
        <div className='ai-flash-news-content'>
          <div className='news-info-left'>
            {tabs.map(item => (
              <div className={`news-info-left-item ${item.id === 1 ? 'active' : ''}`} key={item.id}>
                <img src={item.img} alt='' />
                <p>{item.title}</p>
              </div>
            ))}
          </div>
          <div className='news-info-right'>
            <div
              className='news-info-right-item-large'
              onClick={handleNewsClick}
              style={{ cursor: 'pointer' }}
            >
              <div className='news-info-right-item-img'>
                <img src='/platform/images/research-operations/ai-flash-news-info-1.png' alt='' />
              </div>
              <div className='news-info-right-item-info'>
                <h3>WAIC2025世界人工智能大会</h3>
                <p className='item-desc'>
                  在即将到来的世界人工智能大会 （WAIC2025） 上，汇智灵曦 数字科技有限公司 （AETHER
                  MIND） 将携其最新的医疗智能 化解决方案亮相大会。
                </p>
                <p className='item-date'>2025-7-26</p>
              </div>
            </div>
            <div className='news-info-right-item'>
              <div className='news-info-right-item-date-img'>
                <p className='date-day'>27</p>
                <p className='date-other'>2025-07</p>
              </div>
              <div className='news-info-right-item-info'>
                <h4>训练推理一体化平台，破解大模型医疗落地难题</h4>
                <p className='item-desc'>
                  在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等核心痛点。针对这一现状，汇智灵曦创新推出训练推理一体化平台，致力于打通从数据到应用的全链路，推动大模型在医疗行业的深...
                </p>
                <p className='item-date'>2025-7-26</p>
              </div>
            </div>
            <div className='news-info-right-item'>
              <div className='news-info-right-item-date-img'>
                <p className='date-day'>27</p>
                <p className='date-other'>2025-07</p>
              </div>
              <div className='news-info-right-item-info'>
                <h4>训练推理一体化平台，破解大模型医疗落地难题</h4>
                <p className='item-desc'>
                  在高度专业化、培养周期漫长的医疗领域，通用大模型常面临专业性不足、语义理解偏差、实际部署障碍等核心痛点。针对这一现状，汇智灵曦创新推出训练推理一体化平台，致力于打通从数据到应用的全链路，推动大模型在医疗行业的深...
                </p>
                <p className='item-date'>2025-7-26</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIFlashNews;
