.user-manual-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px 20px;

  .manual-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .manual-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    .ant-typography {
      font-size: 16px;
      color: #666;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  .manual-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .manual-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    .ant-card-head {
      border-bottom: 2px solid #f0f0f0;
      
      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;
      }
    }
  }

  .feature-list {
    .feature-item {
      margin-bottom: 16px;
      
      h4 {
        color: #1890ff;
        margin-bottom: 8px;
      }
      
      .ant-typography {
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .faq-list {
    .faq-item {
      margin-bottom: 24px;
      
      h5 {
        color: #1890ff;
        margin-bottom: 8px;
      }
      
      .ant-typography {
        color: #666;
        line-height: 1.6;
        margin-left: 16px;
      }
    }
  }

  .ant-steps-vertical {
    .ant-steps-item-title {
      font-weight: 600;
      color: #1890ff;
    }
    
    .ant-steps-item-description {
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-manual-page {
    padding: 20px 10px;
    
    .manual-container {
      max-width: 100%;
    }
    
    .manual-header {
      h1 {
        font-size: 24px;
      }
      
      .ant-typography {
        font-size: 14px;
      }
    }
    
    .manual-card {
      margin: 0 -10px;
      border-radius: 8px;
    }
  }
}
