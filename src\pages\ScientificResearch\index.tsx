import React from 'react';
import HeroSection from './components/HeroSection';
import TaskScenariosSection from './components/TaskScenariosSection';
import AIServicesSection from './components/AIServicesSection';
import ProductAdvantagesSection from './components/ProductAdvantagesSection';
import TechnicalSupportSection from './components/TechnicalSupportSection';
import NewsSection from './components/NewsSection';
import './index.less';
import CTASection from '../Home/components/CTASection';
// import TrainingCourse from './components/TrainingCourse';

const ScientificResearchPage: React.FC = () => {
  return (
    <div className='scientificResearch'>
      <HeroSection />
      <AIServicesSection />
      {/* <TrainingCourse/> */}
      {/* <TaskScenario/> */}
      <TaskScenariosSection />
      <ProductAdvantagesSection />
      <TechnicalSupportSection />
      <NewsSection />
      <CTASection />
    </div>
  );
};

export default ScientificResearchPage;
