.select-plan {
  width: 100%;
  padding: 52px 0;
  background: var(---fill-7, #f5f7fa);

  .select-plan-container {
    width: 1240px;
    margin: 0 auto;

    .select-plan-title {
      width: 490px;
      margin: 0 auto;
      > h2 {
        text-align: center;
        width: 100%;
        color: var(---85, rgba(0, 0, 0, 0.85));
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
      .select-plan-title-desc {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        > p {
          color: var(---85, rgba(0, 0, 0, 0.85));

          /* t2 */
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 150%; /* 24px */
        }
        .find-all {
          color: var(---Brand1-5, #37f);
          > img:nth-child(1) {
            opacity: 0.3;
          }
          > img:nth-child(2) {
            opacity: 0.6;
          }
        }
      }
    }
    .select-plan-tabs {
      margin-top: 32px;

      .select-plan-month {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        .select-plan-month-item {
          border-radius: 8px;
          background: #fff;
          padding: 40px;
          display: flex;
          flex-direction: column;
          gap: 24px;

          .select-plan-month-item-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            > h4 {
              color: var(---85, rgba(0, 0, 0, 0.85));
              font-family: 'Alibaba PuHuiTi';
              font-size: 20px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%; /* 30px */
            }
          }
          .select-plan-month-item-price {
            display: flex;
            align-items: center;
            > p {
              color: var(---85, rgba(0, 0, 0, 0.85));
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
              opacity: 0.6;
            }
            > span {
              margin-right: 4px;
              color: var(---Brand1-5, #37f);
              font-family: Poppins;
              font-size: 32px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%; /* 48px */
            }
          }
          .select-plan-month-item-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            padding: 12px 16px;
            border-radius: 8px;
            background: var(---fill-7, #f5f7fa);
            gap: 12px;
            > p {
              overflow: hidden;
              color: var(---45, rgba(0, 0, 0, 0.45));
              text-overflow: ellipsis;

              /* t3 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 21px */
              > span {
                color: var(---85, rgba(0, 0, 0, 0.85));
                text-overflow: ellipsis;
                margin-right: 12px;

                /* t1 */
                font-family: 'Alibaba PuHuiTi';
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: 150%; /* 24px */
              }
            }
          }
        }
      }
    }
    .select-plan-tabs-bar {
      width: 100%;
      height: 48px;
      display: flex;
      justify-content: center;
      position: relative;
      margin-bottom: 32px;
      &::before {
        border-bottom: 4px solid rgba(51, 119, 255, 0.1);
      }
      .ant-tabs-tab {
        height: 48px;
        padding: 0 52px;
        color: var(---45, rgba(0, 0, 0, 0.45));

        /* h1 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 36px */
      }
      .ant-tabs-tab-active .ant-tabs-tab-btn {
        color: var(---85, rgba(0, 0, 0, 0.85));
      }
      .ant-tabs-ink-bar {
        z-index: 5;
        height: 4px;
        background-color: var(---Brand1-5, #37f);
      }
      .select-plan-tabs-bar-item {
        display: flex;
        padding: 0 52px;
        align-items: flex-start;
        justify-content: center;
        color: var(---85, rgba(0, 0, 0, 0.85));

        /* h1 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 36px */
      }
    }
  }
}
