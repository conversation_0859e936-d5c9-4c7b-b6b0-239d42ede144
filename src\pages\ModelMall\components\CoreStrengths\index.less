.core-strengths {
  width: 100%;
  padding: 52px 0;
  background: var(---fill-7, #f5f7fa);

  .core-strengths-container {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 32px;

    .core-strengths-title {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      > h2 {
        color: var(---85, rgba(0, 0, 0, 0.85));
        text-align: center;
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 48px */
      }
      > p {
        color: var(---85, rgba(0, 0, 0, 0.85));

        /* t2 */
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 24px */
        opacity: 0.8;
      }
    }

    .core-strengths-content {
      width: 100%;
      height: 440px;
      display: flex;
      gap: 60px;

      .core-strengths-content-left {
        display: flex;
        width: 433px;
        padding: 40px 0 32px 0;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        align-self: stretch;

        .content-left-title {
          > h3 {
            color: var(---85, rgba(0, 0, 0, 0.85));

            /* h1 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: 150%; /* 36px */
            > span {
              background: var(---logo, linear-gradient(0deg, #2560fd 0%, #03adfe 100%));
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-right: 10px;
            }
          }
          > p {
            margin-top: 12px;
            color: var(---45, rgba(0, 0, 0, 0.45));

            /* t2 */
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 150%; /* 24px */
          }
        }
        .content-left-desc {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 32px;
          align-self: stretch;
          .content-left-desc-item {
            display: flex;
            gap: 10px;
            height: 24px;
            align-items: center;
            > img {
              width: 16px;
              height: 16px;
            }
            > p {
              color: var(---85, rgba(0, 0, 0, 0.85));

              /* t1 */
              font-family: 'Alibaba PuHuiTi';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 150%; /* 24px */
            }
          }
        }
        .content-left-progress {
          display: flex;
          width: 200px;
          align-items: center;
          height: 3px;
          background: rgba(51, 119, 255, 0.1);
          > div {
            display: flex;
            width: 40px;
            height: 3px;
            cursor: pointer;
          }
          .active {
            background: var(---Brand1-5, #37f);
          }
        }
      }
      .core-strengths-content-right {
        flex: 1;
        background: var(---fill-7, #f5f7fa);
        height: 100%;
        position: relative;
        > img {
          border-radius: 22px;
          overflow: hidden;
          width: 100%;
          height: 100%;
        }
        &::after {
          content: '';
          display: block;
          width: 23px;
          height: 100%;
          background: var(---fill-7, #f5f7fa);
          position: absolute;
          right: -2px;
          top: 0;
        }
        .top-mask {
          width: 20px;
          height: 20px;
          top: 0;
          right: 20px;
          position: absolute;
          background: var(---fill-7, #f5f7fa);
        }
        .bottom-mask {
          width: 20px;
          height: 20px;
          bottom: 0;
          right: 20px;
          position: absolute;
          background: var(---fill-7, #f5f7fa);
        }
      }
    }
  }
}
