.scientificResearch {
  min-height: 100vh;
  background: #ffffff;
  font-family:
    'Alibaba PuHuiTi',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Robot<PERSON>',
    'Oxygen',
    'Ubuntu',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  * {
    box-sizing: border-box;
  }

  /* 响应式设计 */
  @media (max-width: 1920px) {
    .sectionContainer {
      max-width: 100%;
    }
  }

  @media (max-width: 1440px) {
    .heroSection,
    .taskScenariosSection,
    .aiServicesSection,
    .productAdvantagesSection {
      padding: 40px 20px;
    }
  }

  @media (max-width: 1024px) {
    .heroCards {
      flex-direction: column;
      gap: 20px;
    }

    .scenariosGrid {
      grid-template-columns: repeat(2, 1fr);
    }

    .featuresGrid {
      grid-template-columns: repeat(2, 1fr);
    }

    .advantagesGrid {
      flex-direction: column;
      gap: 20px;
    }

    .newsGrid {
      flex-direction: column;
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    .heroTitle {
      font-size: 28px;
    }

    .heroSubtitle {
      font-size: 16px;
    }

    .sectionTitle {
      font-size: 24px;
    }

    .scenariosGrid {
      grid-template-columns: 1fr;
    }

    .featuresGrid {
      grid-template-columns: 1fr;
    }

    .servicesList {
      flex-direction: column;
      gap: 16px;
    }

    .heroActions {
      flex-direction: column;
      gap: 16px;
    }

    .actionBtn {
      width: 100%;
      max-width: 280px;
    }
  }

  /* 动画效果 */
  .heroCard,
  .scenarioCard,
  .featureCard,
  .advantageCard,
  .actionBtn,
  .newsItem {
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0px 12px 24px 0px rgba(134, 156, 199, 0.2);
    }
  }

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  /* 加载动画 */
  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 渐变背景 */
  .gradientBg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* 毛玻璃效果 */
  .glassEffect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* 阴影效果 */
  .shadowSm {
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .shadowMd {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.15);
  }

  .shadowLg {
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  }

  /* 按钮样式 */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .btnPrimary {
    background: #3377ff;
    color: #ffffff;

    &:hover {
      background: #2560fd;
    }
  }

  .btnSecondary {
    background: #ffffff;
    color: #3377ff;
    border: 1px solid #3377ff;

    &:hover {
      background: #f0f5ff;
    }
  }

  /* 文本样式 */
  .textPrimary {
    color: #3377ff;
  }

  .textSecondary {
    color: rgba(0, 0, 0, 0.65);
  }

  .textMuted {
    color: rgba(0, 0, 0, 0.45);
  }

  /* 间距工具类 */
  .mt1 {
    margin-top: 8px;
  }
  .mt2 {
    margin-top: 16px;
  }
  .mt3 {
    margin-top: 24px;
  }
  .mt4 {
    margin-top: 32px;
  }
  .mt5 {
    margin-top: 40px;
  }

  .mb1 {
    margin-bottom: 8px;
  }
  .mb2 {
    margin-bottom: 16px;
  }
  .mb3 {
    margin-bottom: 24px;
  }
  .mb4 {
    margin-bottom: 32px;
  }
  .mb5 {
    margin-bottom: 40px;
  }

  .p1 {
    padding: 8px;
  }
  .p2 {
    padding: 16px;
  }
  .p3 {
    padding: 24px;
  }
  .p4 {
    padding: 32px;
  }
  .p5 {
    padding: 40px;
  }
}
