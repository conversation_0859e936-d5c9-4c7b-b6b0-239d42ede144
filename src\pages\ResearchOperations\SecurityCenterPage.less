.security-center-page {
  min-height: 100vh;
  background: #f5f7fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 页面头部
  .page-header {
    background: linear-gradient(135deg, #ff4d4f 0%, #1890ff 100%);
    color: white;
    padding: 80px 0;
    margin-top: 64px;

    .header-content {
      text-align: center;

      h1 {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 16px;
        color: white;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      p {
        font-size: 18px;
        opacity: 0.9;
        margin-bottom: 40px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }

      .header-actions {
        display: flex;
        gap: 16px;
        justify-content: center;

        @media (max-width: 768px) {
          flex-direction: column;
          align-items: center;
        }

        .ant-btn {
          height: 48px;
          padding: 0 32px;
          font-size: 16px;
          border-radius: 8px;
        }
      }
    }
  }

  // 统计数据区域
  .stats-section {
    padding: 60px 0;
    background: white;

    .stat-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-weight: 600;
          font-size: 24px;
        }
      }
    }
  }

  // 主要内容区域
  .content-section {
    padding: 60px 0;
    background: #f8f9fa;

    .ant-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .ant-tabs {
      .ant-tabs-tab {
        font-size: 16px;
        font-weight: 500;
        padding: 12px 24px;

        .anticon {
          margin-right: 8px;
        }
      }

      .ant-tabs-content-holder {
        padding: 24px 0;
      }
    }

    // 标签页头部控制区域
    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .search-filters {
        display: flex;
        gap: 12px;

        @media (max-width: 768px) {
          flex-direction: column;
        }

        .ant-input-search,
        .ant-select {
          border-radius: 6px;
        }
      }
    }

    // 表格样式
    .ant-table-wrapper {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: auto;

      .ant-table {
        min-width: 800px; // 确保表格有最小宽度

        .ant-table-thead > tr > th {
          background: #fafafa;
          font-weight: 600;
          color: #262626;
          border-bottom: 1px solid #f0f0f0;
          white-space: nowrap;
          position: sticky;
          top: 0;
          z-index: 1;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }

        .ant-table-tbody > tr > td {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // 固定列样式
        .ant-table-cell-fix-left,
        .ant-table-cell-fix-right {
          background: white;

          &:before {
            background-color: transparent;
          }
        }

        .ant-table-cell-fix-left-last:after {
          border-right: 1px solid #f0f0f0;
        }

        .ant-table-cell-fix-right-first:after {
          border-left: 1px solid #f0f0f0;
        }
      }

      // 表格滚动条样式
      &::-webkit-scrollbar {
        height: 6px;
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    // 威胁等级分布
    .threat-levels {
      .threat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        span {
          font-weight: 500;
          color: #262626;
        }
      }
    }

    // 安全培训项目
    .training-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .training-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .training-title {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
        }
      }

      .training-deadline {
        margin-top: 8px;
        color: #8c8c8c;
        font-size: 12px;
      }
    }

    // 合规检查项
    .compliance-item {
      margin-bottom: 16px;

      .compliance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .compliance-name {
          font-size: 14px;
          color: #262626;
        }
      }
    }

    // 侧边栏卡片样式
    .timeline-card,
    .policy-card,
    .tools-card {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      margin-bottom: 24px;

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;

        .ant-card-head-title {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    // 安全动态时间线
    .ant-timeline {
      .ant-timeline-item {
        .ant-timeline-item-content {
          .timeline-content {
            p {
              margin-bottom: 4px;
              color: #262626;
              line-height: 1.5;
              font-size: 13px;
            }

            .timeline-time {
              color: #8c8c8c;
              font-size: 12px;
            }
          }
        }
      }
    }

    // 安全策略列表
    .policy-card {
      .ant-list-item {
        border: none;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ant-list-item-meta-title {
          font-size: 14px;
          margin-bottom: 4px;
        }
      }
    }

    // 安全工具按钮组
    .security-tools {
      .ant-btn {
        height: 40px;
        font-size: 14px;
        border-radius: 6px;

        &.ant-btn-primary {
          background: linear-gradient(135deg, #1890ff, #722ed1);
          border: none;
        }
      }
    }
  }

  // 警告框样式
  .ant-alert {
    border-radius: 8px;

    &.ant-alert-success {
      background: #f6ffed;
      border: 1px solid #b7eb8f;
    }
  }

  // 进度条样式
  .ant-progress {
    &.ant-progress-line {
      .ant-progress-bg {
        border-radius: 4px;
      }
    }

    &.ant-progress-circle {
      .ant-progress-text {
        font-weight: 600;
      }
    }
  }

  // 徽章样式
  .ant-badge {
    &.ant-badge-status {
      .ant-badge-status-text {
        font-size: 13px;
        color: #595959;
      }
    }
  }

  // 标签样式优化
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    border: none;
    font-weight: 500;

    &.ant-tag-blue {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.ant-tag-green {
      background: #f6ffed;
      color: #52c41a;
    }

    &.ant-tag-orange {
      background: #fff7e6;
      color: #fa8c16;
    }

    &.ant-tag-red {
      background: #fff2f0;
      color: #ff4d4f;
    }

    &.ant-tag-gray {
      background: #f5f5f5;
      color: #8c8c8c;
    }
  }

  // 按钮样式优化
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      }
    }

    &.ant-btn-link {
      padding: 0;
      height: auto;

      &:hover {
        background: none;
      }
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;

      .ant-modal-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .ant-form {
      .ant-form-item-label > label {
        font-weight: 500;
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        border-radius: 6px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .stats-section,
    .content-section {
      padding: 40px 0;
    }

    .ant-table-wrapper {
      .ant-table {
        font-size: 12px;
        min-width: 600px; // 移动端最小宽度

        .ant-table-thead > tr > th {
          padding: 8px 6px;
          font-size: 12px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 6px;
          font-size: 12px;
        }

        // 移动端隐藏一些非必要列
        .ant-table-column-has-actions.ant-table-column-has-filters {
          display: none;
        }
      }

      // 移动端滚动提示
      &::after {
        content: '← 向左滑动查看更多内容';
        position: absolute;
        bottom: 10px;
        right: 10px;
        font-size: 11px;
        color: #999;
        background: rgba(255, 255, 255, 0.9);
        padding: 2px 6px;
        border-radius: 3px;
        pointer-events: none;
      }
    }

    .ant-card {
      .ant-card-body {
        padding: 16px;
      }
    }

    .threat-levels {
      .threat-item {
        padding: 6px 0;

        span {
          font-size: 13px;
        }
      }
    }

    .training-item {
      padding: 12px 0;

      .training-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .training-title {
          font-size: 13px;
        }
      }
    }

    .compliance-item {
      .compliance-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;

        .compliance-name {
          font-size: 13px;
        }
      }
    }

    .timeline-card {
      .ant-timeline {
        .ant-timeline-item-content {
          .timeline-content {
            p {
              font-size: 12px;
            }
          }
        }
      }
    }

    .security-tools {
      .ant-btn {
        height: 36px;
        font-size: 13px;
      }
    }
  }
}
