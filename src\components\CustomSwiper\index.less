@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.custom-swiper-container {
  position: relative;
  width: 100%;
  
  // CSS变量默认值
  --slide-normal-width: 480px;
  --slide-active-width: 620px;
  --slide-normal-height: 336px;
  --slide-active-height: 100%;
  
  // 遮罩变量默认值
  --mask-gradient: linear-gradient(
    to right,
    transparent 0%,
    transparent 10%,
    black 30%,
    black 70%,
    transparent 90%,
    transparent 100%
  );
  --webkit-mask-gradient: linear-gradient(
    to right,
    transparent 0%,
    transparent 15%,
    black 30%,
    black 70%,
    transparent 85%,
    transparent 100%
  );

  .custom-swiper-content {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .custom-swiper-wrapper {
    width: 100%;
    height: 100%;
    
    // 当启用遮罩时应用遮罩效果
    &.with-mask {
      mask-image: var(--mask-gradient);
      -webkit-mask-image: var(--webkit-mask-gradient);
    }
  }

  .swiper {
    height: 100%;
  }

  .custom-swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    width: var(--slide-normal-width);
    height: var(--slide-normal-height);
    transition: all 0.3s ease;
  }

  // Swiper的slide状态样式
  .swiper-slide {
    width: var(--slide-normal-width) !important;
    height: var(--slide-normal-height) !important;
    transition: all 0.3s ease;
    
    // 确保slide内容居中显示
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .swiper-slide-active {
    width: var(--slide-active-width) !important;
    height: var(--slide-active-height) !important;
  }

  // 中心模式效果
  .swiper-slide-prev,
  .swiper-slide-next {
    opacity: 0.7;
    transform: scale(0.9);
  }

  .swiper-slide-active {
    opacity: 1;
    transform: scale(1);
    z-index: 2;
  }

  .custom-swiper-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: #ffffff;
    border: none;
    border-radius: 50%;
    box-shadow: 0px 10px 20px 0px rgba(134, 156, 199, 0.12);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;

    img {
      width: 24px;
      height: 24px;
    }

    &:hover {
      box-shadow: 0px 12px 24px 0px rgba(134, 156, 199, 0.2);
      transform: translateY(-50%) scale(1.05);
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }
  }

  .custom-swiper-arrow-left {
    left: 20px;
  }

  .custom-swiper-arrow-right {
    right: 20px;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .custom-swiper-container {
    .custom-swiper-arrow {
      width: 40px;
      height: 40px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .custom-swiper-arrow-left {
      left: 16px;
    }

    .custom-swiper-arrow-right {
      right: 16px;
    }
  }
}

@media (max-width: 768px) {
  .custom-swiper-container {
    .custom-swiper-arrow {
      width: 36px;
      height: 36px;

      img {
        width: 18px;
        height: 18px;
      }
    }

    .custom-swiper-arrow-left {
      left: 12px;
    }

    .custom-swiper-arrow-right {
      right: 12px;
    }
  }
}

@media (max-width: 480px) {
  .custom-swiper-container {
    .custom-swiper-arrow {
      width: 32px;
      height: 32px;

      img {
        width: 16px;
        height: 16px;
      }
    }

    .custom-swiper-arrow-left {
      left: 8px;
    }

    .custom-swiper-arrow-right {
      right: 8px;
    }
  }
}
