.become-partner {
  width: 100%;
  height: 220px;
  background: url('/platform/images/data-operation/become-partner-bg.png') no-repeat center center;
  background-size: cover;
  padding: 52px 0;

  .become-partner-container {
    width: 1240px;
    margin: 0 auto;
    .become-partner-title {
      color: var(---85, rgba(0, 0, 0, 0.85));
      text-align: center;
      font-family: 'Alibaba PuHuiTi';
      font-size: 32px;
      font-style: normal;
      font-weight: 500;
      line-height: 150%; /* 48px */
      text-align: center;
    }
    .become-partner-button {
      margin: 0 auto;
      margin-top: 20px;
      display: flex;
      height: 44px;
      padding: 8px 20px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: linear-gradient(270deg, #d987ff 0%, #3852ff 34.56%, #38f 100%);
      overflow: hidden;
      color: var(---, #fff);
      text-overflow: ellipsis;

      /* t1 */
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 150%; /* 24px */
      &:hover {
        color: var(---, #fff);
        background: linear-gradient(270deg, #d987ff 0%, #3852ff 34.56%, #38f 100%);
      }
    }
  }
}
