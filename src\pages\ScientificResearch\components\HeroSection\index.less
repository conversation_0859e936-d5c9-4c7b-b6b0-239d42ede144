.heroSectionScientificResearch {
  width: 100%;
  padding-top: 64px;
  position: relative;

  .heroSectionMask {
    position: absolute;
    width: 100%;
    top: -24px;
    flex-shrink: 0;
    aspect-ratio: 439/188;
    background: url('/platform/images/scientific-research/hero-bg.png') lightgray 50% / cover
      no-repeat;
  }

  .heroSectionMain {
    position: relative;
    height: 660px;
    overflow: hidden;
  }

  .heroBackground {
    position: absolute;
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background: linear-gradient(
      270deg,
      rgba(115, 115, 115, 0) 0%,
      #737373 10.58%,
      #d9d9d9 75.48%,
      rgba(217, 217, 217, 0) 100%
    );
  }

  .heroOverlay {
    width: 100%;
    height: 660px;
    flex-shrink: 0;
    aspect-ratio: 439/188;
    background: url('/platform/images/scientific-research/hero-background.png') lightgray 50% /
      cover no-repeat;
  }

  .heroContent {
    position: relative;
    z-index: 2;
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 40px 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .heroText {
    margin: 67px auto 0;
  }

  .heroTitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 40px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
    line-height: 1.5;
    text-align: center;
  }

  .heightText {
    color: var(---Brand1-5, #37f);
    font-family: 'Alibaba PuHuiTi';
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
  }

  .heroSubtitle {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 1.5;
    margin: 0;
    text-align: center;
  }

  .heroCards {
    height: 431px;
    position: relative;
  }

  .heroCard {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0px 4px 30px 0px rgba(5, 130, 197, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    h3 {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 18px;
      font-weight: 700;
      color: #3f98ff;
      text-align: center;
    }

    p {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
    }

    &.medicalSearch {
      position: absolute;
      top: 25px;
      left: 170px;
      width: 286px;
      height: 189px;
      flex-shrink: 0;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.4);
      box-shadow:
        0 4px 30px 0 rgba(5, 130, 197, 0.1),
        0 -4px 4px 0 rgba(249, 252, 255, 0.25) inset;
      padding: 18px 27px;

      h3 {
        margin: 0;
        margin-bottom: 5px;
      }
    }

    &.documentReading {
      position: absolute;
      top: 175px;
      left: 390px;
      width: 313px;
      height: 162px;
      flex-shrink: 0;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.4);
      box-shadow:
        0 4px 30px 0 rgba(5, 130, 197, 0.1),
        0 -4px 4px 0 rgba(249, 252, 255, 0.25) inset;
      padding: 18px 50px;
    }

    &.dataAnalysis {
      position: absolute;
      top: 52px;
      right: 288px;
      width: 313px;
      height: 225px;
      padding: 18px 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
  }

  .dataAnalysisTitle {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 56px;
    justify-content: space-around;

    h3 {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 18px;
      font-weight: 700;
      color: #3f98ff;
      text-align: center;
      margin: 0;
    }

    p {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      margin: 0;
    }
  }

  .cardImage {
    width: 100%;
    height: 148px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .chartPreview {
    width: 100%;
    height: 122px;
    border-radius: 12px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .uploadArea {
    width: 100%;
    height: 52px;
    border: 1px solid #ffffff;
    border-radius: 8px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
  }

  .uploadBox {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 24px;
      height: 24px;
    }
  }

  .heroActions {
    display: flex;
    gap: 38px;
    align-items: center;
    justify-content: center;
  }

  .actionBtn {
    width: 182px;
    height: 48px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 18px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 41px;
    border: 1px solid var(---logo, #2560fd);
    background: linear-gradient(180deg, #fff 0%, #ebf5fb 100%);
    box-shadow: 0 4px 4px 0 rgba(17, 62, 91, 0.18);
  }
}
