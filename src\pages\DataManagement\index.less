.data-management {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 104px 0;
    margin-bottom: 32px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .header-info {
        h1 {
          font-size: 36px;
          font-weight: 600;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 16px;

          .anticon {
            font-size: 40px;
          }
        }

        p {
          font-size: 18px;
          margin: 0;
          opacity: 0.9;
          line-height: 1.6;
        }
      }
    }

    @media (max-width: 768px) {
      padding: 40px 0;

      .header-info {
        h1 {
          font-size: 28px;
        }

        p {
          font-size: 16px;
        }
      }
    }
  }

  .data-stats {
    max-width: 1200px;
    margin: 0 auto 32px;
    padding: 0 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .data-features {
    max-width: 1200px;
    margin: 0 auto 32px;
    padding: 0 24px;

    .feature-card {
      text-align: center;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .feature-icon {
        padding: 32px 0 16px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }

      .ant-card-meta-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .ant-card-meta-description {
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .data-sources {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px 32px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  @media (max-width: 768px) {
    .data-stats,
    .data-features,
    .data-sources {
      padding: 0 16px 24px;
    }

    .data-features {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
