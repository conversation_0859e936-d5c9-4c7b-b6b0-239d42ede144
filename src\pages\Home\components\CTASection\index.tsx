import React, { useState } from 'react';
import { Button, Input } from 'antd';

import './index.less';

const CTASection: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleRegister = () => {
    if (!phoneNumber) {
      alert('请输入手机号');
      return;
    }
    // 处理注册逻辑
    console.log('注册手机号:', phoneNumber);
  };

  return (
    <section className='cta-section'>
      <div
        className='cta-background'
        style={{ backgroundImage: 'url(/platform/images/home/<USER>' }}
      >
        <div className='cta-container'>
          <div className='cta-content'>
            <h2 className='cta-title'>开启你的AI智能引擎！</h2>
            <p className='cta-subtitle'>打通模型、数据与算力，让AI真正落地每一个医疗场景。</p>
          </div>
          <div className='cta-form'>
            <div className='form-container'>
              <div className='input-container'>
                <Input
                  placeholder='请输入您的手机号'
                  value={phoneNumber}
                  onChange={e => setPhoneNumber(e.target.value)}
                  className='phone-input'
                />
              </div>
              <Button type='primary' onClick={handleRegister} className='register-button'>
                立即注册
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
