import React from 'react';
import './index.less';
import { Button, Image } from 'antd';

const HeroSection: React.FC = () => {
  const handleExperienceClick = () => {
    window.open(
      'http://www.aethermind.cn/frontend/smart-space/ai-model',
      '_blank',
      'noopener,noreferrer'
    );
  };

  return (
    <section className='model-mall-hero-section'>
      <div className='hero-bg'>
        <Image preview={false} src='/platform/images/model-mall/hero-bg.png' alt='hero-bg' />
      </div>
      <div className='hero-content'>
        <h1>大模型 · 一站搞定</h1>
        <p>一键部署，兼容主流框架，赋能模型全生命周期运营</p>
        <Button className='hero-btn' onClick={handleExperienceClick}>
          立即体验
        </Button>
      </div>
    </section>
  );
};

export default HeroSection;
