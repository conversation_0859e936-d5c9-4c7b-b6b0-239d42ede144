import React from 'react';
import './index.less';
import ReactECharts from 'echarts-for-react';
import GaugeChart from '../GaugeChart';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';

type ModelRankingItem = {
    name: string;
    calls: number;
    percentage: number;
    trend: number;
};

export interface DashboardLeftProps {
    metrics: {
        currentTasks: number;
        queueWaiting: number;
        cpuUsage: number;
        gpuUsage: number;
        memoryUsage: number;
        modelRanking: ModelRankingItem[];
    };
}

const DashboardLeft: React.FC<DashboardLeftProps> = ({ metrics }) => {
    const formatDailyCalls = (val: number) => {
        // 设计稿为 8,976 次（数据来源是 8.976 -> 千）
        const num = Math.round(val * 1000);
        return num.toLocaleString('zh-CN');
    };

    // 定义表格列配置
    const columns: ColumnsType<ModelRankingItem> = [
        {
            title: '模型名称',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: ModelRankingItem, index: number) => (
                <div className='table-name'>
                    {text}
                </div>
            )
        },
        {
            title: '响应',
            dataIndex: 'calls',
            key: 'calls',
            render: (value: number) => (
                <div className='table-content'>
                    <span className='table-num'>{value}</span>
                    <span className='table-unit'>ms</span>
                </div>
            )
        },
        {
            title: '错误率',
            dataIndex: 'percentage',
            key: 'percentage',
            render: (value: number) => (
                <div className='table-content'>
                    <span className='table-num'>{value} </span>
                    <span className='table-unit'>%</span>
                </div>

            )
        },
        {
            title: '日调用',
            dataIndex: 'trend',
            key: 'trend',
            render: (value: number) => (
                <div className='table-content'>
                    <span className='table-num'>{formatDailyCalls(value)} </span>
                    <span className='table-unit'>次</span>
                </div>

            )
        }
    ];

    // 转换数据格式，添加key字段
    const tableData = metrics.modelRanking.map((item, index) => ({
        ...item,
        key: `model-${index}`
    }));

    const option = {
        title: {
            text: 'ECharts 入门示例'
        },
        tooltip: {},
        xAxis: {
            data: ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"]
        },
        yAxis: {},
        series: [{
            name: '销量',
            type: 'bar',
            data: "32"
        }]
    };
    return (
        <div className='dashboard-left'>
            {/* 模型训练分析 */}
            <div className='panel-card training-analysis'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>模型训练分析</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content'>
                    <div className='metric-grid'>
                        <div className='metric-item'>
                            <img src='/platform/images/security-dashboard/title-group-icn.png' alt='' className='metric-img' />
                            <div>
                                <div className='metric-label'>训练中 </div>
                                <div>
                                    <span className='metric-value highlight'>{metrics.currentTasks}</span>
                                    <span className='metric-unit'>个</span>
                                </div>
                            </div>
                        </div>
                        <div className='metric-item'>
                            <img src='/platform/images/security-dashboard/title-group-icn.png' alt='' className='metric-img' />
                            <div>
                                <div className='metric-label'> 训练成功率</div>
                                <div>
                                    <span className='metric-value highlight'>{metrics.queueWaiting}</span>
                                    <span className='metric-unit'>%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='metric-grid'>
                        <div className='metric-item'>
                            <img src='/platform/images/security-dashboard/title-group-icn.png' alt='' className='metric-img' />
                            <div>
                                <div className='metric-label'>训练次数</div>
                                <div>
                                    <span className='metric-value highlight'>{metrics.currentTasks}</span>
                                    <span className='metric-unit'>次</span>
                                </div>
                            </div>
                        </div>
                        <div className='metric-item'>
                            <img src='/platform/images/security-dashboard/title-group-icn.png' alt='' className='metric-img' />
                            <div>
                                <div className='metric-label'> 均训练时长</div>
                                <div>
                                    <span className='metric-value highlight'>{metrics.queueWaiting}</span>
                                    <span className='metric-unit'>h</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* 算力资源分析 */}
            <div className='panel-card resource-analysis'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>算力资源分析</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content'>
                    <div className='resource-meters'>
                        <div className='meter-item'>
                            {/* <div style={{ display: 'flex', justifyContent: 'space-around'}}>
                                <GaugeChart value={73} title="CPU" />
                                <GaugeChart value={52} title="GPU" />
                                <GaugeChart value={82} title="储存" />
                            </div> */}
                            {/* <div className='meter-label'>CPU</div>
                            <div className='circular-meter'>
                                <div className='meter-value'>{Math.round(metrics.cpuUsage)}<span className='meter-unit'>%</span></div>
                                <svg className='meter-circle' viewBox='0 0 100 100'>
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='rgba(0, 212, 255, 0.2)' strokeWidth='8' />
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='#00d4ff' strokeWidth='8' strokeDasharray={`${Math.round(metrics.cpuUsage) * 2.83} 283`} strokeLinecap='round' transform='rotate(-90 50 50)' />
                                </svg>
                            </div>
                            <ReactECharts option={option} /> */}
                        </div>
                        <div className='meter-item'>
                            <div className='meter-label'>GPU</div>
                            <div className='circular-meter'>
                                <div className='meter-value'>{Math.round(metrics.gpuUsage)}<span className='meter-unit'>%</span></div>
                                <svg className='meter-circle' viewBox='0 0 100 100'>
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='rgba(0, 255, 155, 0.2)' strokeWidth='8' />
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='#00ff9b' strokeWidth='8' strokeDasharray={`${Math.round(metrics.gpuUsage) * 2.83} 283`} strokeLinecap='round' transform='rotate(-90 50 50)' />
                                </svg>
                            </div>
                        </div>
                        <div className='meter-item'>
                            <div className='meter-label'>存储</div>
                            <div className='circular-meter'>
                                <div className='meter-value'>{Math.round(metrics.memoryUsage)}<span className='meter-unit'>%</span></div>
                                <svg className='meter-circle' viewBox='0 0 100 100'>
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='rgba(255, 187, 85, 0.2)' strokeWidth='8' />
                                    <circle cx='50' cy='50' r='45' fill='none' stroke='#ffbb55' strokeWidth='8' strokeDasharray={`${Math.round(metrics.memoryUsage) * 2.83} 283`} strokeLinecap='round' transform='rotate(-90 50 50)' />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* 模型调用排行 */}
            <div className='panel-card model-ranking'>
                <div className='card-header'>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-left.png' />
                    </div>
                    <div className='card-title-background'>
                        <div className='card-title'>模型调用排行</div>
                    </div>
                    <div className='card-icon'>
                        <img src='/platform/images/security-dashboard/title-group-right.png' />
                    </div>
                </div>
                <div className='card-content'>
                    <Table
                        columns={columns}
                        dataSource={tableData}
                        pagination={false}
                        size="small"
                        style={{
                            background: 'transparent',
                            color: '#fff'
                        }}
                        className="model-ranking-table"
                    />
                </div>
            </div>
        </div>
    );
};

export default DashboardLeft;